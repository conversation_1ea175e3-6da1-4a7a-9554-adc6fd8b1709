# Core ML/DL Dependencies - CUDA 12.9 Compatible
torch>=2.4.0
torchvision>=0.19.0
torchaudio>=2.4.0
transformers>=4.45.0
accelerate>=0.34.0
datasets>=2.14.0

# Scientific Computing
numpy>=1.24.0,<2.0.0
scipy>=1.11.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Deep Learning Utilities
lightning>=2.1.0
torchmetrics>=1.2.0
timm>=1.0.0
einops>=0.8.0

# Optimization and Training
optuna>=3.6.0
ray[tune]>=2.30.0
wandb>=0.18.0
tensorboard>=2.17.0

# Configuration Management
hydra-core>=1.3.0
omegaconf>=2.3.0

# Data Processing
h5py>=3.9.0
zarr>=2.16.0
pyarrow>=14.0.0
fastparquet>=2023.10.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0
bokeh>=3.3.0

# Web Automation
nodriver>=0.37.0
selenium>=4.25.0
requests>=2.31.0
aiohttp>=3.10.0

# Utilities
tqdm>=4.66.0
rich>=13.7.0
click>=8.1.0
pydantic>=2.5.0
python-dotenv>=1.0.0

# Development Tools
pytest>=8.0.0
pytest-asyncio>=0.23.0
pytest-cov>=4.1.0
black>=24.0.0
isort>=5.13.0
flake8>=7.0.0
mypy>=1.7.0
pre-commit>=3.5.0

# Documentation
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# Performance Monitoring
psutil>=6.0.0
nvidia-ml-py>=12.535.0
gpustat>=1.1.0

# Async Support
aiofiles>=24.0.0

# Serialization
joblib>=1.4.0
cloudpickle>=3.1.0

# Networking
httpx>=0.25.0
websockets>=12.0

# System Integration
py-cpuinfo>=9.0.0

# Optional Distributed Training
# deepspeed>=0.15.0  # Commented out due to Windows build issues
fairscale>=0.4.13

# Optional Advanced Optimizers
# apex  # NVIDIA Apex for mixed precision
# lion-pytorch>=0.0.6  # Lion optimizer

# Additional ML Libraries
xgboost>=2.1.0
lightgbm>=4.5.0
catboost>=1.2.0

# Image Processing
pillow>=10.4.0
opencv-python>=4.10.0

# Audio Processing
librosa>=0.10.0
soundfile>=0.12.0

# Text Processing
nltk>=3.9.0
spacy>=3.8.0
tokenizers>=0.20.0
