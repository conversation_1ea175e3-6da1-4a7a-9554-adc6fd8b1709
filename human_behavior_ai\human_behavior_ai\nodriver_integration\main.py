"""
Main NoDriver integration interface.
"""

import asyncio
import logging
from typing import Dict, <PERSON>, Optional, Tuple, Union
import numpy as np
import torch

try:
    import nodriver as uc
except ImportError:
    uc = None
    logging.warning("NoDriver not installed. Install with: pip install nodriver")

from .behavior_engine import BehaviorEngine
from .stealth_controller import <PERSON>eal<PERSON><PERSON><PERSON>roll<PERSON>
from .adaptive_system import AdaptiveSystem
from ..models import HumanBehaviorModel

logger = logging.getLogger(__name__)


class NoDriverIntegration:
    """
    Main interface for integrating human behavior AI with NoDriver.
    
    This class provides seamless integration between the trained behavior models
    and NoDriver for realistic web automation.
    """
    
    def __init__(self, 
                 model: HumanBehaviorModel,
                 stealth_level: str = 'maximum',
                 adaptive_behavior: bool = True,
                 detection_evasion: bool = True):
        """
        Initialize NoDriver integration.
        
        Args:
            model: Trained human behavior model
            stealth_level: Level of stealth ('basic', 'advanced', 'maximum')
            adaptive_behavior: Whether to use adaptive behavior based on website
            detection_evasion: Whether to actively evade bot detection
        """
        if uc is None:
            raise ImportError("NoDriver is required. Install with: pip install nodriver")
        
        self.model = model
        self.stealth_level = stealth_level
        self.adaptive_behavior = adaptive_behavior
        self.detection_evasion = detection_evasion
        
        # Initialize components
        self.behavior_engine = BehaviorEngine(model)
        self.stealth_controller = StealthController(stealth_level)
        self.adaptive_system = AdaptiveSystem() if adaptive_behavior else None
        
        # Browser instance
        self.browser = None
        self.page = None
        
        # State tracking
        self.current_context = {}
        self.behavior_history = []
        self.detection_signals = []
        
        logger.info(f"NoDriver integration initialized with {stealth_level} stealth")
    
    async def start_browser(self, 
                          headless: bool = False,
                          user_data_dir: Optional[str] = None,
                          proxy: Optional[str] = None,
                          **kwargs) -> None:
        """Start the browser with stealth configuration."""
        try:
            # Configure browser options
            browser_config = self.stealth_controller.get_browser_config(
                headless=headless,
                user_data_dir=user_data_dir,
                proxy=proxy,
                **kwargs
            )
            
            # Start browser
            self.browser = await uc.start(**browser_config)
            self.page = self.browser.main_tab
            
            # Apply stealth modifications
            await self.stealth_controller.apply_stealth_modifications(self.page)
            
            logger.info("Browser started successfully with stealth configuration")
            
        except Exception as e:
            logger.error(f"Failed to start browser: {e}")
            raise
    
    async def navigate_to(self, url: str, wait_time: Optional[float] = None) -> None:
        """Navigate to URL with human-like behavior."""
        if not self.page:
            raise RuntimeError("Browser not started. Call start_browser() first.")
        
        try:
            # Update context for navigation
            self.current_context.update({
                'action': 'navigation',
                'url': url,
                'timestamp': asyncio.get_event_loop().time()
            })
            
            # Generate human-like navigation behavior
            if wait_time is None:
                wait_time = self.behavior_engine.generate_navigation_delay()
            
            await asyncio.sleep(wait_time)
            
            # Navigate
            await self.page.get(url)
            
            # Adaptive behavior based on website
            if self.adaptive_system:
                await self.adaptive_system.analyze_website(self.page)
                self.current_context.update(
                    await self.adaptive_system.get_website_context(self.page)
                )
            
            # Post-navigation behavior
            await self._post_navigation_behavior()
            
            logger.info(f"Navigated to {url}")
            
        except Exception as e:
            logger.error(f"Navigation failed: {e}")
            raise
    
    async def human_like_click(self, 
                             x: float, 
                             y: float,
                             button: str = 'left',
                             duration: Optional[float] = None) -> None:
        """Perform human-like click with realistic movement."""
        if not self.page:
            raise RuntimeError("Browser not started. Call start_browser() first.")
        
        try:
            # Generate human-like mouse movement
            current_pos = await self._get_current_mouse_position()
            movement_path = self.behavior_engine.generate_mouse_movement(
                start_pos=current_pos,
                end_pos=(x, y),
                context=self.current_context
            )
            
            # Execute movement
            for pos in movement_path:
                await self.page.mouse.move(pos[0], pos[1])
                await asyncio.sleep(0.001)  # Small delay between movements
            
            # Generate click timing
            if duration is None:
                duration = self.behavior_engine.generate_click_duration()
            
            # Perform click
            await self.page.mouse.down(button=button)
            await asyncio.sleep(duration)
            await self.page.mouse.up(button=button)
            
            # Update behavior history
            self._record_behavior('click', {
                'position': (x, y),
                'button': button,
                'duration': duration,
                'movement_path': movement_path
            })
            
            logger.debug(f"Human-like click at ({x}, {y})")
            
        except Exception as e:
            logger.error(f"Click failed: {e}")
            raise
    
    async def human_like_scroll(self, 
                              direction: str = 'down',
                              amount: int = 500,
                              smooth: bool = True) -> None:
        """Perform human-like scrolling."""
        if not self.page:
            raise RuntimeError("Browser not started. Call start_browser() first.")
        
        try:
            # Generate scrolling pattern
            scroll_pattern = self.behavior_engine.generate_scroll_pattern(
                direction=direction,
                amount=amount,
                smooth=smooth,
                context=self.current_context
            )
            
            # Execute scrolling
            for scroll_step in scroll_pattern:
                delta_x, delta_y, delay = scroll_step
                
                await self.page.mouse.wheel(delta_x=delta_x, delta_y=delta_y)
                await asyncio.sleep(delay)
            
            # Update behavior history
            self._record_behavior('scroll', {
                'direction': direction,
                'amount': amount,
                'pattern': scroll_pattern
            })
            
            logger.debug(f"Human-like scroll {direction} by {amount}px")
            
        except Exception as e:
            logger.error(f"Scroll failed: {e}")
            raise
    
    async def human_like_type(self, 
                            text: str,
                            element: Optional[object] = None,
                            typing_speed: Optional[float] = None) -> None:
        """Type text with human-like patterns."""
        if not self.page:
            raise RuntimeError("Browser not started. Call start_browser() first.")
        
        try:
            # Generate typing pattern
            typing_pattern = self.behavior_engine.generate_typing_pattern(
                text=text,
                typing_speed=typing_speed,
                context=self.current_context
            )
            
            # Focus element if provided
            if element:
                await element.click()
                await asyncio.sleep(0.1)
            
            # Execute typing
            for char, delay in typing_pattern:
                if char == '\b':  # Backspace
                    await self.page.keyboard.press('Backspace')
                elif char == '\n':  # Enter
                    await self.page.keyboard.press('Enter')
                else:
                    await self.page.keyboard.type(char)
                
                await asyncio.sleep(delay)
            
            # Update behavior history
            self._record_behavior('type', {
                'text': text,
                'pattern': typing_pattern,
                'element': str(element) if element else None
            })
            
            logger.debug(f"Human-like typing: '{text[:20]}...'")
            
        except Exception as e:
            logger.error(f"Typing failed: {e}")
            raise
    
    async def wait_and_observe(self, duration: float = 2.0) -> None:
        """Wait and perform human-like observation behaviors."""
        if not self.page:
            raise RuntimeError("Browser not started. Call start_browser() first.")
        
        try:
            # Generate observation behavior
            observation_pattern = self.behavior_engine.generate_observation_behavior(
                duration=duration,
                context=self.current_context
            )
            
            # Execute observation behaviors
            for behavior in observation_pattern:
                behavior_type = behavior['type']
                
                if behavior_type == 'mouse_movement':
                    pos = behavior['position']
                    await self.page.mouse.move(pos[0], pos[1])
                elif behavior_type == 'scroll':
                    delta = behavior['delta']
                    await self.page.mouse.wheel(delta_x=delta[0], delta_y=delta[1])
                elif behavior_type == 'pause':
                    await asyncio.sleep(behavior['duration'])
            
            # Update behavior history
            self._record_behavior('observe', {
                'duration': duration,
                'pattern': observation_pattern
            })
            
            logger.debug(f"Human-like observation for {duration}s")
            
        except Exception as e:
            logger.error(f"Observation failed: {e}")
            raise
    
    async def detect_and_evade(self) -> Dict[str, bool]:
        """Detect potential bot detection and apply evasion techniques."""
        if not self.detection_evasion or not self.page:
            return {'detected': False, 'evaded': False}
        
        try:
            # Check for detection signals
            detection_results = await self.stealth_controller.check_detection_signals(self.page)
            
            if detection_results['detected']:
                logger.warning("Bot detection signals detected")
                
                # Apply evasion techniques
                evasion_success = await self.stealth_controller.apply_evasion_techniques(
                    self.page, detection_results['signals']
                )
                
                # Generate evasive behavior
                if evasion_success:
                    await self._generate_evasive_behavior()
                
                return {
                    'detected': True,
                    'evaded': evasion_success,
                    'signals': detection_results['signals']
                }
            
            return {'detected': False, 'evaded': False}
            
        except Exception as e:
            logger.error(f"Detection/evasion failed: {e}")
            return {'detected': False, 'evaded': False, 'error': str(e)}
    
    async def close(self) -> None:
        """Close the browser and cleanup."""
        try:
            if self.browser:
                await self.browser.stop()
                self.browser = None
                self.page = None
            
            logger.info("Browser closed successfully")
            
        except Exception as e:
            logger.error(f"Failed to close browser: {e}")
    
    async def _get_current_mouse_position(self) -> Tuple[float, float]:
        """Get current mouse position."""
        try:
            # Try to get actual mouse position from browser
            result = await self.page.evaluate("""
                () => {
                    return {x: window.mouseX || 0, y: window.mouseY || 0};
                }
            """)
            return (result.get('x', 0), result.get('y', 0))
        except:
            # Fallback to center of viewport
            viewport = await self.page.evaluate("() => ({width: window.innerWidth, height: window.innerHeight})")
            return (viewport['width'] / 2, viewport['height'] / 2)
    
    async def _post_navigation_behavior(self) -> None:
        """Perform post-navigation human-like behavior."""
        # Wait for page load
        await asyncio.sleep(np.random.uniform(0.5, 2.0))
        
        # Generate some observation behavior
        await self.wait_and_observe(duration=np.random.uniform(1.0, 3.0))
    
    async def _generate_evasive_behavior(self) -> None:
        """Generate behavior to evade detection."""
        # Random mouse movements
        viewport = await self.page.evaluate("() => ({width: window.innerWidth, height: window.innerHeight})")
        
        for _ in range(np.random.randint(3, 8)):
            x = np.random.uniform(0, viewport['width'])
            y = np.random.uniform(0, viewport['height'])
            await self.page.mouse.move(x, y)
            await asyncio.sleep(np.random.uniform(0.1, 0.5))
        
        # Random scroll
        scroll_amount = np.random.randint(-200, 200)
        await self.page.mouse.wheel(delta_y=scroll_amount)
        
        # Wait
        await asyncio.sleep(np.random.uniform(1.0, 3.0))
    
    def _record_behavior(self, action_type: str, details: Dict) -> None:
        """Record behavior for analysis and improvement."""
        behavior_record = {
            'timestamp': asyncio.get_event_loop().time(),
            'action_type': action_type,
            'details': details,
            'context': self.current_context.copy()
        }
        
        self.behavior_history.append(behavior_record)
        
        # Keep only recent history
        if len(self.behavior_history) > 1000:
            self.behavior_history = self.behavior_history[-500:]
    
    def get_behavior_statistics(self) -> Dict:
        """Get statistics about performed behaviors."""
        if not self.behavior_history:
            return {}
        
        action_counts = {}
        for record in self.behavior_history:
            action_type = record['action_type']
            action_counts[action_type] = action_counts.get(action_type, 0) + 1
        
        return {
            'total_actions': len(self.behavior_history),
            'action_counts': action_counts,
            'session_duration': self.behavior_history[-1]['timestamp'] - self.behavior_history[0]['timestamp'],
            'detection_events': len(self.detection_signals)
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()


# Convenience functions
async def create_human_browser(model: HumanBehaviorModel, **kwargs) -> NoDriverIntegration:
    """Create and start a human-like browser instance."""
    integration = NoDriverIntegration(model, **kwargs)
    await integration.start_browser()
    return integration


async def human_automation_session(model: HumanBehaviorModel, 
                                 url: str,
                                 actions: List[Dict],
                                 **kwargs) -> Dict:
    """Run a complete human-like automation session."""
    async with NoDriverIntegration(model, **kwargs) as browser:
        await browser.navigate_to(url)
        
        results = []
        for action in actions:
            action_type = action.get('type')
            
            if action_type == 'click':
                await browser.human_like_click(action['x'], action['y'])
            elif action_type == 'scroll':
                await browser.human_like_scroll(
                    direction=action.get('direction', 'down'),
                    amount=action.get('amount', 500)
                )
            elif action_type == 'type':
                await browser.human_like_type(action['text'])
            elif action_type == 'wait':
                await browser.wait_and_observe(action.get('duration', 2.0))
            
            # Check for detection after each action
            detection_result = await browser.detect_and_evade()
            results.append({
                'action': action,
                'detection': detection_result
            })
        
        return {
            'results': results,
            'statistics': browser.get_behavior_statistics()
        }
