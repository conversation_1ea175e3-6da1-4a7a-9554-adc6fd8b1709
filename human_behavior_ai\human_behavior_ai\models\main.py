"""
Main model interface for HumanBehaviorAI.
"""

import torch
import torch.nn as nn
from typing import Dict, Optional, Union, List
from pathlib import Path
import logging

from .base import BaseModel, ModelConfig
from .hybrid import HybridBehaviorModel
from .transformer import MultiModalTransformer
from .vae import BehaviorVAE
from .gan import BehaviorGAN

logger = logging.getLogger(__name__)


class HumanBehaviorModel:
    """
    Main interface for human behavior modeling.
    
    This class provides a unified interface for different model architectures
    and handles model selection, training, and inference.
    """
    
    AVAILABLE_MODELS = {
        'hybrid': HybridBehaviorModel,
        'transformer': MultiModalTransformer,
        'vae': BehaviorVAE,
        'gan': BehaviorGAN,
    }
    
    def __init__(self, model_type: str = 'hybrid', config: Optional[ModelConfig] = None):
        """
        Initialize the human behavior model.
        
        Args:
            model_type: Type of model to use ('hybrid', 'transformer', 'vae', 'gan')
            config: Model configuration. If None, uses default configuration.
        """
        if model_type not in self.AVAILABLE_MODELS:
            raise ValueError(f"Unknown model type: {model_type}. Available: {list(self.AVAILABLE_MODELS.keys())}")
        
        self.model_type = model_type
        self.config = config or ModelConfig()
        
        # Initialize the model
        model_class = self.AVAILABLE_MODELS[model_type]
        self.model = model_class(self.config)
        
        # Training state
        self.is_trained = False
        self.training_history = []
        
        logger.info(f"Initialized {model_type} model with {self.model.get_num_parameters():,} parameters")
    
    def forward(self, batch: Dict[str, torch.Tensor], **kwargs) -> Dict[str, torch.Tensor]:
        """Forward pass through the model."""
        return self.model.forward(batch, **kwargs)
    
    def generate(self, context: Dict[str, torch.Tensor], length: int, **kwargs) -> Dict[str, torch.Tensor]:
        """Generate behavior sequences."""
        return self.model.generate(context, length, **kwargs)
    
    def train_model(self, train_loader, val_loader=None, num_epochs: int = 100, 
                   device: str = 'cuda', **kwargs):
        """
        Train the model.
        
        Args:
            train_loader: Training data loader
            val_loader: Validation data loader (optional)
            num_epochs: Number of training epochs
            device: Device to train on ('cuda' or 'cpu')
            **kwargs: Additional training arguments
        """
        from ..training import get_trainer
        
        # Get appropriate trainer for the model type
        trainer = get_trainer(self.model_type, self.model, self.config)
        
        # Train the model
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=num_epochs,
            device=device,
            **kwargs
        )
        
        self.is_trained = True
        self.training_history.extend(history)
        
        logger.info(f"Training completed. Final loss: {history[-1].get('train_loss', 'N/A')}")
        
        return history
    
    def evaluate(self, test_loader, device: str = 'cuda', **kwargs) -> Dict[str, float]:
        """
        Evaluate the model.
        
        Args:
            test_loader: Test data loader
            device: Device to evaluate on
            **kwargs: Additional evaluation arguments
        """
        from ..evaluation import get_evaluator
        
        if not self.is_trained:
            logger.warning("Model has not been trained yet. Evaluation results may not be meaningful.")
        
        # Get appropriate evaluator
        evaluator = get_evaluator(self.model_type, self.model, self.config)
        
        # Evaluate the model
        results = evaluator.evaluate(test_loader, device=device, **kwargs)
        
        logger.info(f"Evaluation completed. Results: {results}")
        
        return results
    
    def save_pretrained(self, path: Union[str, Path]) -> None:
        """Save the trained model."""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        
        # Save the model
        self.model.save_pretrained(path)
        
        # Save additional metadata
        metadata = {
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'training_history': self.training_history,
            'num_parameters': self.model.get_num_parameters(),
        }
        
        import json
        with open(path / 'metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Model saved to {path}")
    
    @classmethod
    def load_pretrained(cls, path: Union[str, Path]) -> 'HumanBehaviorModel':
        """Load a pretrained model."""
        path = Path(path)
        
        # Load metadata
        import json
        with open(path / 'metadata.json', 'r') as f:
            metadata = json.load(f)
        
        model_type = metadata['model_type']
        
        # Load configuration
        config = ModelConfig.load(path / 'config.json')
        
        # Create model instance
        instance = cls(model_type=model_type, config=config)
        
        # Load model weights
        model_class = cls.AVAILABLE_MODELS[model_type]
        instance.model = model_class.load_pretrained(path)
        
        # Restore metadata
        instance.is_trained = metadata.get('is_trained', False)
        instance.training_history = metadata.get('training_history', [])
        
        logger.info(f"Loaded pretrained {model_type} model from {path}")
        
        return instance
    
    def to(self, device: Union[str, torch.device]) -> 'HumanBehaviorModel':
        """Move model to device."""
        self.model = self.model.to(device)
        return self
    
    def cuda(self) -> 'HumanBehaviorModel':
        """Move model to CUDA."""
        return self.to('cuda')
    
    def cpu(self) -> 'HumanBehaviorModel':
        """Move model to CPU."""
        return self.to('cpu')
    
    def eval(self) -> 'HumanBehaviorModel':
        """Set model to evaluation mode."""
        self.model.eval()
        return self
    
    def train(self) -> 'HumanBehaviorModel':
        """Set model to training mode."""
        self.model.train()
        return self
    
    def parameters(self):
        """Get model parameters."""
        return self.model.parameters()
    
    def named_parameters(self):
        """Get named model parameters."""
        return self.model.named_parameters()
    
    def state_dict(self):
        """Get model state dictionary."""
        return self.model.state_dict()
    
    def load_state_dict(self, state_dict):
        """Load model state dictionary."""
        return self.model.load_state_dict(state_dict)
    
    def get_model_info(self) -> Dict:
        """Get comprehensive model information."""
        return {
            'model_type': self.model_type,
            'num_parameters': self.model.get_num_parameters(),
            'is_trained': self.is_trained,
            'config': self.config.to_dict(),
            'training_epochs': len(self.training_history),
            'model_class': self.model.__class__.__name__,
        }
    
    def generate_diverse_behaviors(self, context: Dict[str, torch.Tensor], 
                                 num_samples: int = 5, length: int = 100,
                                 diversity_method: str = 'temperature') -> List[Dict[str, torch.Tensor]]:
        """
        Generate diverse behavior samples.
        
        Args:
            context: Context for generation
            num_samples: Number of diverse samples to generate
            length: Length of each sequence
            diversity_method: Method for ensuring diversity ('temperature', 'nucleus', 'top_k')
        """
        self.model.eval()
        
        samples = []
        
        with torch.no_grad():
            for i in range(num_samples):
                if diversity_method == 'temperature':
                    # Use different temperature for each sample
                    temperature = 0.8 + 0.4 * (i / num_samples)  # 0.8 to 1.2
                    sample = self.generate(context, length, temperature=temperature)
                elif diversity_method == 'nucleus':
                    # Use nucleus sampling with different p values
                    p = 0.7 + 0.25 * (i / num_samples)  # 0.7 to 0.95
                    sample = self.generate(context, length, nucleus_p=p)
                elif diversity_method == 'top_k':
                    # Use top-k sampling with different k values
                    k = max(10, 50 - 10 * i)  # 50 to 10
                    sample = self.generate(context, length, top_k=k)
                else:
                    # Default generation with noise
                    sample = self.generate(context, length)
                    
                    # Add small amount of noise for diversity
                    for modality in sample:
                        if isinstance(sample[modality], torch.Tensor):
                            noise_scale = 0.01 * (i + 1) / num_samples
                            sample[modality] += torch.randn_like(sample[modality]) * noise_scale
                
                samples.append(sample)
        
        return samples
    
    def interpolate_behaviors(self, start_context: Dict[str, torch.Tensor],
                            end_context: Dict[str, torch.Tensor],
                            num_steps: int = 10) -> Dict[str, torch.Tensor]:
        """
        Interpolate between two behavior patterns.
        
        Args:
            start_context: Starting behavior context
            end_context: Ending behavior context
            num_steps: Number of interpolation steps
        """
        if hasattr(self.model, 'interpolate'):
            return self.model.interpolate(start_context, end_context, num_steps)
        else:
            logger.warning(f"Model type {self.model_type} does not support interpolation")
            return self.generate(start_context, length=100)
    
    def get_attention_weights(self, batch: Dict[str, torch.Tensor]) -> Optional[torch.Tensor]:
        """Get attention weights if the model supports it."""
        if hasattr(self.model, 'get_attention_weights'):
            return self.model.get_attention_weights(batch)
        else:
            logger.warning(f"Model type {self.model_type} does not provide attention weights")
            return None
    
    def analyze_behavior_patterns(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Analyze behavior patterns in the input data."""
        self.model.eval()
        
        with torch.no_grad():
            outputs = self.forward(batch)
            
            analysis = {
                'input_statistics': self._compute_input_statistics(batch),
                'model_outputs': outputs,
            }
            
            # Add model-specific analysis
            if hasattr(self.model, 'analyze_patterns'):
                model_analysis = self.model.analyze_patterns(batch)
                analysis.update(model_analysis)
        
        return analysis
    
    def _compute_input_statistics(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Compute basic statistics of input data."""
        stats = {}
        
        for modality, data in batch.items():
            if isinstance(data, torch.Tensor) and data.numel() > 0:
                stats[f'{modality}_mean'] = data.mean()
                stats[f'{modality}_std'] = data.std()
                stats[f'{modality}_min'] = data.min()
                stats[f'{modality}_max'] = data.max()
        
        return stats
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return (f"HumanBehaviorModel(\n"
                f"  model_type={self.model_type},\n"
                f"  num_parameters={self.model.get_num_parameters():,},\n"
                f"  is_trained={self.is_trained},\n"
                f"  config={self.config.__class__.__name__}\n"
                f")")


def create_model(model_type: str = 'hybrid', **config_kwargs) -> HumanBehaviorModel:
    """
    Factory function to create a human behavior model.
    
    Args:
        model_type: Type of model to create
        **config_kwargs: Configuration parameters
    """
    config = ModelConfig(**config_kwargs)
    return HumanBehaviorModel(model_type=model_type, config=config)


def load_model(path: Union[str, Path]) -> HumanBehaviorModel:
    """
    Load a pretrained model.
    
    Args:
        path: Path to the saved model
    """
    return HumanBehaviorModel.load_pretrained(path)
