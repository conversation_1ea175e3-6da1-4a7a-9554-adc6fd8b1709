"""
HumanBehaviorAI: State-of-the-Art Human Interaction Pattern Learning
"""

from setuptools import setup, find_packages

# Read the README file
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Read requirements
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

# Define extras_require
extras_require = {
    "dev": [
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "pytest-cov>=4.1.0",
        "black>=23.11.0",
        "isort>=5.12.0",
        "flake8>=6.1.0",
        "mypy>=1.7.0",
        "pre-commit>=3.5.0",
    ],
    "docs": [
        "sphinx>=7.2.0",
        "sphinx-rtd-theme>=1.3.0",
        "myst-parser>=2.0.0",
    ],
    "gpu": [
        "torch>=2.1.0",
        "torchvision>=0.16.0",
        "torchaudio>=2.1.0",
    ],
    "distributed": [
        "deepspeed>=0.12.0",
        "fairscale>=0.4.13",
    ],
}

# Add 'all' extras that includes everything
all_extras = []
for extra_deps in extras_require.values():
    all_extras.extend(extra_deps)
extras_require["all"] = list(set(all_extras))  # Remove duplicates

setup(
    name="human-behavior-ai",
    version="1.0.0",
    author="HumanBehaviorAI Team",
    author_email="<EMAIL>",
    description="State-of-the-Art Human Interaction Pattern Learning for Web Automation",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/human-behavior-ai",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require=extras_require,
    entry_points={
        "console_scripts": [
            "human-behavior-train=human_behavior_ai.cli.train:main",
            "human-behavior-evaluate=human_behavior_ai.cli.evaluate:main",
            "human-behavior-generate=human_behavior_ai.cli.generate:main",
            "human-behavior-serve=human_behavior_ai.cli.serve:main",
        ],
    },
    include_package_data=True,
    package_data={
        "human_behavior_ai": [
            "configs/*.yaml",
            "configs/**/*.yaml",
            "data/schemas/*.json",
            "models/pretrained/*.pt",
        ],
    },
    keywords=[
        "machine learning",
        "deep learning",
        "human behavior",
        "web automation",
        "bot detection",
        "artificial intelligence",
        "transformers",
        "gan",
        "vae",
        "reinforcement learning",
        "nodriver",
        "selenium",
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-org/human-behavior-ai/issues",
        "Source": "https://github.com/your-org/human-behavior-ai",
        "Documentation": "https://human-behavior-ai.readthedocs.io/",
    },
)
