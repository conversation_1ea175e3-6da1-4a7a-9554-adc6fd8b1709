#!/usr/bin/env python3
"""
Training script for human behavior AI models.
"""

import argparse
import logging
import os
import sys
from pathlib import Path
import json
import torch
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
import wandb

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from human_behavior_ai.models import create_model, ModelConfig
from human_behavior_ai.data import create_data_loaders
from human_behavior_ai.training import get_trainer, TrainingConfig
from human_behavior_ai.utils import setup_logging, set_seed, get_device_info

logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train human behavior AI models')
    
    # Model configuration
    parser.add_argument('--model-type', type=str, default='hybrid',
                       choices=['hybrid', 'transformer', 'vae', 'gan'],
                       help='Type of model to train')
    parser.add_argument('--config', type=str, help='Path to model configuration file')
    
    # Data configuration
    parser.add_argument('--data-path', type=str, required=True,
                       help='Path to training data directory')
    parser.add_argument('--val-data-path', type=str,
                       help='Path to validation data directory (optional)')
    parser.add_argument('--sequence-length', type=int, default=1024,
                       help='Length of input sequences')
    parser.add_argument('--batch-size', type=int, default=32,
                       help='Training batch size')
    parser.add_argument('--val-batch-size', type=int, default=64,
                       help='Validation batch size')
    
    # Training configuration
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of training epochs')
    parser.add_argument('--learning-rate', type=float, default=1e-4,
                       help='Learning rate')
    parser.add_argument('--weight-decay', type=float, default=1e-5,
                       help='Weight decay')
    parser.add_argument('--warmup-steps', type=int, default=1000,
                       help='Number of warmup steps')
    parser.add_argument('--gradient-clip', type=float, default=1.0,
                       help='Gradient clipping value')
    
    # Training strategy
    parser.add_argument('--training-strategy', type=str, default='multi_stage',
                       choices=['single_stage', 'multi_stage', 'curriculum'],
                       help='Training strategy')
    parser.add_argument('--mixed-precision', action='store_true',
                       help='Use mixed precision training')
    parser.add_argument('--gradient-accumulation', type=int, default=1,
                       help='Gradient accumulation steps')
    
    # Distributed training
    parser.add_argument('--distributed', action='store_true',
                       help='Use distributed training')
    parser.add_argument('--local-rank', type=int, default=0,
                       help='Local rank for distributed training')
    
    # Checkpointing and logging
    parser.add_argument('--output-dir', type=str, default='./outputs',
                       help='Output directory for checkpoints and logs')
    parser.add_argument('--save-every', type=int, default=1000,
                       help='Save checkpoint every N steps')
    parser.add_argument('--eval-every', type=int, default=500,
                       help='Evaluate every N steps')
    parser.add_argument('--log-every', type=int, default=100,
                       help='Log every N steps')
    
    # Experiment tracking
    parser.add_argument('--wandb-project', type=str, default='human-behavior-ai',
                       help='Weights & Biases project name')
    parser.add_argument('--wandb-run-name', type=str,
                       help='Weights & Biases run name')
    parser.add_argument('--no-wandb', action='store_true',
                       help='Disable Weights & Biases logging')
    
    # Hardware optimization
    parser.add_argument('--num-workers', type=int, default=4,
                       help='Number of data loader workers')
    parser.add_argument('--pin-memory', action='store_true',
                       help='Pin memory for data loading')
    parser.add_argument('--compile', action='store_true',
                       help='Use torch.compile for optimization')
    
    # Debugging
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    parser.add_argument('--profile', action='store_true',
                       help='Enable profiling')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    return parser.parse_args()


def setup_distributed(args):
    """Setup distributed training."""
    if args.distributed:
        # Initialize process group
        dist.init_process_group(backend='nccl')
        
        # Set device
        torch.cuda.set_device(args.local_rank)
        
        # Update batch size for distributed training
        args.batch_size = args.batch_size // dist.get_world_size()
        args.val_batch_size = args.val_batch_size // dist.get_world_size()
        
        logger.info(f"Distributed training initialized. Rank: {dist.get_rank()}, World size: {dist.get_world_size()}")


def load_config(config_path: str) -> ModelConfig:
    """Load model configuration from file."""
    with open(config_path, 'r') as f:
        config_dict = json.load(f)
    return ModelConfig.from_dict(config_dict)


def create_output_dir(args):
    """Create output directory structure."""
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create subdirectories
    (output_dir / 'checkpoints').mkdir(exist_ok=True)
    (output_dir / 'logs').mkdir(exist_ok=True)
    (output_dir / 'configs').mkdir(exist_ok=True)
    
    return output_dir


def save_training_config(args, config: ModelConfig, output_dir: Path):
    """Save training configuration."""
    training_config = {
        'model_type': args.model_type,
        'model_config': config.to_dict(),
        'training_args': vars(args),
        'device_info': get_device_info(),
    }
    
    config_path = output_dir / 'configs' / 'training_config.json'
    with open(config_path, 'w') as f:
        json.dump(training_config, f, indent=2)
    
    logger.info(f"Training configuration saved to {config_path}")


def main():
    """Main training function."""
    args = parse_args()
    
    # Setup logging
    setup_logging(level=logging.DEBUG if args.debug else logging.INFO)
    
    # Set random seed
    set_seed(args.seed)
    
    # Setup distributed training
    setup_distributed(args)
    
    # Create output directory
    output_dir = create_output_dir(args)
    
    # Load or create model configuration
    if args.config:
        config = load_config(args.config)
    else:
        config = ModelConfig(
            sequence_length=args.sequence_length,
            learning_rate=args.learning_rate,
            batch_size=args.batch_size,
        )
    
    # Save training configuration
    save_training_config(args, config, output_dir)
    
    # Initialize Weights & Biases
    if not args.no_wandb and (not args.distributed or dist.get_rank() == 0):
        wandb.init(
            project=args.wandb_project,
            name=args.wandb_run_name,
            config=vars(args),
            dir=str(output_dir / 'logs')
        )
    
    # Create model
    logger.info(f"Creating {args.model_type} model...")
    model = create_model(args.model_type, config=config)
    
    # Move to device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    # Compile model if requested
    if args.compile and hasattr(torch, 'compile'):
        logger.info("Compiling model with torch.compile...")
        model.model = torch.compile(model.model)
    
    # Setup distributed model
    if args.distributed:
        model.model = DDP(model.model, device_ids=[args.local_rank])
    
    # Create data loaders
    logger.info("Creating data loaders...")
    train_loader, val_loader = create_data_loaders(
        train_data_path=args.data_path,
        val_data_path=args.val_data_path,
        sequence_length=args.sequence_length,
        batch_size=args.batch_size,
        val_batch_size=args.val_batch_size,
        num_workers=args.num_workers,
        pin_memory=args.pin_memory,
        distributed=args.distributed
    )
    
    # Create training configuration
    training_config = TrainingConfig(
        num_epochs=args.epochs,
        learning_rate=args.learning_rate,
        weight_decay=args.weight_decay,
        warmup_steps=args.warmup_steps,
        gradient_clip=args.gradient_clip,
        mixed_precision=args.mixed_precision,
        gradient_accumulation_steps=args.gradient_accumulation,
        save_every=args.save_every,
        eval_every=args.eval_every,
        log_every=args.log_every,
        output_dir=str(output_dir),
        strategy=args.training_strategy,
    )
    
    # Create trainer
    logger.info("Creating trainer...")
    trainer = get_trainer(args.model_type, model, training_config)
    
    # Start training
    logger.info("Starting training...")
    try:
        history = trainer.train(
            train_loader=train_loader,
            val_loader=val_loader,
            device=device
        )
        
        # Save final model
        final_model_path = output_dir / 'final_model'
        model.save_pretrained(final_model_path)
        logger.info(f"Final model saved to {final_model_path}")
        
        # Log final results
        if not args.no_wandb and (not args.distributed or dist.get_rank() == 0):
            wandb.log({
                'final_train_loss': history[-1].get('train_loss', 0),
                'final_val_loss': history[-1].get('val_loss', 0),
                'total_epochs': len(history),
            })
            wandb.finish()
        
        logger.info("Training completed successfully!")
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        
        # Save checkpoint
        checkpoint_path = output_dir / 'interrupted_checkpoint'
        model.save_pretrained(checkpoint_path)
        logger.info(f"Checkpoint saved to {checkpoint_path}")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        raise
    
    finally:
        # Cleanup distributed training
        if args.distributed:
            dist.destroy_process_group()


if __name__ == '__main__':
    main()
