"""
Configuration management utilities for HumanBehaviorAI.
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Union, Optional
import logging
from omegaconf import OmegaConf, DictConfig

logger = logging.getLogger(__name__)


def load_config(config_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Load configuration from file.
    
    Args:
        config_path: Path to configuration file (.json, .yaml, .yml)
        
    Returns:
        Configuration dictionary
        
    Raises:
        ValueError: If file format is not supported
        FileNotFoundError: If config file doesn't exist
    """
    config_path = Path(config_path)
    
    if not config_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    suffix = config_path.suffix.lower()
    
    if suffix == '.json':
        with open(config_path, 'r') as f:
            config = json.load(f)
    elif suffix in ['.yaml', '.yml']:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    else:
        raise ValueError(f"Unsupported config file format: {suffix}")
    
    logger.info(f"Configuration loaded from: {config_path}")
    return config


def save_config(config: Dict[str, Any], config_path: Union[str, Path]) -> None:
    """
    Save configuration to file.
    
    Args:
        config: Configuration dictionary
        config_path: Path to save configuration file
    """
    config_path = Path(config_path)
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    suffix = config_path.suffix.lower()
    
    if suffix == '.json':
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
    elif suffix in ['.yaml', '.yml']:
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
    else:
        # Default to JSON
        config_path = config_path.with_suffix('.json')
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
    
    logger.info(f"Configuration saved to: {config_path}")


def merge_configs(*configs: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge multiple configuration dictionaries.
    
    Args:
        *configs: Configuration dictionaries to merge
        
    Returns:
        Merged configuration dictionary
    """
    merged = {}
    
    for config in configs:
        merged = deep_merge(merged, config)
    
    return merged


def deep_merge(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Deep merge two dictionaries.
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary (takes precedence)
        
    Returns:
        Merged dictionary
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge(result[key], value)
        else:
            result[key] = value
    
    return result


def load_hydra_config(config_path: Union[str, Path]) -> DictConfig:
    """
    Load configuration using Hydra/OmegaConf.
    
    Args:
        config_path: Path to configuration file
        
    Returns:
        OmegaConf configuration object
    """
    config_path = Path(config_path)
    
    if not config_path.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    config = OmegaConf.load(config_path)
    logger.info(f"Hydra configuration loaded from: {config_path}")
    
    return config


def save_hydra_config(config: DictConfig, config_path: Union[str, Path]) -> None:
    """
    Save Hydra/OmegaConf configuration to file.
    
    Args:
        config: OmegaConf configuration object
        config_path: Path to save configuration file
    """
    config_path = Path(config_path)
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    OmegaConf.save(config, config_path)
    logger.info(f"Hydra configuration saved to: {config_path}")


def validate_config(config: Dict[str, Any], schema: Dict[str, Any]) -> bool:
    """
    Validate configuration against a schema.
    
    Args:
        config: Configuration to validate
        schema: Schema to validate against
        
    Returns:
        True if valid, False otherwise
    """
    # Simple validation - can be extended with more sophisticated schema validation
    for key, expected_type in schema.items():
        if key not in config:
            logger.error(f"Missing required configuration key: {key}")
            return False
        
        if not isinstance(config[key], expected_type):
            logger.error(f"Invalid type for {key}: expected {expected_type}, got {type(config[key])}")
            return False
    
    return True


def get_default_config() -> Dict[str, Any]:
    """
    Get default configuration for HumanBehaviorAI.
    
    Returns:
        Default configuration dictionary
    """
    return {
        'model': {
            'type': 'hybrid',
            'sequence_length': 1024,
            'hidden_dim': 512,
            'num_layers': 6,
            'num_heads': 8,
            'dropout': 0.1,
        },
        'training': {
            'batch_size': 32,
            'learning_rate': 1e-4,
            'num_epochs': 100,
            'warmup_steps': 1000,
            'weight_decay': 1e-5,
            'gradient_clip': 1.0,
        },
        'data': {
            'sequence_length': 1024,
            'overlap': 0.5,
            'augmentation': True,
            'normalization': True,
        },
        'hardware': {
            'device': 'auto',
            'mixed_precision': True,
            'num_workers': 4,
            'pin_memory': True,
        },
        'logging': {
            'level': 'INFO',
            'wandb_project': 'human-behavior-ai',
            'log_every': 100,
            'save_every': 1000,
        }
    }
