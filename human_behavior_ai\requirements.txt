# Core ML/DL Dependencies
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0
transformers>=4.35.0
accelerate>=0.24.0
datasets>=2.14.0

# Scientific Computing
numpy>=1.24.0
scipy>=1.11.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Deep Learning Utilities
lightning>=2.1.0
torchmetrics>=1.2.0
timm>=0.9.0
einops>=0.7.0

# Optimization and Training
optuna>=3.4.0
ray[tune]>=2.8.0
wandb>=0.16.0
tensorboard>=2.15.0

# Configuration Management
hydra-core>=1.3.0
omegaconf>=2.3.0

# Data Processing
h5py>=3.9.0
zarr>=2.16.0
pyarrow>=14.0.0
fastparquet>=2023.10.0

# Visualization
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0
bokeh>=3.3.0

# Web Automation
nodriver>=0.28
selenium>=4.15.0
requests>=2.31.0
aiohttp>=3.9.0

# Utilities
tqdm>=4.66.0
rich>=13.7.0
click>=8.1.0
pydantic>=2.5.0
python-dotenv>=1.0.0

# Development Tools
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0
pre-commit>=3.5.0

# Documentation
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# Performance Monitoring
psutil>=5.9.0
nvidia-ml-py>=12.535.0
gpustat>=1.1.0

# Async Support
asyncio>=3.4.3
aiofiles>=23.2.0
uvloop>=0.19.0

# Serialization
pickle5>=0.0.12
joblib>=1.3.0
cloudpickle>=3.0.0

# Networking
httpx>=0.25.0
websockets>=12.0

# System Integration
psutil>=5.9.0
py-cpuinfo>=9.0.0

# Optional GPU Acceleration (uncomment if using CUDA)
# torch-audio>=2.1.0+cu118
# torch-vision>=0.16.0+cu118
# torch>=2.1.0+cu118

# Optional Distributed Training
# deepspeed>=0.12.0
# fairscale>=0.4.13

# Optional Advanced Optimizers
# apex  # NVIDIA Apex for mixed precision
# lion-pytorch>=0.0.6  # Lion optimizer
