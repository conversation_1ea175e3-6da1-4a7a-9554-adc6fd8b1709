"""
Input/Output utilities for HumanBehaviorAI.
"""

import torch
import pickle
import json
from pathlib import Path
from typing import Dict, Any, Optional, Union
import logging
from datetime import datetime
import shutil

logger = logging.getLogger(__name__)


def save_checkpoint(
    model: torch.nn.Module,
    optimizer: torch.optim.Optimizer,
    epoch: int,
    loss: float,
    checkpoint_path: Union[str, Path],
    metadata: Optional[Dict[str, Any]] = None
) -> None:
    """
    Save model checkpoint.
    
    Args:
        model: PyTorch model
        optimizer: Optimizer
        epoch: Current epoch
        loss: Current loss value
        checkpoint_path: Path to save checkpoint
        metadata: Additional metadata to save
    """
    checkpoint_path = Path(checkpoint_path)
    checkpoint_path.parent.mkdir(parents=True, exist_ok=True)
    
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss,
        'timestamp': datetime.now().isoformat(),
    }
    
    if metadata:
        checkpoint['metadata'] = metadata
    
    torch.save(checkpoint, checkpoint_path)
    logger.info(f"Checkpoint saved to: {checkpoint_path}")


def load_checkpoint(
    checkpoint_path: Union[str, Path],
    model: Optional[torch.nn.Module] = None,
    optimizer: Optional[torch.optim.Optimizer] = None,
    device: Optional[torch.device] = None
) -> Dict[str, Any]:
    """
    Load model checkpoint.
    
    Args:
        checkpoint_path: Path to checkpoint file
        model: Optional model to load state into
        optimizer: Optional optimizer to load state into
        device: Device to load checkpoint on
        
    Returns:
        Checkpoint dictionary
    """
    checkpoint_path = Path(checkpoint_path)
    
    if not checkpoint_path.exists():
        raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
    
    # Load checkpoint
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # Load model state
    if model is not None and 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info("Model state loaded from checkpoint")
    
    # Load optimizer state
    if optimizer is not None and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        logger.info("Optimizer state loaded from checkpoint")
    
    logger.info(f"Checkpoint loaded from: {checkpoint_path}")
    logger.info(f"Checkpoint epoch: {checkpoint.get('epoch', 'unknown')}")
    logger.info(f"Checkpoint loss: {checkpoint.get('loss', 'unknown')}")
    
    return checkpoint


def create_experiment_dir(
    base_dir: Union[str, Path],
    experiment_name: Optional[str] = None,
    timestamp: bool = True
) -> Path:
    """
    Create experiment directory with timestamp.
    
    Args:
        base_dir: Base directory for experiments
        experiment_name: Name of the experiment
        timestamp: Whether to add timestamp to directory name
        
    Returns:
        Path to created experiment directory
    """
    base_dir = Path(base_dir)
    base_dir.mkdir(parents=True, exist_ok=True)
    
    # Create experiment name
    if experiment_name is None:
        experiment_name = "experiment"
    
    if timestamp:
        timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
        exp_dir_name = f"{experiment_name}_{timestamp_str}"
    else:
        exp_dir_name = experiment_name
    
    exp_dir = base_dir / exp_dir_name
    exp_dir.mkdir(parents=True, exist_ok=True)
    
    # Create subdirectories
    (exp_dir / 'checkpoints').mkdir(exist_ok=True)
    (exp_dir / 'logs').mkdir(exist_ok=True)
    (exp_dir / 'configs').mkdir(exist_ok=True)
    (exp_dir / 'outputs').mkdir(exist_ok=True)
    (exp_dir / 'plots').mkdir(exist_ok=True)
    
    logger.info(f"Experiment directory created: {exp_dir}")
    return exp_dir


def save_pickle(obj: Any, file_path: Union[str, Path]) -> None:
    """
    Save object to pickle file.
    
    Args:
        obj: Object to save
        file_path: Path to save file
    """
    file_path = Path(file_path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(file_path, 'wb') as f:
        pickle.dump(obj, f)
    
    logger.info(f"Object saved to pickle: {file_path}")


def load_pickle(file_path: Union[str, Path]) -> Any:
    """
    Load object from pickle file.
    
    Args:
        file_path: Path to pickle file
        
    Returns:
        Loaded object
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"Pickle file not found: {file_path}")
    
    with open(file_path, 'rb') as f:
        obj = pickle.load(f)
    
    logger.info(f"Object loaded from pickle: {file_path}")
    return obj


def save_json(obj: Dict[str, Any], file_path: Union[str, Path], indent: int = 2) -> None:
    """
    Save dictionary to JSON file.
    
    Args:
        obj: Dictionary to save
        file_path: Path to save file
        indent: JSON indentation
    """
    file_path = Path(file_path)
    file_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(file_path, 'w') as f:
        json.dump(obj, f, indent=indent, default=str)
    
    logger.info(f"JSON saved to: {file_path}")


def load_json(file_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Load dictionary from JSON file.
    
    Args:
        file_path: Path to JSON file
        
    Returns:
        Loaded dictionary
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"JSON file not found: {file_path}")
    
    with open(file_path, 'r') as f:
        obj = json.load(f)
    
    logger.info(f"JSON loaded from: {file_path}")
    return obj


def backup_file(file_path: Union[str, Path], backup_dir: Optional[Union[str, Path]] = None) -> Path:
    """
    Create backup of a file.
    
    Args:
        file_path: Path to file to backup
        backup_dir: Directory to store backup (default: same directory as original)
        
    Returns:
        Path to backup file
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    if backup_dir is None:
        backup_dir = file_path.parent
    else:
        backup_dir = Path(backup_dir)
        backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Create backup filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
    backup_path = backup_dir / backup_name
    
    # Copy file
    shutil.copy2(file_path, backup_path)
    logger.info(f"File backed up: {file_path} -> {backup_path}")
    
    return backup_path


def get_file_size(file_path: Union[str, Path]) -> int:
    """
    Get file size in bytes.
    
    Args:
        file_path: Path to file
        
    Returns:
        File size in bytes
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")
    
    return file_path.stat().st_size


def format_file_size(size_bytes: int) -> str:
    """
    Format file size in human-readable format.
    
    Args:
        size_bytes: Size in bytes
        
    Returns:
        Formatted size string
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"


def clean_directory(directory: Union[str, Path], pattern: str = "*", keep_recent: int = 5) -> None:
    """
    Clean directory by removing old files matching pattern.
    
    Args:
        directory: Directory to clean
        pattern: File pattern to match
        keep_recent: Number of recent files to keep
    """
    directory = Path(directory)
    
    if not directory.exists():
        logger.warning(f"Directory not found: {directory}")
        return
    
    # Get matching files sorted by modification time
    files = sorted(directory.glob(pattern), key=lambda x: x.stat().st_mtime, reverse=True)
    
    # Remove old files
    files_to_remove = files[keep_recent:]
    for file_path in files_to_remove:
        try:
            file_path.unlink()
            logger.info(f"Removed old file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to remove file {file_path}: {e}")
    
    if files_to_remove:
        logger.info(f"Cleaned {len(files_to_remove)} old files from {directory}")
    else:
        logger.info(f"No files to clean in {directory}")
