"""
Hybrid model combining Transformer, VAE, and GAN for comprehensive behavior learning.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple, List
import math

from .base import BaseModel, ModelConfig
from .transformer import MultiModalTransformer
from .vae import BehaviorVAE
from .gan import BehaviorGAN


class HybridBehaviorModel(BaseModel):
    """
    Hybrid model combining multiple architectures for state-of-the-art behavior learning.
    
    Architecture:
    1. Transformer Encoder: Learns sequential dependencies and cross-modal interactions
    2. VAE Component: Learns latent representations and enables diverse generation
    3. GAN Component: Ensures realistic behavior synthesis through adversarial training
    4. Fusion Network: Combines outputs from all components intelligently
    """
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        
        # Hybrid-specific configuration
        self.transformer_weight = config.model_specific.get('transformer_weight', 0.4)
        self.vae_weight = config.model_specific.get('vae_weight', 0.3)
        self.gan_weight = config.model_specific.get('gan_weight', 0.3)
        self.fusion_strategy = config.model_specific.get('fusion_strategy', 'attention')
        
        # Core components
        self.transformer = MultiModalTransformer(config)
        self.vae = BehaviorVAE(config)
        self.gan = BehaviorGAN(config)
        
        # Fusion network
        self.fusion_network = FusionNetwork(
            input_dim=config.hidden_dim * 3,  # Outputs from 3 components
            hidden_dim=config.hidden_dim,
            output_dim=config.output_dim,
            strategy=self.fusion_strategy
        )
        
        # Uncertainty estimation
        self.uncertainty_estimator = UncertaintyEstimator(
            input_dim=config.hidden_dim,
            hidden_dim=config.hidden_dim // 2
        )
        
        # Adaptive weighting network
        self.adaptive_weighting = AdaptiveWeighting(
            input_dim=config.hidden_dim,
            num_components=3
        )
        
        # Multi-scale temporal modeling
        self.temporal_scales = nn.ModuleList([
            TemporalScale(
                input_dim=config.hidden_dim,
                hidden_dim=config.hidden_dim,
                kernel_size=k,
                dilation=d
            )
            for k, d in [(3, 1), (5, 2), (7, 4), (9, 8)]
        ])
        
        # Context-aware adaptation
        self.context_adapter = ContextAdapter(
            context_dim=config.context_dim,
            hidden_dim=config.hidden_dim,
            num_adaptation_layers=2
        )
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Conv1d):
            torch.nn.init.kaiming_normal_(module.weight, mode='fan_out', nonlinearity='relu')
    
    def forward(self, batch: Dict[str, torch.Tensor], 
                training_stage: str = 'joint') -> Dict[str, torch.Tensor]:
        """
        Forward pass through hybrid model.
        
        Args:
            batch: Input batch containing multi-modal data
            training_stage: 'transformer', 'vae', 'gan', or 'joint'
        """
        outputs = {}
        
        # Get outputs from each component
        if training_stage in ['transformer', 'joint']:
            transformer_outputs = self.transformer(batch)
            outputs.update({f'transformer_{k}': v for k, v in transformer_outputs.items()})
        
        if training_stage in ['vae', 'joint']:
            vae_outputs = self.vae(batch)
            outputs.update({f'vae_{k}': v for k, v in vae_outputs.items()})
        
        if training_stage in ['gan', 'joint']:
            # For GAN, we need to handle real/fake data differently
            if 'is_real' in batch and not batch['is_real']:
                # Generate fake data
                noise = self.gan.generate_noise(batch['mouse'].shape[0], batch['mouse'].device)
                fake_data = self.gan.forward_generator(noise, batch.get('context'))
                gan_outputs = {'generated_data': fake_data}
            else:
                # Process real data through discriminator
                embedded_data = self.embed_inputs(batch)
                discriminator_score = self.gan.forward_discriminator(embedded_data)
                gan_outputs = {'discriminator_score': discriminator_score}
            
            outputs.update({f'gan_{k}': v for k, v in gan_outputs.items()})
        
        # Joint processing for inference and joint training
        if training_stage == 'joint':
            joint_outputs = self._joint_forward(batch, outputs)
            outputs.update(joint_outputs)
        
        return outputs
    
    def _joint_forward(self, batch: Dict[str, torch.Tensor], 
                      component_outputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Joint forward pass combining all components."""
        # Extract hidden states from components
        transformer_hidden = component_outputs.get('transformer_hidden_states')
        vae_hidden = component_outputs.get('vae_reconstruction')
        
        # Handle GAN output (might be generated data or discriminator features)
        if 'gan_generated_data' in component_outputs:
            gan_hidden = self.embed_inputs({
                'mouse': component_outputs['gan_generated_data'][:, :, :self.config.mouse_dim],
                'keyboard': component_outputs['gan_generated_data'][:, :, self.config.mouse_dim:self.config.mouse_dim+self.config.keyboard_dim],
                'scroll': component_outputs['gan_generated_data'][:, :, -self.config.scroll_dim-self.config.context_dim:-self.config.context_dim],
                'context': component_outputs['gan_generated_data'][:, :, -self.config.context_dim:],
            })
        else:
            # Use transformer hidden states as fallback
            gan_hidden = transformer_hidden
        
        # Multi-scale temporal processing
        temporal_features = []
        for temporal_scale in self.temporal_scales:
            scale_features = temporal_scale(transformer_hidden)
            temporal_features.append(scale_features)
        
        multi_scale_features = torch.stack(temporal_features, dim=-1).mean(dim=-1)
        
        # Context-aware adaptation
        if 'context' in batch:
            adapted_features = self.context_adapter(multi_scale_features, batch['context'])
        else:
            adapted_features = multi_scale_features
        
        # Adaptive weighting of components
        component_features = torch.cat([
            transformer_hidden.mean(dim=1),  # Global pooling
            vae_hidden.mean(dim=1) if vae_hidden is not None else torch.zeros_like(transformer_hidden.mean(dim=1)),
            gan_hidden.mean(dim=1) if gan_hidden is not None else torch.zeros_like(transformer_hidden.mean(dim=1))
        ], dim=-1)
        
        weights = self.adaptive_weighting(adapted_features.mean(dim=1))
        
        # Weighted combination
        weighted_transformer = weights[:, 0:1].unsqueeze(1) * transformer_hidden
        weighted_vae = weights[:, 1:2].unsqueeze(1) * (vae_hidden if vae_hidden is not None else torch.zeros_like(transformer_hidden))
        weighted_gan = weights[:, 2:3].unsqueeze(1) * (gan_hidden if gan_hidden is not None else torch.zeros_like(transformer_hidden))
        
        combined_features = weighted_transformer + weighted_vae + weighted_gan
        
        # Fusion network
        fusion_input = torch.cat([
            combined_features,
            adapted_features,
            multi_scale_features
        ], dim=-1)
        
        fused_output = self.fusion_network(fusion_input)
        
        # Uncertainty estimation
        uncertainty = self.uncertainty_estimator(combined_features)
        
        return {
            'joint_output': fused_output,
            'component_weights': weights,
            'uncertainty': uncertainty,
            'multi_scale_features': multi_scale_features,
            'adapted_features': adapted_features,
        }
    
    def generate(self, context: Dict[str, torch.Tensor], length: int,
                generation_strategy: str = 'hybrid') -> Dict[str, torch.Tensor]:
        """
        Generate behavior sequences using hybrid approach.
        
        Args:
            context: Context information for generation
            length: Length of sequence to generate
            generation_strategy: 'transformer', 'vae', 'gan', or 'hybrid'
        """
        self.eval()
        
        with torch.no_grad():
            if generation_strategy == 'transformer':
                return self.transformer.generate(context, length)
            elif generation_strategy == 'vae':
                return self.vae.generate(context, length)
            elif generation_strategy == 'gan':
                batch_size = context['mouse'].shape[0]
                device = context['mouse'].device
                noise = self.gan.generate_noise(batch_size, device)
                generated = self.gan.forward_generator(noise, context.get('context'))
                
                # Convert to modality format
                return {
                    'mouse': generated[:, :, :self.config.mouse_dim],
                    'keyboard': generated[:, :, self.config.mouse_dim:self.config.mouse_dim+self.config.keyboard_dim],
                    'scroll': generated[:, :, -self.config.scroll_dim-self.config.context_dim:-self.config.context_dim],
                    'context': generated[:, :, -self.config.context_dim:],
                }
            else:  # hybrid
                return self._hybrid_generate(context, length)
    
    def _hybrid_generate(self, context: Dict[str, torch.Tensor], length: int) -> Dict[str, torch.Tensor]:
        """Hybrid generation combining all components."""
        # Generate from each component
        transformer_gen = self.transformer.generate(context, length)
        vae_gen = self.vae.generate(context, length)
        
        batch_size = context['mouse'].shape[0]
        device = context['mouse'].device
        noise = self.gan.generate_noise(batch_size, device)
        gan_output = self.gan.forward_generator(noise, context.get('context'))
        
        gan_gen = {
            'mouse': gan_output[:, :length, :self.config.mouse_dim],
            'keyboard': gan_output[:, :length, self.config.mouse_dim:self.config.mouse_dim+self.config.keyboard_dim],
            'scroll': gan_output[:, :length, -self.config.scroll_dim-self.config.context_dim:-self.config.context_dim],
            'context': gan_output[:, :length, -self.config.context_dim:],
        }
        
        # Combine generations using learned weights
        combined_batch = {
            'mouse': torch.stack([transformer_gen['mouse'], vae_gen['mouse'], gan_gen['mouse']], dim=-1),
            'keyboard': torch.stack([transformer_gen['keyboard'], vae_gen['keyboard'], gan_gen['keyboard']], dim=-1),
            'scroll': torch.stack([transformer_gen['scroll'], vae_gen['scroll'], gan_gen['scroll']], dim=-1),
            'context': torch.stack([transformer_gen['context'], vae_gen['context'], gan_gen['context']], dim=-1),
        }
        
        # Get adaptive weights
        embedded = self.embed_inputs({k: v.mean(dim=-1) for k, v in combined_batch.items()})
        weights = self.adaptive_weighting(embedded.mean(dim=1))  # [B, 3]
        
        # Weighted combination
        final_generation = {}
        for modality in ['mouse', 'keyboard', 'scroll', 'context']:
            weighted = (combined_batch[modality] * weights.unsqueeze(1).unsqueeze(1)).sum(dim=-1)
            final_generation[modality] = weighted
        
        return final_generation
    
    def compute_total_loss(self, outputs: Dict[str, torch.Tensor], 
                          batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Compute total loss combining all components."""
        losses = {}
        total_loss = 0
        
        # Transformer loss
        if 'transformer_predictions' in outputs:
            transformer_loss = F.mse_loss(
                outputs['transformer_predictions'], 
                self.embed_inputs(batch)
            )
            losses['transformer_loss'] = transformer_loss
            total_loss += self.transformer_weight * transformer_loss
        
        # VAE losses
        if 'vae_total_loss' in outputs:
            losses['vae_loss'] = outputs['vae_total_loss']
            total_loss += self.vae_weight * outputs['vae_total_loss']
        
        # GAN losses (handled separately in training loop)
        if 'gan_discriminator_score' in outputs:
            # This would be handled by the GAN training logic
            pass
        
        # Joint loss (if available)
        if 'joint_output' in outputs:
            joint_loss = F.mse_loss(outputs['joint_output'], self.embed_inputs(batch))
            losses['joint_loss'] = joint_loss
            total_loss += joint_loss
        
        # Uncertainty regularization
        if 'uncertainty' in outputs:
            uncertainty_reg = torch.mean(outputs['uncertainty'])
            losses['uncertainty_reg'] = uncertainty_reg
            total_loss += 0.01 * uncertainty_reg
        
        losses['total_loss'] = total_loss
        return losses


class FusionNetwork(nn.Module):
    """Network for fusing outputs from multiple components."""
    
    def __init__(self, input_dim: int, hidden_dim: int, output_dim: int, strategy: str = 'attention'):
        super().__init__()
        self.strategy = strategy
        
        if strategy == 'attention':
            self.attention = nn.MultiheadAttention(
                embed_dim=input_dim,
                num_heads=8,
                batch_first=True
            )
            self.projection = nn.Linear(input_dim, output_dim)
        elif strategy == 'mlp':
            self.mlp = nn.Sequential(
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(hidden_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(hidden_dim, output_dim)
            )
        else:
            raise ValueError(f"Unknown fusion strategy: {strategy}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Fuse multi-component features."""
        if self.strategy == 'attention':
            attn_output, _ = self.attention(x, x, x)
            return self.projection(attn_output)
        else:
            return self.mlp(x)


class UncertaintyEstimator(nn.Module):
    """Estimate uncertainty in model predictions."""
    
    def __init__(self, input_dim: int, hidden_dim: int):
        super().__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Estimate uncertainty."""
        return self.network(x.mean(dim=1))  # Global pooling


class AdaptiveWeighting(nn.Module):
    """Learn adaptive weights for component combination."""
    
    def __init__(self, input_dim: int, num_components: int):
        super().__init__()
        
        self.network = nn.Sequential(
            nn.Linear(input_dim, input_dim // 2),
            nn.ReLU(),
            nn.Linear(input_dim // 2, num_components),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Compute adaptive weights."""
        return self.network(x)


class TemporalScale(nn.Module):
    """Multi-scale temporal convolution."""
    
    def __init__(self, input_dim: int, hidden_dim: int, kernel_size: int, dilation: int):
        super().__init__()
        
        self.conv = nn.Conv1d(
            in_channels=input_dim,
            out_channels=hidden_dim,
            kernel_size=kernel_size,
            dilation=dilation,
            padding=(kernel_size - 1) * dilation // 2
        )
        self.norm = nn.LayerNorm(hidden_dim)
        self.activation = nn.GELU()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Apply temporal convolution."""
        # x shape: [B, T, H]
        x_conv = x.transpose(1, 2)  # [B, H, T]
        x_conv = self.conv(x_conv)
        x_conv = x_conv.transpose(1, 2)  # [B, T, H]
        
        return self.activation(self.norm(x_conv))


class ContextAdapter(nn.Module):
    """Adapt features based on context information."""
    
    def __init__(self, context_dim: int, hidden_dim: int, num_adaptation_layers: int):
        super().__init__()
        
        self.context_encoder = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        self.adaptation_layers = nn.ModuleList([
            nn.Linear(hidden_dim, hidden_dim)
            for _ in range(num_adaptation_layers)
        ])
        
        self.gate = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Sigmoid()
        )
    
    def forward(self, features: torch.Tensor, context: torch.Tensor) -> torch.Tensor:
        """Adapt features based on context."""
        # Encode context
        if context.dim() == 3:  # [B, T, C]
            context = context.mean(dim=1)  # [B, C]
        
        context_encoded = self.context_encoder(context)  # [B, H]
        
        # Expand context to match feature dimensions
        context_expanded = context_encoded.unsqueeze(1).expand_as(features)  # [B, T, H]
        
        # Compute adaptation gate
        combined = torch.cat([features, context_expanded], dim=-1)
        gate = self.gate(combined)
        
        # Apply context-aware adaptation
        adapted = features * gate + context_expanded * (1 - gate)
        
        return adapted
