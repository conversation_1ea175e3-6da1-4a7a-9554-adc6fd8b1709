torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0
transformers>=4.35.0
accelerate>=0.24.0
datasets>=2.14.0
numpy>=1.24.0
scipy>=1.11.0
pandas>=2.0.0
scikit-learn>=1.3.0
lightning>=2.1.0
torchmetrics>=1.2.0
timm>=0.9.0
einops>=0.7.0
optuna>=3.4.0
ray[tune]>=2.8.0
wandb>=0.16.0
tensorboard>=2.15.0
hydra-core>=1.3.0
omegaconf>=2.3.0
h5py>=3.9.0
zarr>=2.16.0
pyarrow>=14.0.0
fastparquet>=2023.10.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0
bokeh>=3.3.0
nodriver>=0.28
selenium>=4.15.0
requests>=2.31.0
aiohttp>=3.9.0
tqdm>=4.66.0
rich>=13.7.0
click>=8.1.0
pydantic>=2.5.0
python-dotenv>=1.0.0
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0
pre-commit>=3.5.0
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0
psutil>=5.9.0
nvidia-ml-py>=12.535.0
gpustat>=1.1.0
asyncio>=3.4.3
aiofiles>=23.2.0
uvloop>=0.19.0
pickle5>=0.0.12
joblib>=1.3.0
cloudpickle>=3.0.0
httpx>=0.25.0
websockets>=12.0
psutil>=5.9.0
py-cpuinfo>=9.0.0

[all]
torchaudio>=2.1.0
sphinx-rtd-theme>=1.3.0
flake8>=6.1.0
pytest-asyncio>=0.21.0
black>=23.11.0
torchvision>=0.16.0
pre-commit>=3.5.0
deepspeed>=0.12.0
pytest>=7.4.0
mypy>=1.7.0
fairscale>=0.4.13
isort>=5.12.0
pytest-cov>=4.1.0
sphinx>=7.2.0
myst-parser>=2.0.0
torch>=2.1.0

[dev]
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0
pre-commit>=3.5.0

[distributed]
deepspeed>=0.12.0
fairscale>=0.4.13

[docs]
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

[gpu]
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0
