"""
Hybrid trainer for multi-component human behavior AI models.

This module implements specialized training for the hybrid architecture
that combines transformers, VAEs, GANs, and RL components.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, Tuple
import numpy as np

from .base import BaseTrainer, TrainingConfig
from ..models.hybrid import HybridBehaviorModel
from ..models.base import ModelConfig


class HybridTrainingConfig(TrainingConfig):
    """Extended configuration for hybrid model training."""
    
    # Component loss weights
    reconstruction_weight: float = 1.0
    kl_weight: float = 0.1
    adversarial_weight: float = 0.5
    rl_weight: float = 0.3
    consistency_weight: float = 0.2
    
    # Training phases
    warmup_vae_epochs: int = 10
    warmup_gan_epochs: int = 20
    full_training_epochs: int = 70
    
    # Component-specific learning rates
    vae_lr_multiplier: float = 1.0
    gan_lr_multiplier: float = 0.5
    rl_lr_multiplier: float = 2.0
    
    # Advanced training techniques
    use_curriculum_learning: bool = True
    use_progressive_training: bool = True
    use_component_scheduling: bool = True


class HybridBehaviorTrainer(BaseTrainer):
    """
    Specialized trainer for hybrid behavior models.
    
    Implements multi-stage training with component-specific optimization
    and advanced techniques for stable multi-component learning.
    """
    
    def __init__(
        self,
        model: HybridBehaviorModel,
        config: HybridTrainingConfig,
        train_dataloader: DataLoader,
        val_dataloader: Optional[DataLoader] = None,
        test_dataloader: Optional[DataLoader] = None
    ):
        super().__init__(model, config, train_dataloader, val_dataloader, test_dataloader)
        
        self.hybrid_config = config
        
        # Setup component-specific optimizers
        self._setup_component_optimizers()
        
        # Training phase tracking
        self.current_phase = "warmup_vae"
        self.phase_step = 0
        
        # Component loss tracking
        self.loss_history = {
            'reconstruction': [],
            'kl_divergence': [],
            'adversarial': [],
            'rl_reward': [],
            'consistency': []
        }
    
    def _setup_component_optimizers(self):
        """Setup separate optimizers for different model components."""
        base_lr = self.config.learning_rate
        
        # VAE optimizer
        vae_params = list(self.model.vae.parameters())
        self.vae_optimizer = torch.optim.AdamW(
            vae_params,
            lr=base_lr * self.hybrid_config.vae_lr_multiplier,
            weight_decay=self.config.weight_decay
        )
        
        # GAN optimizer (separate for generator and discriminator)
        gan_gen_params = list(self.model.gan.generator.parameters())
        gan_disc_params = list(self.model.gan.discriminator.parameters())
        
        self.gan_gen_optimizer = torch.optim.AdamW(
            gan_gen_params,
            lr=base_lr * self.hybrid_config.gan_lr_multiplier,
            weight_decay=self.config.weight_decay
        )
        
        self.gan_disc_optimizer = torch.optim.AdamW(
            gan_disc_params,
            lr=base_lr * self.hybrid_config.gan_lr_multiplier,
            weight_decay=self.config.weight_decay
        )
        
        # RL optimizer
        if hasattr(self.model, 'rl_component') and self.model.rl_component is not None:
            rl_params = list(self.model.rl_component.parameters())
            self.rl_optimizer = torch.optim.AdamW(
                rl_params,
                lr=base_lr * self.hybrid_config.rl_lr_multiplier,
                weight_decay=self.config.weight_decay
            )
    
    def _get_current_phase_weights(self) -> Dict[str, float]:
        """Get loss weights based on current training phase."""
        if self.current_phase == "warmup_vae":
            return {
                'reconstruction': 1.0,
                'kl_divergence': 0.1,
                'adversarial': 0.0,
                'rl_reward': 0.0,
                'consistency': 0.1
            }
        elif self.current_phase == "warmup_gan":
            return {
                'reconstruction': 0.8,
                'kl_divergence': 0.1,
                'adversarial': 0.5,
                'rl_reward': 0.0,
                'consistency': 0.2
            }
        else:  # full_training
            return {
                'reconstruction': self.hybrid_config.reconstruction_weight,
                'kl_divergence': self.hybrid_config.kl_weight,
                'adversarial': self.hybrid_config.adversarial_weight,
                'rl_reward': self.hybrid_config.rl_weight,
                'consistency': self.hybrid_config.consistency_weight
            }
    
    def _update_training_phase(self):
        """Update training phase based on current epoch."""
        if self.current_epoch < self.hybrid_config.warmup_vae_epochs:
            self.current_phase = "warmup_vae"
        elif self.current_epoch < (self.hybrid_config.warmup_vae_epochs + 
                                  self.hybrid_config.warmup_gan_epochs):
            self.current_phase = "warmup_gan"
        else:
            self.current_phase = "full_training"
    
    def compute_loss(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Compute multi-component loss for hybrid model.
        
        Args:
            batch: Dictionary containing batch data with keys:
                - 'sequences': Input behavioral sequences
                - 'targets': Target sequences (for supervised learning)
                - 'rewards': Reward signals (for RL training)
                
        Returns:
            Dictionary containing all loss components
        """
        sequences = batch['sequences']
        batch_size, seq_len, feature_dim = sequences.shape
        
        # Update training phase
        self._update_training_phase()
        phase_weights = self._get_current_phase_weights()
        
        # Forward pass through hybrid model
        model_output = self.model(sequences)
        
        # Extract components
        reconstructed = model_output['reconstructed']
        latent_mean = model_output['latent_mean']
        latent_logvar = model_output['latent_logvar']
        generated = model_output['generated']
        
        # 1. Reconstruction loss (VAE component)
        reconstruction_loss = F.mse_loss(reconstructed, sequences)
        
        # 2. KL divergence loss (VAE component)
        kl_loss = -0.5 * torch.sum(1 + latent_logvar - latent_mean.pow(2) - latent_logvar.exp())
        kl_loss = kl_loss / (batch_size * seq_len)
        
        # 3. Adversarial loss (GAN component)
        if self.current_phase != "warmup_vae":
            # Generator loss (fool discriminator)
            fake_scores = self.model.gan.discriminator(generated)
            gen_loss = -torch.mean(fake_scores)
            
            # Discriminator loss
            real_scores = self.model.gan.discriminator(sequences)
            fake_scores_detached = self.model.gan.discriminator(generated.detach())
            disc_loss = torch.mean(fake_scores_detached) - torch.mean(real_scores)
            
            # Gradient penalty for WGAN-GP
            gradient_penalty = self._compute_gradient_penalty(sequences, generated.detach())
            disc_loss += 10.0 * gradient_penalty
            
            adversarial_loss = gen_loss
        else:
            adversarial_loss = torch.tensor(0.0, device=sequences.device)
            disc_loss = torch.tensor(0.0, device=sequences.device)
        
        # 4. RL reward loss (if RL component exists)
        if (hasattr(self.model, 'rl_component') and 
            self.model.rl_component is not None and 
            self.current_phase == "full_training"):
            
            # Extract behavioral features for RL
            behavioral_features = model_output.get('behavioral_features', latent_mean)
            
            # Compute RL loss (policy gradient or actor-critic)
            if 'rewards' in batch:
                rewards = batch['rewards']
                rl_loss = self._compute_rl_loss(behavioral_features, rewards)
            else:
                # Use intrinsic reward based on behavioral quality
                intrinsic_rewards = self._compute_intrinsic_rewards(behavioral_features)
                rl_loss = self._compute_rl_loss(behavioral_features, intrinsic_rewards)
        else:
            rl_loss = torch.tensor(0.0, device=sequences.device)
        
        # 5. Consistency loss (cross-component alignment)
        consistency_loss = self._compute_consistency_loss(model_output)
        
        # Combine losses with phase-specific weights
        total_loss = (
            phase_weights['reconstruction'] * reconstruction_loss +
            phase_weights['kl_divergence'] * kl_loss +
            phase_weights['adversarial'] * adversarial_loss +
            phase_weights['rl_reward'] * rl_loss +
            phase_weights['consistency'] * consistency_loss
        )
        
        # Store loss components for tracking
        loss_dict = {
            'total_loss': total_loss,
            'reconstruction_loss': reconstruction_loss,
            'kl_loss': kl_loss,
            'adversarial_loss': adversarial_loss,
            'discriminator_loss': disc_loss if self.current_phase != "warmup_vae" else torch.tensor(0.0),
            'rl_loss': rl_loss,
            'consistency_loss': consistency_loss,
            'phase': self.current_phase
        }
        
        return loss_dict
    
    def _compute_gradient_penalty(self, real_data: torch.Tensor, fake_data: torch.Tensor) -> torch.Tensor:
        """Compute gradient penalty for WGAN-GP."""
        batch_size = real_data.size(0)
        alpha = torch.rand(batch_size, 1, 1, device=real_data.device)
        
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated.requires_grad_(True)
        
        disc_interpolated = self.model.gan.discriminator(interpolated)
        
        gradients = torch.autograd.grad(
            outputs=disc_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(disc_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        gradient_penalty = ((gradients.norm(2, dim=(1, 2)) - 1) ** 2).mean()
        return gradient_penalty
    
    def _compute_rl_loss(self, behavioral_features: torch.Tensor, rewards: torch.Tensor) -> torch.Tensor:
        """Compute reinforcement learning loss."""
        # Simplified RL loss - in practice, this would be more sophisticated
        # This is a placeholder for policy gradient or actor-critic loss
        
        # For now, use a simple reward prediction loss
        predicted_rewards = self.model.rl_component(behavioral_features)
        rl_loss = F.mse_loss(predicted_rewards.squeeze(), rewards)
        
        return rl_loss
    
    def _compute_intrinsic_rewards(self, behavioral_features: torch.Tensor) -> torch.Tensor:
        """Compute intrinsic rewards based on behavioral quality."""
        # Compute rewards based on:
        # 1. Naturalness (how human-like the behavior is)
        # 2. Diversity (avoiding repetitive patterns)
        # 3. Task completion (achieving objectives)
        
        batch_size = behavioral_features.size(0)
        
        # Naturalness reward (based on feature variance and smoothness)
        naturalness = torch.var(behavioral_features, dim=1).mean(dim=1)
        
        # Diversity reward (based on feature uniqueness)
        diversity = torch.pdist(behavioral_features.view(batch_size, -1)).mean()
        diversity = diversity.expand(batch_size)
        
        # Combine rewards
        intrinsic_rewards = 0.5 * naturalness + 0.5 * diversity
        
        return intrinsic_rewards
    
    def _compute_consistency_loss(self, model_output: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Compute consistency loss between different model components."""
        # Ensure different components produce consistent representations
        
        reconstructed = model_output['reconstructed']
        generated = model_output['generated']
        
        # Feature-level consistency
        recon_features = self.model.transformer(reconstructed)
        gen_features = self.model.transformer(generated)
        
        consistency_loss = F.mse_loss(recon_features, gen_features.detach())
        
        return consistency_loss
    
    def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Perform hybrid training step with component-specific optimization.
        """
        self.model.train()
        
        # Move batch to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                for k, v in batch.items()}
        
        # Compute losses
        if self.scaler is not None:
            with torch.cuda.amp.autocast():
                loss_dict = self.compute_loss(batch)
        else:
            loss_dict = self.compute_loss(batch)
        
        # Component-specific optimization
        self._optimize_components(loss_dict, batch)
        
        return loss_dict
    
    def _optimize_components(self, loss_dict: Dict[str, torch.Tensor], batch: Dict[str, torch.Tensor]):
        """Optimize different model components separately."""
        
        # 1. Optimize VAE components
        if loss_dict['reconstruction_loss'] > 0 or loss_dict['kl_loss'] > 0:
            vae_loss = loss_dict['reconstruction_loss'] + loss_dict['kl_loss']
            
            self.vae_optimizer.zero_grad()
            if self.scaler is not None:
                self.scaler.scale(vae_loss).backward(retain_graph=True)
                self.scaler.step(self.vae_optimizer)
            else:
                vae_loss.backward(retain_graph=True)
                self.vae_optimizer.step()
        
        # 2. Optimize GAN components
        if self.current_phase != "warmup_vae":
            # Optimize discriminator
            if loss_dict['discriminator_loss'] > 0:
                self.gan_disc_optimizer.zero_grad()
                if self.scaler is not None:
                    self.scaler.scale(loss_dict['discriminator_loss']).backward(retain_graph=True)
                    self.scaler.step(self.gan_disc_optimizer)
                else:
                    loss_dict['discriminator_loss'].backward(retain_graph=True)
                    self.gan_disc_optimizer.step()
            
            # Optimize generator
            if loss_dict['adversarial_loss'] > 0:
                self.gan_gen_optimizer.zero_grad()
                if self.scaler is not None:
                    self.scaler.scale(loss_dict['adversarial_loss']).backward(retain_graph=True)
                    self.scaler.step(self.gan_gen_optimizer)
                else:
                    loss_dict['adversarial_loss'].backward(retain_graph=True)
                    self.gan_gen_optimizer.step()
        
        # 3. Optimize RL component
        if (hasattr(self, 'rl_optimizer') and 
            self.current_phase == "full_training" and 
            loss_dict['rl_loss'] > 0):
            
            self.rl_optimizer.zero_grad()
            if self.scaler is not None:
                self.scaler.scale(loss_dict['rl_loss']).backward(retain_graph=True)
                self.scaler.step(self.rl_optimizer)
            else:
                loss_dict['rl_loss'].backward(retain_graph=True)
                self.rl_optimizer.step()
        
        # Update scaler if using mixed precision
        if self.scaler is not None:
            self.scaler.update()
    
    def validation_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Perform validation step."""
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                for k, v in batch.items()}
        
        with torch.no_grad():
            loss_dict = self.compute_loss(batch)
        
        return loss_dict
