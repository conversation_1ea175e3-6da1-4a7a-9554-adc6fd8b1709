"""
Device management utilities for HumanBehaviorAI.
"""

import torch
import logging
from typing import Dict, Any, Optional
import psutil
import platform

logger = logging.getLogger(__name__)


def get_device_info() -> Dict[str, Any]:
    """
    Get comprehensive device information.
    
    Returns:
        Dictionary containing device information
    """
    info = {
        'platform': platform.platform(),
        'python_version': platform.python_version(),
        'pytorch_version': torch.__version__,
        'cpu_count': psutil.cpu_count(),
        'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
        'cuda_available': torch.cuda.is_available(),
    }
    
    if torch.cuda.is_available():
        info.update({
            'cuda_version': torch.version.cuda,
            'cudnn_version': torch.backends.cudnn.version(),
            'cuda_device_count': torch.cuda.device_count(),
            'cuda_devices': []
        })
        
        for i in range(torch.cuda.device_count()):
            device_props = torch.cuda.get_device_properties(i)
            device_info = {
                'device_id': i,
                'name': device_props.name,
                'memory_total_gb': round(device_props.total_memory / (1024**3), 2),
                'compute_capability': f"{device_props.major}.{device_props.minor}",
                'multiprocessor_count': device_props.multi_processor_count,
            }
            info['cuda_devices'].append(device_info)
    
    return info


def get_optimal_device(prefer_cuda: bool = True) -> torch.device:
    """
    Get the optimal device for computation.
    
    Args:
        prefer_cuda: Whether to prefer CUDA if available
        
    Returns:
        Optimal torch device
    """
    if prefer_cuda and torch.cuda.is_available():
        device = torch.device('cuda')
        logger.info(f"Using CUDA device: {torch.cuda.get_device_name()}")
    else:
        device = torch.device('cpu')
        logger.info("Using CPU device")
    
    return device


def setup_cuda(device_id: Optional[int] = None, memory_fraction: float = 0.9) -> None:
    """
    Setup CUDA configuration.
    
    Args:
        device_id: Specific CUDA device ID to use (None for default)
        memory_fraction: Fraction of GPU memory to allocate
    """
    if not torch.cuda.is_available():
        logger.warning("CUDA not available, skipping CUDA setup")
        return
    
    if device_id is not None:
        torch.cuda.set_device(device_id)
        logger.info(f"Set CUDA device to: {device_id}")
    
    # Set memory fraction if supported
    try:
        if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
            torch.cuda.set_per_process_memory_fraction(memory_fraction)
            logger.info(f"Set CUDA memory fraction to: {memory_fraction}")
    except Exception as e:
        logger.warning(f"Could not set CUDA memory fraction: {e}")
    
    # Enable optimizations
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    
    logger.info("CUDA setup complete")


def get_memory_usage() -> Dict[str, float]:
    """
    Get current memory usage information.
    
    Returns:
        Dictionary with memory usage information
    """
    memory_info = {
        'cpu_memory_used_gb': round(psutil.virtual_memory().used / (1024**3), 2),
        'cpu_memory_percent': psutil.virtual_memory().percent,
    }
    
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
            memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
            memory_info[f'cuda_{i}_allocated_gb'] = round(memory_allocated, 2)
            memory_info[f'cuda_{i}_reserved_gb'] = round(memory_reserved, 2)
    
    return memory_info


def clear_cuda_cache() -> None:
    """Clear CUDA cache to free up memory."""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        logger.info("CUDA cache cleared")


def log_device_info() -> None:
    """Log comprehensive device information."""
    info = get_device_info()
    
    logger.info("=== Device Information ===")
    logger.info(f"Platform: {info['platform']}")
    logger.info(f"Python: {info['python_version']}")
    logger.info(f"PyTorch: {info['pytorch_version']}")
    logger.info(f"CPU Count: {info['cpu_count']}")
    logger.info(f"Memory: {info['memory_total_gb']} GB")
    
    if info['cuda_available']:
        logger.info(f"CUDA: Available (version {info['cuda_version']})")
        logger.info(f"cuDNN: {info['cudnn_version']}")
        logger.info(f"CUDA Devices: {info['cuda_device_count']}")
        
        for device in info['cuda_devices']:
            logger.info(f"  Device {device['device_id']}: {device['name']}")
            logger.info(f"    Memory: {device['memory_total_gb']} GB")
            logger.info(f"    Compute: {device['compute_capability']}")
    else:
        logger.info("CUDA: Not available")
    
    logger.info("==========================")
