"""
Visualization utilities for HumanBehaviorAI.
"""

import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


def plot_training_curves(
    history: List[Dict[str, float]],
    metrics: Optional[List[str]] = None,
    save_path: Optional[Path] = None,
    show: bool = True
) -> plt.Figure:
    """
    Plot training curves from training history.
    
    Args:
        history: List of training history dictionaries
        metrics: List of metrics to plot. If None, plots all available metrics.
        save_path: Path to save the plot
        show: Whether to display the plot
        
    Returns:
        Matplotlib figure
    """
    if not history:
        logger.warning("No training history provided")
        return plt.figure()
    
    # Convert to DataFrame
    df = pd.DataFrame(history)
    
    # Get metrics to plot
    if metrics is None:
        metrics = [col for col in df.columns if col not in ['epoch', 'step']]
    
    # Create subplots
    n_metrics = len(metrics)
    n_cols = min(3, n_metrics)
    n_rows = (n_metrics + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(5 * n_cols, 4 * n_rows))
    if n_metrics == 1:
        axes = [axes]
    elif n_rows == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()
    
    # Plot each metric
    for i, metric in enumerate(metrics):
        if metric in df.columns:
            ax = axes[i]
            
            # Separate train and validation metrics
            if f'val_{metric}' in df.columns:
                ax.plot(df.index, df[metric], label=f'Train {metric}', linewidth=2)
                ax.plot(df.index, df[f'val_{metric}'], label=f'Val {metric}', linewidth=2)
            else:
                ax.plot(df.index, df[metric], label=metric, linewidth=2)
            
            ax.set_title(f'{metric.title()} Over Time')
            ax.set_xlabel('Epoch')
            ax.set_ylabel(metric.title())
            ax.legend()
            ax.grid(True, alpha=0.3)
    
    # Hide unused subplots
    for i in range(n_metrics, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Training curves saved to: {save_path}")
    
    if show:
        plt.show()
    
    return fig


def visualize_behavior_patterns(
    trajectories: np.ndarray,
    labels: Optional[np.ndarray] = None,
    max_trajectories: int = 10,
    save_path: Optional[Path] = None,
    show: bool = True
) -> plt.Figure:
    """
    Visualize behavior patterns from trajectories.
    
    Args:
        trajectories: Behavior trajectories [n_samples, seq_len, features]
        labels: Optional labels for trajectories
        max_trajectories: Maximum number of trajectories to plot
        save_path: Path to save the plot
        show: Whether to display the plot
        
    Returns:
        Matplotlib figure
    """
    n_samples, seq_len, n_features = trajectories.shape
    n_plot = min(max_trajectories, n_samples)
    
    # Create subplots for different features
    n_cols = min(3, n_features)
    n_rows = (n_features + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(5 * n_cols, 4 * n_rows))
    if n_features == 1:
        axes = [axes]
    elif n_rows == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()
    
    # Plot trajectories for each feature
    for feature_idx in range(n_features):
        ax = axes[feature_idx]
        
        for i in range(n_plot):
            trajectory = trajectories[i, :, feature_idx]
            label = f'Traj {i}' if labels is None else f'Traj {i} ({labels[i]})'
            ax.plot(trajectory, alpha=0.7, linewidth=1.5, label=label)
        
        ax.set_title(f'Feature {feature_idx} Trajectories')
        ax.set_xlabel('Time Step')
        ax.set_ylabel(f'Feature {feature_idx} Value')
        ax.grid(True, alpha=0.3)
        
        if n_plot <= 5:  # Only show legend for small number of trajectories
            ax.legend()
    
    # Hide unused subplots
    for i in range(n_features, len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Behavior patterns saved to: {save_path}")
    
    if show:
        plt.show()
    
    return fig


def plot_attention_weights(
    attention_weights: np.ndarray,
    input_tokens: Optional[List[str]] = None,
    save_path: Optional[Path] = None,
    show: bool = True
) -> plt.Figure:
    """
    Plot attention weights as a heatmap.
    
    Args:
        attention_weights: Attention weights [seq_len, seq_len]
        input_tokens: Optional list of input token labels
        save_path: Path to save the plot
        show: Whether to display the plot
        
    Returns:
        Matplotlib figure
    """
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create heatmap
    sns.heatmap(
        attention_weights,
        annot=False,
        cmap='Blues',
        ax=ax,
        cbar_kws={'label': 'Attention Weight'}
    )
    
    # Set labels if provided
    if input_tokens:
        ax.set_xticklabels(input_tokens, rotation=45, ha='right')
        ax.set_yticklabels(input_tokens, rotation=0)
    
    ax.set_title('Attention Weights Heatmap')
    ax.set_xlabel('Key Position')
    ax.set_ylabel('Query Position')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Attention weights saved to: {save_path}")
    
    if show:
        plt.show()
    
    return fig


def plot_loss_landscape(
    loss_values: np.ndarray,
    param_ranges: Tuple[np.ndarray, np.ndarray],
    param_names: Tuple[str, str],
    save_path: Optional[Path] = None,
    show: bool = True
) -> plt.Figure:
    """
    Plot loss landscape for parameter exploration.
    
    Args:
        loss_values: Loss values [n_points_x, n_points_y]
        param_ranges: Tuple of parameter ranges (x_range, y_range)
        param_names: Tuple of parameter names (x_name, y_name)
        save_path: Path to save the plot
        show: Whether to display the plot
        
    Returns:
        Matplotlib figure
    """
    fig, ax = plt.subplots(figsize=(10, 8))
    
    x_range, y_range = param_ranges
    x_name, y_name = param_names
    
    # Create contour plot
    contour = ax.contourf(x_range, y_range, loss_values, levels=20, cmap='viridis')
    ax.contour(x_range, y_range, loss_values, levels=20, colors='black', alpha=0.3, linewidths=0.5)
    
    # Add colorbar
    cbar = plt.colorbar(contour, ax=ax)
    cbar.set_label('Loss Value')
    
    ax.set_xlabel(x_name)
    ax.set_ylabel(y_name)
    ax.set_title('Loss Landscape')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Loss landscape saved to: {save_path}")
    
    if show:
        plt.show()
    
    return fig


def create_training_dashboard(
    history: List[Dict[str, float]],
    trajectories: Optional[np.ndarray] = None,
    save_path: Optional[Path] = None,
    show: bool = True
) -> plt.Figure:
    """
    Create a comprehensive training dashboard.
    
    Args:
        history: Training history
        trajectories: Optional behavior trajectories to visualize
        save_path: Path to save the dashboard
        show: Whether to display the dashboard
        
    Returns:
        Matplotlib figure
    """
    fig = plt.figure(figsize=(20, 12))
    
    # Training curves (top half)
    ax1 = plt.subplot(2, 3, (1, 3))
    if history:
        df = pd.DataFrame(history)
        metrics = [col for col in df.columns if col not in ['epoch', 'step']]
        
        for metric in metrics[:4]:  # Plot up to 4 metrics
            if metric in df.columns:
                ax1.plot(df.index, df[metric], label=metric, linewidth=2)
        
        ax1.set_title('Training Metrics Over Time')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Metric Value')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
    
    # Behavior trajectories (bottom left)
    if trajectories is not None:
        ax2 = plt.subplot(2, 3, 4)
        n_samples = min(5, trajectories.shape[0])
        for i in range(n_samples):
            ax2.plot(trajectories[i, :, 0], alpha=0.7, linewidth=1.5)
        ax2.set_title('Sample Behavior Trajectories')
        ax2.set_xlabel('Time Step')
        ax2.set_ylabel('Feature Value')
        ax2.grid(True, alpha=0.3)
    
    # Loss distribution (bottom middle)
    ax3 = plt.subplot(2, 3, 5)
    if history:
        losses = [h.get('train_loss', 0) for h in history if 'train_loss' in h]
        if losses:
            ax3.hist(losses, bins=20, alpha=0.7, edgecolor='black')
            ax3.set_title('Training Loss Distribution')
            ax3.set_xlabel('Loss Value')
            ax3.set_ylabel('Frequency')
            ax3.grid(True, alpha=0.3)
    
    # Learning rate schedule (bottom right)
    ax4 = plt.subplot(2, 3, 6)
    if history:
        lrs = [h.get('learning_rate', 0) for h in history if 'learning_rate' in h]
        if lrs:
            ax4.plot(lrs, linewidth=2, color='orange')
            ax4.set_title('Learning Rate Schedule')
            ax4.set_xlabel('Epoch')
            ax4.set_ylabel('Learning Rate')
            ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Training dashboard saved to: {save_path}")
    
    if show:
        plt.show()
    
    return fig
