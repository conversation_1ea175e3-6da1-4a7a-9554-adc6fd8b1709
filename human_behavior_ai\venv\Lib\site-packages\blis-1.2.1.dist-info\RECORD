blis-1.2.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
blis-1.2.1.dist-info/METADATA,sha256=D8b1jTlbxl0cd06dU7EQ3mTp3PJbxUg-lSc4H3Ahy6I,7603
blis-1.2.1.dist-info/RECORD,,
blis-1.2.1.dist-info/WHEEL,sha256=ovhA9_Ei_7ok2fAych90j-feDV4goiAxbO7REePtvw0,101
blis-1.2.1.dist-info/licenses/LICENSE,sha256=GoFvQzlw5PMnaNMONhi_MYR1Kq18h-8QeW1xDk-GU7Y,2077
blis-1.2.1.dist-info/top_level.txt,sha256=yqZIBDPqq9RefHu0PWPWmOQfhhKsBy120eoLTMlmXkA,5
blis/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
blis/__init__.py,sha256=bV8lTy__5O9CrmYGsSuV6B859iW8ygR884ty09UIwHo,84
blis/__pycache__/__init__.cpython-312.pyc,,
blis/__pycache__/about.cpython-312.pyc,,
blis/__pycache__/benchmark.cpython-312.pyc,,
blis/about.py,sha256=pq_tzvFNOZtiZyMVWp4njCUyhDcplsJxltB97hm1HBs,558
blis/benchmark.py,sha256=3XTpfeu37Hku82u2XoIV_DgbBZVvLVnA5yyVbd2cDAk,2766
blis/cy.cp312-win_amd64.exp,sha256=FFMtqGeOnGbySUJ28KFJgjv5hNLvjKT435mbp2k4LG0,732
blis/cy.cp312-win_amd64.lib,sha256=7CLxxE8Wb016IbaHMNJo9NejsIiq95Nv-M4GGT7KHB0,1924
blis/cy.cp312-win_amd64.pyd,sha256=3sm-Q08DBARXQwJWJbSSrFjQ6fd7YK-gKl9bsnYuRDY,11316224
blis/cy.pxd,sha256=OtasmdBXQu4jT_i92WErB4B-SsGw006iTiML5qpNg80,3807
blis/cy.pyx,sha256=mFKqkayNWz4dvomlmRLdjwwsZj9X334CbYGezZb7Zoc,17559
blis/py.cp312-win_amd64.exp,sha256=Zv6uncM7tkgjYBFyyQKi6k8Bjz3IaA0VObL73VgNhdw,732
blis/py.cp312-win_amd64.lib,sha256=1t_d8A6OQ9ra0TV-F-lROtTeh3lcJSJ4iLo1IGO3Yb0,1924
blis/py.cp312-win_amd64.pyd,sha256=ftpGWY4OyIXDboPJetgX4BvJqs1e2FEkR8P2W7degmg,11533824
blis/py.pyx,sha256=_Eo-ggF9fnpfoajZUW8ATPesSe5YZAH6P9_EXLB8n2M,7250
blis/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
blis/tests/__pycache__/__init__.cpython-312.pyc,,
blis/tests/__pycache__/common.cpython-312.pyc,,
blis/tests/__pycache__/test_dotv.cpython-312.pyc,,
blis/tests/__pycache__/test_gemm.cpython-312.pyc,,
blis/tests/common.py,sha256=V6G1fzZpfIZl-ICmk7Yc0OxFyE37I0_AuCUnC7lU6p8,2662
blis/tests/test_dotv.py,sha256=5xcvRk0zRxu1Psqceg0fzTktDS7giG8q3zvquwpRals,1172
blis/tests/test_gemm.py,sha256=TKd6XpywJGdEhuGZXRd7ckJMDTH5wUtKXccbF6tfius,2568
