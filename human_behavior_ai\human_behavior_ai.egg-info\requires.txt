torch>=2.4.0
torchvision>=0.19.0
torchaudio>=2.4.0
transformers>=4.45.0
accelerate>=0.34.0
datasets>=2.14.0
numpy<2.0.0,>=1.24.0
scipy>=1.11.0
pandas>=2.0.0
scikit-learn>=1.3.0
lightning>=2.1.0
torchmetrics>=1.2.0
timm>=1.0.0
einops>=0.8.0
optuna>=3.6.0
ray[tune]>=2.30.0
wandb>=0.18.0
tensorboard>=2.17.0
hydra-core>=1.3.0
omegaconf>=2.3.0
h5py>=3.9.0
zarr>=2.16.0
pyarrow>=14.0.0
fastparquet>=2023.10.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0
bokeh>=3.3.0
nodriver>=0.37.0
selenium>=4.25.0
requests>=2.31.0
aiohttp>=3.10.0
tqdm>=4.66.0
rich>=13.7.0
click>=8.1.0
pydantic>=2.5.0
python-dotenv>=1.0.0
pytest>=8.0.0
pytest-asyncio>=0.23.0
pytest-cov>=4.1.0
black>=24.0.0
isort>=5.13.0
flake8>=7.0.0
mypy>=1.7.0
pre-commit>=3.5.0
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0
psutil>=6.0.0
nvidia-ml-py>=12.535.0
gpustat>=1.1.0
aiofiles>=24.0.0
joblib>=1.4.0
cloudpickle>=3.1.0
httpx>=0.25.0
websockets>=12.0
py-cpuinfo>=9.0.0
fairscale>=0.4.13
xgboost>=2.1.0
lightgbm>=4.5.0
catboost>=1.2.0
pillow>=10.4.0
opencv-python>=4.10.0
librosa>=0.10.0
soundfile>=0.12.0
nltk>=3.9.0
spacy>=3.8.0
tokenizers>=0.20.0

[all]
torchvision>=0.16.0
sphinx>=7.2.0
myst-parser>=2.0.0
sphinx-rtd-theme>=1.3.0
flake8>=6.1.0
black>=23.11.0
isort>=5.12.0
pytest-asyncio>=0.21.0
torch>=2.1.0
fairscale>=0.4.13
pytest>=7.4.0
mypy>=1.7.0
torchaudio>=2.1.0
pre-commit>=3.5.0
pytest-cov>=4.1.0

[dev]
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0
pre-commit>=3.5.0

[distributed]
fairscale>=0.4.13

[docs]
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

[gpu]
torch>=2.1.0
torchvision>=0.16.0
torchaudio>=2.1.0
