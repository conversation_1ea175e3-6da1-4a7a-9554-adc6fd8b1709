"""
Reinforcement learning trainer for adaptive behavioral strategies.

This module implements RL training for learning optimal behavioral
patterns that adapt to different contexts and environments.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, Tuple, List, Deque
from collections import deque
import numpy as np
import random

from .base import BaseTrainer, TrainingConfig
from ..models.rl import ActorCritic, BehavioralRewardModel


class ReinforcementTrainingConfig(TrainingConfig):
    """Configuration for reinforcement learning training."""
    
    # RL-specific parameters
    gamma: float = 0.99  # Discount factor
    gae_lambda: float = 0.95  # GAE parameter
    clip_epsilon: float = 0.2  # PPO clipping parameter
    entropy_coeff: float = 0.01  # Entropy regularization
    value_loss_coeff: float = 0.5  # Value function loss coefficient
    
    # Training parameters
    ppo_epochs: int = 4  # PPO update epochs per batch
    mini_batch_size: int = 64
    max_grad_norm: float = 0.5
    
    # Experience collection
    rollout_length: int = 2048  # Steps per rollout
    num_envs: int = 1  # Number of parallel environments
    
    # Reward shaping
    use_intrinsic_rewards: bool = True
    intrinsic_reward_weight: float = 0.1
    use_curiosity: bool = True
    curiosity_weight: float = 0.1
    
    # Exploration
    exploration_noise: float = 0.1
    exploration_decay: float = 0.995
    min_exploration: float = 0.01


class ExperienceBuffer:
    """Buffer for storing RL experiences."""
    
    def __init__(self, capacity: int = 10000):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state: torch.Tensor, action: torch.Tensor, reward: float,
             next_state: torch.Tensor, done: bool, log_prob: float, value: float):
        """Add experience to buffer."""
        experience = {
            'state': state,
            'action': action,
            'reward': reward,
            'next_state': next_state,
            'done': done,
            'log_prob': log_prob,
            'value': value
        }
        self.buffer.append(experience)
    
    def sample(self, batch_size: int) -> Dict[str, torch.Tensor]:
        """Sample batch of experiences."""
        batch = random.sample(self.buffer, min(batch_size, len(self.buffer)))
        
        # Stack experiences
        states = torch.stack([exp['state'] for exp in batch])
        actions = torch.stack([exp['action'] for exp in batch])
        rewards = torch.tensor([exp['reward'] for exp in batch], dtype=torch.float32)
        next_states = torch.stack([exp['next_state'] for exp in batch])
        dones = torch.tensor([exp['done'] for exp in batch], dtype=torch.bool)
        log_probs = torch.tensor([exp['log_prob'] for exp in batch], dtype=torch.float32)
        values = torch.tensor([exp['value'] for exp in batch], dtype=torch.float32)
        
        return {
            'states': states,
            'actions': actions,
            'rewards': rewards,
            'next_states': next_states,
            'dones': dones,
            'log_probs': log_probs,
            'values': values
        }
    
    def __len__(self):
        return len(self.buffer)


class ReinforcementTrainer(BaseTrainer):
    """
    Reinforcement learning trainer for behavioral models.
    
    Implements PPO (Proximal Policy Optimization) with additional
    techniques for learning human-like behavioral patterns.
    """
    
    def __init__(
        self,
        model: ActorCritic,
        config: ReinforcementTrainingConfig,
        train_dataloader: Optional[DataLoader] = None,
        val_dataloader: Optional[DataLoader] = None,
        test_dataloader: Optional[DataLoader] = None
    ):
        # Note: RL training doesn't always use traditional dataloaders
        super().__init__(model, config, train_dataloader, val_dataloader, test_dataloader)
        
        self.rl_config = config
        
        # Initialize reward model
        self.reward_model = BehavioralRewardModel(
            feature_dim=model.config.hidden_dim
        ).to(self.device)
        
        # Experience buffer
        self.experience_buffer = ExperienceBuffer(capacity=config.rollout_length * 2)
        
        # Training statistics
        self.episode_rewards = []
        self.episode_lengths = []
        self.policy_losses = []
        self.value_losses = []
        self.entropy_losses = []
        
        # Current exploration noise
        self.current_exploration = config.exploration_noise
        
        # Episode tracking
        self.current_episode_reward = 0.0
        self.current_episode_length = 0
    
    def compute_loss(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Compute PPO loss for actor-critic model.
        
        Args:
            batch: Dictionary containing experience batch
            
        Returns:
            Dictionary containing loss components
        """
        states = batch['states']
        actions = batch['actions']
        old_log_probs = batch['log_probs']
        rewards = batch['rewards']
        values = batch['values']
        advantages = batch.get('advantages', torch.zeros_like(rewards))
        returns = batch.get('returns', rewards)
        
        # Forward pass through current policy
        new_log_probs, entropy, new_values = self.model.evaluate(states, actions)
        
        # Policy loss (PPO clipping)
        ratio = torch.exp(new_log_probs - old_log_probs)
        surr1 = ratio * advantages
        surr2 = torch.clamp(ratio, 1 - self.rl_config.clip_epsilon, 
                           1 + self.rl_config.clip_epsilon) * advantages
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # Value loss
        value_loss = F.mse_loss(new_values, returns)
        
        # Entropy loss (for exploration)
        entropy_loss = -entropy.mean()
        
        # Combined loss
        total_loss = (
            policy_loss +
            self.rl_config.value_loss_coeff * value_loss +
            self.rl_config.entropy_coeff * entropy_loss
        )
        
        return {
            'total_loss': total_loss,
            'policy_loss': policy_loss,
            'value_loss': value_loss,
            'entropy_loss': entropy_loss,
            'mean_advantage': advantages.mean(),
            'mean_return': returns.mean()
        }
    
    def compute_advantages_and_returns(self, experiences: List[Dict]) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Compute advantages and returns using GAE (Generalized Advantage Estimation).
        
        Args:
            experiences: List of experience dictionaries
            
        Returns:
            Tuple of (advantages, returns)
        """
        rewards = torch.tensor([exp['reward'] for exp in experiences], dtype=torch.float32)
        values = torch.tensor([exp['value'] for exp in experiences], dtype=torch.float32)
        dones = torch.tensor([exp['done'] for exp in experiences], dtype=torch.bool)
        
        advantages = torch.zeros_like(rewards)
        returns = torch.zeros_like(rewards)
        
        # Compute advantages using GAE
        gae = 0
        for t in reversed(range(len(experiences))):
            if t == len(experiences) - 1:
                next_value = 0.0 if dones[t] else values[t]
            else:
                next_value = values[t + 1]
            
            delta = rewards[t] + self.rl_config.gamma * next_value - values[t]
            gae = delta + self.rl_config.gamma * self.rl_config.gae_lambda * gae * (1 - dones[t])
            advantages[t] = gae
        
        # Compute returns
        returns = advantages + values
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        return advantages, returns
    
    def collect_rollout(self, num_steps: int) -> List[Dict]:
        """
        Collect rollout experiences from environment interaction.
        
        Args:
            num_steps: Number of steps to collect
            
        Returns:
            List of experience dictionaries
        """
        experiences = []
        
        # Initialize state (this would typically come from an environment)
        # For now, we'll simulate behavioral states
        state = self._generate_initial_state()
        
        for step in range(num_steps):
            # Get action from policy
            with torch.no_grad():
                action, log_prob, value = self.model.act(state.unsqueeze(0))
                action = action.squeeze(0)
                log_prob = log_prob.item()
                value = value.item()
            
            # Add exploration noise
            if self.training:
                noise = torch.randn_like(action) * self.current_exploration
                action = action + noise
                action = torch.clamp(action, -1, 1)  # Assume normalized action space
            
            # Execute action in environment (simulated)
            next_state, reward, done = self._simulate_environment_step(state, action)
            
            # Store experience
            experiences.append({
                'state': state,
                'action': action,
                'reward': reward,
                'next_state': next_state,
                'done': done,
                'log_prob': log_prob,
                'value': value
            })
            
            # Update episode tracking
            self.current_episode_reward += reward
            self.current_episode_length += 1
            
            # Handle episode termination
            if done:
                self.episode_rewards.append(self.current_episode_reward)
                self.episode_lengths.append(self.current_episode_length)
                self.current_episode_reward = 0.0
                self.current_episode_length = 0
                state = self._generate_initial_state()
            else:
                state = next_state
        
        return experiences
    
    def _generate_initial_state(self) -> torch.Tensor:
        """Generate initial behavioral state."""
        # Simulate initial behavioral state
        # In practice, this would come from actual behavioral data or environment
        state_dim = self.model.state_dim
        return torch.randn(state_dim, device=self.device)
    
    def _simulate_environment_step(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, float, bool]:
        """
        Simulate environment step for behavioral learning.
        
        Args:
            state: Current behavioral state
            action: Action taken (behavioral parameters)
            
        Returns:
            Tuple of (next_state, reward, done)
        """
        # Simulate next state based on action
        next_state = state + 0.1 * action + 0.05 * torch.randn_like(state)
        
        # Compute reward using reward model
        with torch.no_grad():
            behavioral_features = torch.cat([state, action], dim=0).unsqueeze(0)
            reward_dict = self.reward_model(behavioral_features)
            reward = reward_dict['total_reward'].item()
        
        # Add intrinsic rewards if enabled
        if self.rl_config.use_intrinsic_rewards:
            intrinsic_reward = self._compute_intrinsic_reward(state, action, next_state)
            reward += self.rl_config.intrinsic_reward_weight * intrinsic_reward
        
        # Determine if episode is done (simplified)
        done = (self.current_episode_length >= 1000 or 
                torch.norm(next_state) > 10.0)  # Arbitrary termination conditions
        
        return next_state, reward, done
    
    def _compute_intrinsic_reward(self, state: torch.Tensor, action: torch.Tensor, 
                                next_state: torch.Tensor) -> float:
        """Compute intrinsic reward for exploration."""
        # Simple curiosity-based reward: reward for state changes
        state_change = torch.norm(next_state - state).item()
        
        # Reward for diverse actions
        action_magnitude = torch.norm(action).item()
        
        intrinsic_reward = 0.5 * state_change + 0.5 * action_magnitude
        return intrinsic_reward
    
    def train_step(self, batch: Optional[Dict[str, torch.Tensor]] = None) -> Dict[str, torch.Tensor]:
        """
        Perform RL training step using PPO.
        
        Args:
            batch: Optional batch (not used in RL training)
            
        Returns:
            Dictionary containing training metrics
        """
        self.model.train()
        
        # Collect rollout experiences
        experiences = self.collect_rollout(self.rl_config.rollout_length)
        
        # Compute advantages and returns
        advantages, returns = self.compute_advantages_and_returns(experiences)
        
        # Prepare batch data
        states = torch.stack([exp['state'] for exp in experiences])
        actions = torch.stack([exp['action'] for exp in experiences])
        old_log_probs = torch.tensor([exp['log_prob'] for exp in experiences], dtype=torch.float32)
        values = torch.tensor([exp['value'] for exp in experiences], dtype=torch.float32)
        
        batch_data = {
            'states': states,
            'actions': actions,
            'log_probs': old_log_probs,
            'rewards': torch.tensor([exp['reward'] for exp in experiences], dtype=torch.float32),
            'values': values,
            'advantages': advantages,
            'returns': returns
        }
        
        # Move to device
        batch_data = {k: v.to(self.device) for k, v in batch_data.items()}
        
        # PPO updates
        total_metrics = {}
        for epoch in range(self.rl_config.ppo_epochs):
            # Shuffle data
            indices = torch.randperm(len(experiences))
            
            # Mini-batch updates
            for start_idx in range(0, len(experiences), self.rl_config.mini_batch_size):
                end_idx = min(start_idx + self.rl_config.mini_batch_size, len(experiences))
                mini_batch_indices = indices[start_idx:end_idx]
                
                mini_batch = {k: v[mini_batch_indices] for k, v in batch_data.items()}
                
                # Compute loss
                if self.scaler is not None:
                    with torch.cuda.amp.autocast():
                        loss_dict = self.compute_loss(mini_batch)
                else:
                    loss_dict = self.compute_loss(mini_batch)
                
                # Backward pass
                self.optimizer.zero_grad()
                if self.scaler is not None:
                    self.scaler.scale(loss_dict['total_loss']).backward()
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.rl_config.max_grad_norm)
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                else:
                    loss_dict['total_loss'].backward()
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.rl_config.max_grad_norm)
                    self.optimizer.step()
                
                # Accumulate metrics
                for key, value in loss_dict.items():
                    if key not in total_metrics:
                        total_metrics[key] = []
                    total_metrics[key].append(value.item() if isinstance(value, torch.Tensor) else value)
        
        # Average metrics
        avg_metrics = {key: np.mean(values) for key, values in total_metrics.items()}
        
        # Add episode statistics
        if self.episode_rewards:
            avg_metrics['mean_episode_reward'] = np.mean(self.episode_rewards[-10:])
            avg_metrics['mean_episode_length'] = np.mean(self.episode_lengths[-10:])
        
        # Update exploration
        self.current_exploration = max(
            self.current_exploration * self.rl_config.exploration_decay,
            self.rl_config.min_exploration
        )
        avg_metrics['exploration_noise'] = self.current_exploration
        
        return avg_metrics
    
    def validation_step(self, batch: Optional[Dict[str, torch.Tensor]] = None) -> Dict[str, torch.Tensor]:
        """Perform validation step (evaluate policy performance)."""
        self.model.eval()
        
        # Collect validation rollout with deterministic policy
        val_experiences = []
        state = self._generate_initial_state()
        episode_reward = 0.0
        episode_length = 0
        
        with torch.no_grad():
            for _ in range(100):  # Short validation episode
                action, _, value = self.model.act(state.unsqueeze(0), deterministic=True)
                action = action.squeeze(0)
                
                next_state, reward, done = self._simulate_environment_step(state, action)
                
                episode_reward += reward
                episode_length += 1
                
                if done:
                    break
                
                state = next_state
        
        return {
            'val_episode_reward': episode_reward,
            'val_episode_length': episode_length
        }
