/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* App Layout */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

.app-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 2rem;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--accent-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    background: none;
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.theme-toggle:hover {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.app-body {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar Navigation */
.sidebar {
    width: 250px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.sidebar-content {
    padding: 2rem 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-secondary);
    font-weight: 500;
}

.nav-item:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--accent-primary);
    color: white;
    border-right: 3px solid var(--accent-secondary);
}

.nav-item i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
    background: var(--bg-primary);
}

.content-section {
    display: none;
    max-width: 1200px;
    margin: 0 auto;
}

.content-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.section-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Recording Section */
.recording-container {
    position: relative;
}

.recording-setup {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;
}

.setup-card {
    background: var(--bg-secondary);
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 100%;
}

.setup-icon {
    font-size: 4rem;
    color: var(--accent-primary);
    margin-bottom: 1.5rem;
}

.setup-card h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.setup-card p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.feature-list {
    list-style: none;
    text-align: left;
    margin: 2rem 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    color: var(--text-primary);
}

.feature-list i {
    color: var(--accent-primary);
    width: 20px;
}

.session-info {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
    border: 1px solid var(--border-color);
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.start-button {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 2rem auto 0;
}

.start-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Recording Active */
.recording-active {
    background: var(--bg-secondary);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
}

.recording-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.recording-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--accent-primary);
}

.status-indicator.recording {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.recording-timer, .data-counter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

.task-container {
    margin-bottom: 2rem;
}

.task-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.progress-bar {
    flex: 1;
    height: 8px;
    background: var(--bg-primary);
    border-radius: 4px;
    margin-right: 1rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    width: 0%;
    transition: width 0.3s ease;
}

.current-task {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.current-task h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.current-task p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.task-content {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 2px dashed var(--border-color);
    position: relative;
}

.task-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.task-button {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.task-button:not(.secondary) {
    background: var(--accent-primary);
    color: white;
}

.task-button.secondary {
    background: transparent;
    color: var(--text-secondary);
    border: 2px solid var(--border-color);
}

.task-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.recording-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.control-button {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-button.danger {
    border-color: #ef4444;
    color: #ef4444;
}

.control-button:hover {
    background: var(--bg-hover);
}

/* Recording Complete */
.recording-complete {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;
}

.complete-card {
    background: var(--bg-secondary);
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
    max-width: 600px;
    width: 100%;
}

.complete-icon {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 1.5rem;
}

.complete-card h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.session-summary {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 1.5rem;
    margin: 2rem 0;
    border: 1px solid var(--border-color);
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
    border-bottom: none;
}

.complete-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.action-button {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-button:not(.secondary) {
    background: var(--accent-primary);
    color: white;
}

.action-button.secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Sessions Section */
.sessions-container {
    max-width: 1000px;
}

.sessions-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    gap: 1rem;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
}

.sessions-list {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.session-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.session-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.session-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.session-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: capitalize;
}

.session-status.completed {
    background: #dcfce7;
    color: #166534;
}

.session-status.active {
    background: #fef3c7;
    color: #92400e;
}

.session-date {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.session-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    color: var(--text-primary);
}

.session-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    flex: 1;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.view-btn {
    background: var(--accent-primary);
    color: white;
}

.analyze-btn {
    background: var(--accent-secondary);
    color: white;
}

.download-btn {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.delete-btn {
    background: #ef4444;
    color: white;
}

.delete-btn:hover {
    background: #dc2626;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Analysis Section */
.analysis-container {
    max-width: 1200px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.analysis-charts {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.chart-container h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.chart-placeholder {
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    border: 2px dashed var(--border-color);
    border-radius: 8px;
}

.chart-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Modal Styles */
.session-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: var(--bg-secondary);
    border-radius: 16px;
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-primary);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-primary);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Session Overview */
.session-overview {
    margin-bottom: 2rem;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.overview-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.overview-item strong {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.overview-item span {
    color: var(--text-primary);
    font-weight: 600;
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.completed {
    background: #dcfce7;
    color: #166534;
}

.status-badge.active {
    background: #fef3c7;
    color: #92400e;
}

/* Data Summary */
.data-summary {
    margin-bottom: 2rem;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Data Preview */
.data-preview {
    margin-bottom: 2rem;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.preview-table th,
.preview-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.preview-table th {
    background: var(--bg-primary);
    font-weight: 600;
    color: var(--text-primary);
}

.preview-table td {
    color: var(--text-secondary);
    font-family: monospace;
    font-size: 0.8rem;
}

/* Session Analysis */
.session-analysis-section {
    background: var(--bg-secondary);
    border-radius: 16px;
    margin-top: 2rem;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
}

.analysis-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.close-analysis {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.close-analysis:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.analysis-content {
    padding: 2rem;
}

.analysis-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.metric-card h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.metric-grid {
    display: grid;
    gap: 0.75rem;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-item strong {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.metric-item span {
    color: var(--text-primary);
    font-weight: 600;
    font-family: monospace;
}

/* Analysis Charts */
.analysis-charts {
    margin-bottom: 2rem;
}

.chart-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-container {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.chart-container h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
}

/* Chart Bars */
.chart-bars {
    display: flex;
    align-items: end;
    justify-content: space-around;
    height: 200px;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.chart-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    width: 60px;
    background: linear-gradient(to top, var(--accent-primary), var(--accent-secondary));
    border-radius: 4px 4px 0 0;
    position: relative;
    transition: all 0.3s ease;
}

.chart-bar:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.bar-label {
    position: absolute;
    bottom: -30px;
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-align: center;
    width: 80px;
    left: 50%;
    transform: translateX(-50%);
}

.bar-value {
    position: absolute;
    top: -25px;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: center;
    width: 80px;
    left: 50%;
    transform: translateX(-50%);
}

/* Timeline */
.timeline-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.timeline-bar {
    height: 30px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 1rem;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 100px;
}

.timeline-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Analysis Export */
.analysis-export {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

/* Metadata Section */
.metadata-section {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.metadata-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 600;
}

.metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
}

.metadata-item {
    font-size: 0.8rem;
    color: var(--text-secondary);
    display: flex;
    justify-content: space-between;
}

.metadata-item strong {
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-body {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .sidebar-content {
        padding: 1rem 0;
    }

    .nav-menu {
        display: flex;
        overflow-x: auto;
    }

    .nav-item {
        flex-shrink: 0;
        padding: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .sessions-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        max-width: none;
    }

    .sessions-list {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .session-actions {
        flex-direction: column;
    }
}

/* Countdown Styles */
.countdown-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 300px;
    text-align: center;
}

.task-instructions {
    margin-bottom: 2rem;
    max-width: 600px;
}

.task-instructions p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.countdown-display {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.countdown-number {
    font-size: 4rem;
    font-weight: bold;
    color: var(--accent-primary);
    margin-bottom: 1rem;
    animation: countdown-pulse 1s ease-in-out infinite;
}

.countdown-text {
    font-size: 1.2rem;
    color: var(--text-secondary);
}

@keyframes countdown-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Typing Task Styles */
.typing-area .text-display-container {
    position: relative;
}

.typing-area #text-display .correct {
    background-color: #10b981;
    color: white;
    padding: 0 2px;
    border-radius: 2px;
}

.typing-area #text-display .incorrect {
    background-color: #ef4444;
    color: white;
    padding: 0 2px;
    border-radius: 2px;
}

.typing-area #text-display .current {
    background-color: var(--accent-primary);
    color: white;
    padding: 0 2px;
    border-radius: 2px;
    animation: typing-blink 1s infinite;
}

@keyframes typing-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

/* Mouse Movement Task Animations */

@keyframes ripple {
    0% { transform: scale(0); opacity: 1; }
    100% { transform: scale(1); opacity: 0; }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes fadeInUp {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
}

.typing-stats {
    display: flex;
    justify-content: space-around;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.typing-stats span {
    font-weight: 500;
}

/* Drag and Drop Styles */
.drag-item {
    transition: all 0.2s ease;
}

.drag-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.drag-item:active {
    cursor: grabbing;
}

.drop-zone {
    transition: all 0.2s ease;
}

.drop-zone.drag-over {
    transform: scale(1.02);
    border-style: solid;
}

/* Navigation Task Styles */
.nav-item a:hover {
    background: var(--accent-primary-light) !important;
}

.dropdown-menu {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dropdown-menu a:hover {
    background: var(--accent-primary) !important;
    color: white !important;
}
