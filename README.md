# Interaction Recorder - AI Training Data Collection

A production-level web application for recording detailed user interactions at high frequency (144Hz) for training AI models to perform human-like web automation using nodriver.

## Features

### 🎯 High-Frequency Data Capture
- **144Hz precision** mouse movement tracking
- **Velocity and acceleration** calculations
- **Typing rhythm** and pattern analysis
- **Scrolling behavior** capture
- **Click precision** and timing data

### 🎮 Guided Recording Sessions
- **10-minute structured sessions** with 8 different tasks
- **Visual task guidance** and progress tracking
- **Mouse movement calibration**
- **Precise clicking challenges**
- **Text input and editing tasks**
- **Drag and drop operations**
- **Scrolling pattern capture**
- **Form interaction recording**
- **Menu navigation tasks**
- **Free-form interaction**

### 📊 Advanced Analytics
- **Real-time data visualization**
- **Mouse movement heatmaps**
- **Speed and acceleration charts**
- **Typing rhythm analysis**
- **Behavioral pattern detection**
- **Session comparison tools**

### 🎨 Modern UI/UX
- **Dark and light themes**
- **Responsive design**
- **Sidebar navigation**
- **Real-time progress tracking**
- **Professional dashboard**

### 💾 Data Management
- **Session storage and retrieval**
- **Data export capabilities**
- **Analysis report generation**
- **Search and filtering**
- **Batch data processing**

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd interaction-recorder
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the server**
   ```bash
   npm start
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## Usage

### Starting a Recording Session

1. **Navigate to the Recording tab**
2. **Click "Start Recording Session"**
3. **Follow the guided tasks** for approximately 10 minutes
4. **Complete or skip tasks** as needed
5. **Review your session data** upon completion

### Viewing Sessions

1. **Go to the Saved Sessions tab**
2. **Browse your recorded sessions**
3. **View detailed session data**
4. **Export data** in JSON format
5. **Delete unwanted sessions**

### Analyzing Data

1. **Visit the Analysis tab**
2. **View overall statistics**
3. **Analyze specific sessions**
4. **Generate detailed reports**
5. **Export analysis data**

## Data Structure

### Captured Data Points

Each interaction generates detailed data points including:

```javascript
{
  type: 'mouse_move' | 'click' | 'key_down' | 'wheel' | ...,
  timestamp: performance.now(),
  data: {
    // Type-specific data
    x: number,           // Mouse X coordinate
    y: number,           // Mouse Y coordinate
    velocity: {          // Calculated velocity
      x: number,
      y: number,
      magnitude: number
    },
    acceleration: object, // Calculated acceleration
    key: string,         // Keyboard key
    deltaY: number,      // Scroll delta
    taskId: number       // Current task identifier
  }
}
```

### Session Metadata

```javascript
{
  id: string,              // Unique session ID
  startTime: timestamp,    // Session start time
  endTime: timestamp,      // Session end time
  duration: number,        // Total duration in ms
  status: 'active' | 'completed',
  dataPoints: number,      // Total data points captured
  metadata: {
    userAgent: string,     // Browser information
    screenResolution: string,
    viewportSize: string,
    captureFrequency: 144  // Hz
  }
}
```

## API Endpoints

### Session Management
- `POST /api/sessions/start` - Start new recording session
- `POST /api/sessions/:id/data` - Save interaction data
- `POST /api/sessions/:id/complete` - Complete session
- `GET /api/sessions` - List all sessions
- `GET /api/sessions/:id` - Get specific session data

### Analysis
- `GET /api/analysis/summary` - Overall statistics
- `GET /api/analysis/sessions/:id` - Session-specific analysis

## Technical Specifications

### Performance
- **144Hz capture rate** (6.94ms intervals)
- **Batch processing** for efficient data storage
- **Real-time UI updates** without performance impact
- **Memory-efficient** data handling

### Browser Compatibility
- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge

### Data Storage
- JSON file-based storage (easily upgradeable to database)
- Automatic data compression
- Session isolation
- Backup-friendly format

## Development

### Project Structure
```
interaction-recorder/
├── server.js              # Express server
├── package.json           # Dependencies
├── public/
│   ├── index.html         # Main application
│   ├── css/
│   │   ├── styles.css     # Main styles
│   │   └── themes.css     # Theme system
│   └── js/
│       ├── app.js         # Main application logic
│       ├── recorder.js    # High-frequency data capture
│       ├── sessions.js    # Session management
│       └── analysis.js    # Data analysis
└── data/
    └── sessions/          # Session data storage
```

### Adding New Task Types

1. **Define task in recorder.js**
   ```javascript
   {
     title: "New Task",
     description: "Task description",
     type: "new_task_type",
     duration: 60000,
     instructions: "Task instructions"
   }
   ```

2. **Implement task content generation**
   ```javascript
   generateTaskContent(task) {
     if (task.type === 'new_task_type') {
       // Generate task-specific UI
     }
   }
   ```

3. **Add task-specific analysis** in analysis.js

### Customizing Capture Frequency

Modify the capture frequency in `recorder.js`:
```javascript
this.captureFrequency = 144; // Hz (144, 120, 60, etc.)
```

## Use Cases

### AI Training Data
- **Web automation models** (nodriver, Selenium)
- **Human behavior simulation**
- **UI/UX pattern analysis**
- **Accessibility research**
- **User experience optimization**

### Research Applications
- **Human-computer interaction studies**
- **Behavioral pattern analysis**
- **Performance benchmarking**
- **Usability testing**

## Security & Privacy

- **Local data storage** - no external data transmission
- **Session isolation** - each session is independent
- **User consent** - clear data collection disclosure
- **Data ownership** - users control their data
- **Export capabilities** - easy data portability

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues, questions, or contributions:
- Create an issue on GitHub
- Review the documentation
- Check existing sessions for examples

---

**Built for AI Training Data Collection**
*Capture human interaction patterns with unprecedented precision*
