#!/usr/bin/env python3
"""
Installation verification script for HumanBehaviorAI
Tests core dependencies and CUDA availability
"""

import sys
import importlib
import subprocess

def test_import(module_name, package_name=None):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        print(f"✓ {package_name or module_name} - OK")
        return True
    except ImportError as e:
        print(f"✗ {package_name or module_name} - FAILED: {e}")
        return False

def test_cuda():
    """Test CUDA availability"""
    try:
        import torch
        if torch.cuda.is_available():
            print(f"✓ CUDA - Available (Version: {torch.version.cuda})")
            print(f"  GPU Count: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
            return True
        else:
            print("⚠ CUDA - Not available (CPU only)")
            return False
    except Exception as e:
        print(f"✗ CUDA - Error: {e}")
        return False

def main():
    print("=== HumanBehaviorAI Installation Verification ===\n")
    
    # Core dependencies
    core_deps = [
        ("torch", "PyTorch"),
        ("torchvision", "TorchVision"),
        ("torchaudio", "TorchAudio"),
        ("transformers", "Transformers"),
        ("accelerate", "Accelerate"),
        ("datasets", "Datasets"),
        ("numpy", "NumPy"),
        ("pandas", "Pandas"),
        ("matplotlib", "Matplotlib"),
        ("tqdm", "TQDM"),
    ]
    
    print("Testing Core Dependencies:")
    core_success = 0
    for module, name in core_deps:
        if test_import(module, name):
            core_success += 1
    
    print(f"\nCore Dependencies: {core_success}/{len(core_deps)} successful")
    
    # Optional dependencies
    optional_deps = [
        ("wandb", "Weights & Biases"),
        ("tensorboard", "TensorBoard"),
        ("optuna", "Optuna"),
        ("nodriver", "NoDriver"),
        ("selenium", "Selenium"),
    ]
    
    print("\nTesting Optional Dependencies:")
    optional_success = 0
    for module, name in optional_deps:
        if test_import(module, name):
            optional_success += 1
    
    print(f"\nOptional Dependencies: {optional_success}/{len(optional_deps)} successful")
    
    # Test CUDA
    print("\nTesting CUDA:")
    cuda_available = test_cuda()
    
    # Test package installation
    print("\nTesting Package Installation:")
    try:
        import human_behavior_ai
        print("✓ HumanBehaviorAI package - OK")
        package_installed = True
    except ImportError as e:
        print(f"✗ HumanBehaviorAI package - FAILED: {e}")
        package_installed = False
    
    # Summary
    print("\n" + "="*50)
    print("INSTALLATION SUMMARY:")
    print(f"Core Dependencies: {core_success}/{len(core_deps)}")
    print(f"Optional Dependencies: {optional_success}/{len(optional_deps)}")
    print(f"CUDA Support: {'Yes' if cuda_available else 'No'}")
    print(f"Package Installed: {'Yes' if package_installed else 'No'}")
    
    if core_success == len(core_deps) and package_installed:
        print("\n🎉 Installation appears to be successful!")
        if not cuda_available:
            print("⚠ Note: CUDA not available - training will use CPU only")
    else:
        print("\n❌ Installation has issues that need to be resolved")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
