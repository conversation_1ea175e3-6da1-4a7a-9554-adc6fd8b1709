"""
Adversarial training components for bot detection evasion.

This module implements adversarial training techniques to make
behavioral models robust against detection systems.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, Tuple, List
import numpy as np

from .base import BaseTrainer, TrainingConfig
from ..models.base import BaseModel


class AdversarialTrainingConfig(TrainingConfig):
    """Configuration for adversarial training."""
    
    # Adversarial training parameters
    adversarial_weight: float = 0.5
    detection_weight: float = 1.0
    
    # Attack parameters
    attack_epsilon: float = 0.1
    attack_steps: int = 10
    attack_step_size: float = 0.01
    
    # Defense parameters
    defense_noise_std: float = 0.05
    defense_dropout: float = 0.1
    
    # Training strategy
    adversarial_frequency: int = 5  # Every N steps
    use_pgd_attack: bool = True
    use_fgsm_attack: bool = False
    
    # Detection evasion
    evasion_targets: List[str] = None  # Types of detection to evade
    
    def __post_init__(self):
        super().__post_init__()
        if self.evasion_targets is None:
            self.evasion_targets = ["timing", "movement", "pattern"]


class DetectionModel(nn.Module):
    """
    Simulated bot detection model for adversarial training.
    
    This model simulates various detection mechanisms that
    the behavioral model needs to evade.
    """
    
    def __init__(
        self,
        input_dim: int = 512,
        hidden_dims: List[int] = [256, 128, 64],
        num_detection_types: int = 3
    ):
        super().__init__()
        
        self.input_dim = input_dim
        self.num_detection_types = num_detection_types
        
        # Shared feature extractor
        layers = []
        prev_dim = input_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Detection heads for different types
        self.detection_heads = nn.ModuleList([
            nn.Linear(prev_dim, 2)  # Binary classification: human vs bot
            for _ in range(num_detection_types)
        ])
        
        # Combined detection head
        self.combined_head = nn.Linear(prev_dim, 2)
    
    def forward(self, behavioral_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass through detection model.
        
        Args:
            behavioral_features: Features extracted from behavioral sequences
            
        Returns:
            Dictionary containing detection scores for different types
        """
        features = self.feature_extractor(behavioral_features)
        
        # Individual detection scores
        detection_scores = {}
        for i, head in enumerate(self.detection_heads):
            detection_scores[f'detection_{i}'] = head(features)
        
        # Combined detection score
        detection_scores['combined'] = self.combined_head(features)
        
        return detection_scores


class AdversarialTrainer(BaseTrainer):
    """
    Adversarial trainer for bot detection evasion.
    
    Implements adversarial training where the behavioral model
    learns to generate human-like patterns that evade detection.
    """
    
    def __init__(
        self,
        model: BaseModel,
        config: AdversarialTrainingConfig,
        train_dataloader: DataLoader,
        val_dataloader: Optional[DataLoader] = None,
        test_dataloader: Optional[DataLoader] = None
    ):
        super().__init__(model, config, train_dataloader, val_dataloader, test_dataloader)
        
        self.adv_config = config
        
        # Initialize detection model
        self.detection_model = DetectionModel(
            input_dim=model.config.hidden_dim,
            num_detection_types=len(config.evasion_targets)
        ).to(self.device)
        
        # Setup detection model optimizer
        self.detection_optimizer = torch.optim.AdamW(
            self.detection_model.parameters(),
            lr=config.learning_rate * 0.5,  # Slower learning for detector
            weight_decay=config.weight_decay
        )
        
        # Attack methods
        self.attack_methods = {
            'pgd': self._pgd_attack,
            'fgsm': self._fgsm_attack
        }
        
        # Training statistics
        self.detection_accuracy = []
        self.evasion_success_rate = []
    
    def compute_loss(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Compute adversarial training loss.
        
        Args:
            batch: Dictionary containing batch data
            
        Returns:
            Dictionary containing loss components
        """
        sequences = batch['sequences']
        batch_size = sequences.size(0)
        
        # Forward pass through behavioral model
        model_output = self.model(sequences)
        behavioral_features = model_output.get('behavioral_features', 
                                              model_output.get('latent_mean', sequences.mean(dim=1)))
        
        # 1. Primary task loss (reconstruction, generation, etc.)
        primary_loss = self._compute_primary_loss(model_output, sequences)
        
        # 2. Detection evasion loss
        detection_scores = self.detection_model(behavioral_features)
        
        # We want to fool the detector (make it classify as human)
        human_labels = torch.zeros(batch_size, dtype=torch.long, device=self.device)
        
        evasion_losses = []
        for key, scores in detection_scores.items():
            evasion_loss = F.cross_entropy(scores, human_labels)
            evasion_losses.append(evasion_loss)
        
        total_evasion_loss = torch.stack(evasion_losses).mean()
        
        # 3. Adversarial perturbation loss (if using adversarial examples)
        adversarial_loss = torch.tensor(0.0, device=self.device)
        if self.current_step % self.adv_config.adversarial_frequency == 0:
            adversarial_loss = self._compute_adversarial_loss(sequences, behavioral_features)
        
        # Combine losses
        total_loss = (
            primary_loss +
            self.adv_config.detection_weight * total_evasion_loss +
            self.adv_config.adversarial_weight * adversarial_loss
        )
        
        return {
            'total_loss': total_loss,
            'primary_loss': primary_loss,
            'evasion_loss': total_evasion_loss,
            'adversarial_loss': adversarial_loss
        }
    
    def _compute_primary_loss(self, model_output: Dict[str, torch.Tensor], 
                            sequences: torch.Tensor) -> torch.Tensor:
        """Compute primary task loss (model-specific)."""
        # Default implementation: reconstruction loss
        if 'reconstructed' in model_output:
            return F.mse_loss(model_output['reconstructed'], sequences)
        else:
            # Fallback: feature consistency loss
            return torch.tensor(0.0, device=sequences.device)
    
    def _compute_adversarial_loss(self, sequences: torch.Tensor, 
                                behavioral_features: torch.Tensor) -> torch.Tensor:
        """Compute adversarial perturbation loss."""
        # Generate adversarial examples
        if self.adv_config.use_pgd_attack:
            adv_sequences = self._pgd_attack(sequences)
        elif self.adv_config.use_fgsm_attack:
            adv_sequences = self._fgsm_attack(sequences)
        else:
            return torch.tensor(0.0, device=sequences.device)
        
        # Forward pass with adversarial examples
        adv_output = self.model(adv_sequences)
        adv_features = adv_output.get('behavioral_features', 
                                    adv_output.get('latent_mean', adv_sequences.mean(dim=1)))
        
        # Consistency loss between clean and adversarial features
        adversarial_loss = F.mse_loss(adv_features, behavioral_features.detach())
        
        return adversarial_loss
    
    def _pgd_attack(self, sequences: torch.Tensor) -> torch.Tensor:
        """Projected Gradient Descent attack."""
        adv_sequences = sequences.clone().detach()
        adv_sequences.requires_grad_(True)
        
        for _ in range(self.adv_config.attack_steps):
            # Forward pass
            model_output = self.model(adv_sequences)
            behavioral_features = model_output.get('behavioral_features', 
                                                 model_output.get('latent_mean', adv_sequences.mean(dim=1)))
            
            # Detection loss (we want to maximize detection)
            detection_scores = self.detection_model(behavioral_features)
            bot_labels = torch.ones(sequences.size(0), dtype=torch.long, device=self.device)
            
            loss = F.cross_entropy(detection_scores['combined'], bot_labels)
            
            # Backward pass
            loss.backward()
            
            # Update adversarial examples
            with torch.no_grad():
                adv_sequences += self.adv_config.attack_step_size * adv_sequences.grad.sign()
                
                # Project to epsilon ball
                perturbation = adv_sequences - sequences
                perturbation = torch.clamp(perturbation, 
                                         -self.adv_config.attack_epsilon, 
                                         self.adv_config.attack_epsilon)
                adv_sequences = sequences + perturbation
                
                # Clear gradients
                adv_sequences.grad.zero_()
        
        return adv_sequences.detach()
    
    def _fgsm_attack(self, sequences: torch.Tensor) -> torch.Tensor:
        """Fast Gradient Sign Method attack."""
        adv_sequences = sequences.clone().detach()
        adv_sequences.requires_grad_(True)
        
        # Forward pass
        model_output = self.model(adv_sequences)
        behavioral_features = model_output.get('behavioral_features', 
                                             model_output.get('latent_mean', adv_sequences.mean(dim=1)))
        
        # Detection loss
        detection_scores = self.detection_model(behavioral_features)
        bot_labels = torch.ones(sequences.size(0), dtype=torch.long, device=self.device)
        
        loss = F.cross_entropy(detection_scores['combined'], bot_labels)
        
        # Backward pass
        loss.backward()
        
        # Generate adversarial example
        with torch.no_grad():
            perturbation = self.adv_config.attack_epsilon * adv_sequences.grad.sign()
            adv_sequences = sequences + perturbation
        
        return adv_sequences.detach()
    
    def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Perform adversarial training step.
        """
        self.model.train()
        self.detection_model.train()
        
        # Move batch to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                for k, v in batch.items()}
        
        # 1. Train detection model (discriminator-like training)
        self._train_detection_model(batch)
        
        # 2. Train behavioral model with adversarial loss
        if self.scaler is not None:
            with torch.cuda.amp.autocast():
                loss_dict = self.compute_loss(batch)
        else:
            loss_dict = self.compute_loss(batch)
        
        total_loss = loss_dict['total_loss']
        
        # Backward pass for behavioral model
        self.optimizer.zero_grad()
        if self.scaler is not None:
            self.scaler.scale(total_loss).backward()
            self.scaler.step(self.optimizer)
            self.scaler.update()
        else:
            total_loss.backward()
            self.optimizer.step()
        
        return loss_dict
    
    def _train_detection_model(self, batch: Dict[str, torch.Tensor]):
        """Train the detection model to distinguish human vs bot behavior."""
        sequences = batch['sequences']
        
        # Get behavioral features from current model
        with torch.no_grad():
            model_output = self.model(sequences)
            behavioral_features = model_output.get('behavioral_features', 
                                                 model_output.get('latent_mean', sequences.mean(dim=1)))
        
        # Detection model forward pass
        detection_scores = self.detection_model(behavioral_features)
        
        # Labels: assume batch contains real human data (label 0)
        # and generated/bot data (label 1)
        if 'is_human' in batch:
            labels = batch['is_human'].long()
        else:
            # Default: assume all data is human for now
            labels = torch.zeros(sequences.size(0), dtype=torch.long, device=self.device)
        
        # Compute detection loss
        detection_loss = F.cross_entropy(detection_scores['combined'], labels)
        
        # Update detection model
        self.detection_optimizer.zero_grad()
        detection_loss.backward()
        self.detection_optimizer.step()
        
        # Track detection accuracy
        with torch.no_grad():
            predictions = torch.argmax(detection_scores['combined'], dim=1)
            accuracy = (predictions == labels).float().mean().item()
            self.detection_accuracy.append(accuracy)
    
    def validation_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Perform validation step with evasion success rate."""
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                for k, v in batch.items()}
        
        with torch.no_grad():
            # Compute standard validation loss
            loss_dict = self.compute_loss(batch)
            
            # Compute evasion success rate
            sequences = batch['sequences']
            model_output = self.model(sequences)
            behavioral_features = model_output.get('behavioral_features', 
                                                 model_output.get('latent_mean', sequences.mean(dim=1)))
            
            detection_scores = self.detection_model(behavioral_features)
            predictions = torch.argmax(detection_scores['combined'], dim=1)
            
            # Success if predicted as human (label 0)
            evasion_success = (predictions == 0).float().mean().item()
            self.evasion_success_rate.append(evasion_success)
            
            loss_dict['evasion_success_rate'] = evasion_success
            loss_dict['detection_accuracy'] = np.mean(self.detection_accuracy[-100:]) if self.detection_accuracy else 0.0
        
        return loss_dict
