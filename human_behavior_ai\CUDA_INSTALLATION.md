# CUDA 12.9 Installation Guide for HumanBehaviorAI

## Prerequisites
- CUDA Toolkit 12.9 installed on your system
- Python 3.8+ with virtual environment activated

## PyTorch Installation for CUDA 12.9

### Step 1: Install PyTorch with CUDA 12.4 Support
Since PyTorch doesn't have official CUDA 12.9 binaries yet, use CUDA 12.4 which is compatible:

```bash
# Install PyTorch with CUDA 12.4 (compatible with CUDA 12.9)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu124
```

### Step 2: Verify CUDA Installation
```python
import torch
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
print(f"CUDA version: {torch.version.cuda}")
print(f"Number of GPUs: {torch.cuda.device_count()}")
if torch.cuda.is_available():
    print(f"GPU name: {torch.cuda.get_device_name(0)}")
```

### Step 3: Install HumanBehaviorAI
```bash
# Install in editable mode with GPU extras
pip install -e ".[gpu]"
```

### Alternative: Manual Installation
If the above doesn't work, install dependencies manually:

```bash
# Core PyTorch with CUDA
pip install torch>=2.4.0 torchvision>=0.19.0 torchaudio>=2.4.0 --index-url https://download.pytorch.org/whl/cu124

# Install remaining dependencies
pip install -r requirements.txt
```

## Troubleshooting

### CUDA Version Mismatch
If you get CUDA version errors:
1. Check your CUDA toolkit version: `nvcc --version`
2. Use the appropriate PyTorch index URL
3. For CUDA 12.1: `--index-url https://download.pytorch.org/whl/cu121`
4. For CUDA 11.8: `--index-url https://download.pytorch.org/whl/cu118`

### Memory Issues
If you encounter GPU memory issues during training:
1. Reduce batch size in training configuration
2. Enable gradient checkpointing
3. Use mixed precision training (FP16)

### Performance Optimization
For maximum performance:
1. Ensure CUDA toolkit and PyTorch versions are compatible
2. Use the latest NVIDIA drivers
3. Enable TensorFloat-32 (TF32) for Ampere GPUs:
   ```python
   torch.backends.cuda.matmul.allow_tf32 = True
   torch.backends.cudnn.allow_tf32 = True
   ```

## Verification Script
Run this to verify your installation:

```python
import torch
import transformers
import accelerate
import nodriver

print("=== Installation Verification ===")
print(f"PyTorch: {torch.__version__}")
print(f"Transformers: {transformers.__version__}")
print(f"Accelerate: {accelerate.__version__}")
print(f"CUDA Available: {torch.cuda.is_available()}")

if torch.cuda.is_available():
    print(f"CUDA Version: {torch.version.cuda}")
    print(f"GPU Count: {torch.cuda.device_count()}")
    print(f"GPU Name: {torch.cuda.get_device_name(0)}")
    
    # Test GPU computation
    x = torch.randn(1000, 1000).cuda()
    y = torch.mm(x, x.t())
    print("GPU computation test: PASSED")
else:
    print("WARNING: CUDA not available - will use CPU only")

print("=== All checks completed ===")
```
