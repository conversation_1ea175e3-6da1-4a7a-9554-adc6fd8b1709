// Main Application Controller
class InteractionRecorderApp {
    constructor() {
        this.currentSection = 'recording';
        this.theme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        this.setupTheme();
        this.setupNavigation();
        this.setupEventListeners();
        this.loadInitialData();
    }

    setupTheme() {
        document.documentElement.setAttribute('data-theme', this.theme);
        const themeToggle = document.getElementById('theme-toggle');
        const icon = themeToggle.querySelector('i');
        
        if (this.theme === 'dark') {
            icon.className = 'fas fa-sun';
        } else {
            icon.className = 'fas fa-moon';
        }
    }

    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const section = item.dataset.section;
                this.switchSection(section);
            });
        });
    }

    switchSection(section) {
        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${section}-section`).classList.add('active');

        this.currentSection = section;

        // Load section-specific data
        this.loadSectionData(section);
    }

    setupEventListeners() {
        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Recording controls
        document.getElementById('start-recording').addEventListener('click', () => {
            this.startRecording();
        });

        document.getElementById('stop-recording').addEventListener('click', () => {
            this.stopRecording();
        });

        document.getElementById('pause-recording').addEventListener('click', () => {
            this.pauseRecording();
        });

        document.getElementById('new-session').addEventListener('click', () => {
            this.resetRecording();
        });

        document.getElementById('view-session').addEventListener('click', () => {
            this.viewCurrentSession();
        });

        // Task controls
        document.getElementById('complete-task').addEventListener('click', () => {
            this.completeCurrentTask();
        });

        document.getElementById('skip-task').addEventListener('click', () => {
            this.skipCurrentTask();
        });

        // Sessions controls
        document.getElementById('refresh-sessions').addEventListener('click', () => {
            this.loadSessions();
        });

        // Search functionality
        document.getElementById('session-search').addEventListener('input', (e) => {
            this.filterSessions(e.target.value);
        });
    }

    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        localStorage.setItem('theme', this.theme);
        this.setupTheme();
    }

    async loadInitialData() {
        try {
            // Load analysis data for dashboard
            if (this.currentSection === 'analysis') {
                await this.loadAnalysisData();
            }
        } catch (error) {
            console.error('Error loading initial data:', error);
        }
    }

    async loadSectionData(section) {
        switch (section) {
            case 'sessions':
                await this.loadSessions();
                break;
            case 'analysis':
                await this.loadAnalysisData();
                break;
        }
    }

    async loadSessions() {
        try {
            const response = await fetch('/api/sessions');
            const sessions = await response.json();
            this.renderSessions(sessions);
        } catch (error) {
            console.error('Error loading sessions:', error);
            this.showError('Failed to load sessions');
        }
    }

    renderSessions(sessions) {
        const container = document.getElementById('sessions-list');
        
        if (sessions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>No Sessions Found</h3>
                    <p>Start recording to create your first session</p>
                </div>
            `;
            return;
        }

        container.innerHTML = sessions.map(session => `
            <div class="session-card" data-session-id="${session.id}">
                <div class="session-header">
                    <div class="session-status ${session.status}">
                        <i class="fas ${session.status === 'completed' ? 'fa-check-circle' : 'fa-clock'}"></i>
                        ${session.status}
                    </div>
                    <div class="session-date">
                        ${new Date(session.startTime).toLocaleDateString()}
                    </div>
                </div>
                <div class="session-details">
                    <div class="detail-item">
                        <strong>Duration:</strong> 
                        ${session.duration ? this.formatDuration(session.duration) : 'In progress'}
                    </div>
                    <div class="detail-item">
                        <strong>Data Points:</strong> ${session.dataPoints || 0}
                    </div>
                </div>
                <div class="session-actions">
                    <button class="action-btn view-btn" onclick="app.viewSession('${session.id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="action-btn analyze-btn" onclick="app.analyzeSession('${session.id}')">
                        <i class="fas fa-chart-line"></i> Analyze
                    </button>
                    <button class="action-btn download-btn" onclick="app.downloadSession('${session.id}')">
                        <i class="fas fa-download"></i> Download
                    </button>
                    <button class="action-btn delete-btn" onclick="app.deleteSession('${session.id}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `).join('');
    }

    async loadAnalysisData() {
        try {
            const response = await fetch('/api/analysis/summary');
            const data = await response.json();
            this.renderAnalysisData(data);
        } catch (error) {
            console.error('Error loading analysis data:', error);
            this.showError('Failed to load analysis data');
        }
    }

    renderAnalysisData(data) {
        document.getElementById('total-sessions').textContent = data.totalSessions;
        document.getElementById('total-data-points').textContent = data.totalDataPoints.toLocaleString();
        document.getElementById('avg-duration').textContent = this.formatDuration(data.averageDuration);
        document.getElementById('completed-sessions').textContent = data.completedSessions;
    }

    filterSessions(query) {
        const sessionCards = document.querySelectorAll('.session-card');
        sessionCards.forEach(card => {
            const text = card.textContent.toLowerCase();
            const matches = text.includes(query.toLowerCase());
            card.style.display = matches ? 'block' : 'none';
        });
    }

    async viewSession(sessionId) {
        try {
            const response = await fetch(`/api/sessions/${sessionId}`);
            const sessionData = await response.json();
            
            // Switch to analysis view with session data
            this.switchSection('analysis');
            // TODO: Implement detailed session view
            console.log('Session data:', sessionData);
        } catch (error) {
            console.error('Error viewing session:', error);
            this.showError('Failed to load session data');
        }
    }

    async downloadSession(sessionId) {
        try {
            const response = await fetch(`/api/sessions/${sessionId}`);
            const sessionData = await response.json();
            
            const dataStr = JSON.stringify(sessionData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `session_${sessionId}_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        } catch (error) {
            console.error('Error downloading session:', error);
            this.showError('Failed to download session data');
        }
    }

    async analyzeSession(sessionId) {
        try {
            // Switch to analysis view and load session data
            this.switchSection('analysis');
            // TODO: Implement detailed session analysis
            console.log('Analyzing session:', sessionId);
        } catch (error) {
            console.error('Error analyzing session:', error);
            this.showError('Failed to analyze session');
        }
    }

    async deleteSession(sessionId) {
        // Create a more sophisticated confirmation dialog
        const sessionCard = document.querySelector(`[data-session-id="${sessionId}"]`);
        const sessionDate = sessionCard ? sessionCard.querySelector('.session-date').textContent : 'Unknown';

        const confirmed = confirm(
            `⚠️ Delete Session Confirmation\n\n` +
            `Session Date: ${sessionDate}\n` +
            `Session ID: ${sessionId.substring(0, 8)}...\n\n` +
            `This will permanently delete:\n` +
            `• All recorded interaction data\n` +
            `• Session metadata and statistics\n` +
            `• Analysis results\n\n` +
            `This action cannot be undone. Are you sure you want to continue?`
        );

        if (!confirmed) {
            return;
        }

        try {
            // Add loading state to delete button
            const deleteBtn = sessionCard?.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.disabled = true;
                deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
            }

            const response = await fetch(`/api/sessions/${sessionId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            await response.json();

            // Animate session card removal
            if (sessionCard) {
                sessionCard.style.transition = 'all 0.3s ease-out';
                sessionCard.style.transform = 'translateX(-100%)';
                sessionCard.style.opacity = '0';

                setTimeout(() => {
                    sessionCard.remove();
                }, 300);
            }

            this.showSuccess('Session deleted successfully');

            // Refresh the sessions list after animation
            setTimeout(() => {
                this.loadSessions();
            }, 400);

        } catch (error) {
            console.error('Error deleting session:', error);
            this.showError('Failed to delete session: ' + error.message);

            // Restore delete button if it exists
            const deleteBtn = sessionCard?.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i> Delete';
            }
        }
    }

    // Recording methods (will be implemented in recorder.js)
    startRecording() {
        if (window.recorder) {
            window.recorder.start();
        }
    }

    stopRecording() {
        if (window.recorder) {
            window.recorder.stop();
        }
    }

    pauseRecording() {
        if (window.recorder) {
            window.recorder.pause();
        }
    }

    resetRecording() {
        document.getElementById('recording-setup').classList.remove('hidden');
        document.getElementById('recording-active').classList.add('hidden');
        document.getElementById('recording-complete').classList.add('hidden');
    }

    completeCurrentTask() {
        if (window.recorder) {
            window.recorder.completeTask();
        }
    }

    skipCurrentTask() {
        if (window.recorder) {
            window.recorder.skipTask();
        }
    }

    viewCurrentSession() {
        if (window.recorder && window.recorder.currentSessionId) {
            this.viewSession(window.recorder.currentSessionId);
        }
    }

    // Utility methods
    formatDuration(ms) {
        if (!ms) return '0m 0s';
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}m ${seconds}s`;
    }

    showError(message) {
        // Simple error display - could be enhanced with a toast system
        console.error(message);
        alert(message);
    }

    showSuccess(message) {
        // Simple success display - could be enhanced with a toast system
        console.log(message);
    }
}

// Initialize the application
const app = new InteractionRecorderApp();

// Make app globally available for debugging
window.app = app;
