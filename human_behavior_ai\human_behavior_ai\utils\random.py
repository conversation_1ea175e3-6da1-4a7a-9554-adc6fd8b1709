"""
Random state management utilities for HumanBehaviorAI.
"""

import random
import numpy as np
import torch
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def set_seed(seed: int = 42) -> None:
    """
    Set random seed for reproducibility across all libraries.
    
    Args:
        seed: Random seed value
    """
    # Python random
    random.seed(seed)
    
    # NumPy
    np.random.seed(seed)
    
    # PyTorch
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    
    # Additional PyTorch settings for reproducibility
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    # Set environment variable for additional reproducibility
    import os
    os.environ['PYTHONHASHSEED'] = str(seed)
    
    logger.info(f"Random seed set to: {seed}")


def get_random_state() -> Dict[str, Any]:
    """
    Get current random state from all libraries.
    
    Returns:
        Dictionary containing random states
    """
    state = {
        'python_random': random.getstate(),
        'numpy_random': np.random.get_state(),
        'torch_random': torch.get_rng_state(),
    }
    
    if torch.cuda.is_available():
        state['torch_cuda_random'] = torch.cuda.get_rng_state()
        if torch.cuda.device_count() > 1:
            state['torch_cuda_random_all'] = torch.cuda.get_rng_state_all()
    
    return state


def set_random_state(state: Dict[str, Any]) -> None:
    """
    Set random state for all libraries.
    
    Args:
        state: Dictionary containing random states
    """
    if 'python_random' in state:
        random.setstate(state['python_random'])
    
    if 'numpy_random' in state:
        np.random.set_state(state['numpy_random'])
    
    if 'torch_random' in state:
        torch.set_rng_state(state['torch_random'])
    
    if torch.cuda.is_available():
        if 'torch_cuda_random' in state:
            torch.cuda.set_rng_state(state['torch_cuda_random'])
        
        if 'torch_cuda_random_all' in state and torch.cuda.device_count() > 1:
            torch.cuda.set_rng_state_all(state['torch_cuda_random_all'])
    
    logger.info("Random state restored")


def create_reproducible_generator(seed: Optional[int] = None) -> torch.Generator:
    """
    Create a reproducible PyTorch generator.
    
    Args:
        seed: Seed for the generator (uses random seed if None)
        
    Returns:
        PyTorch generator with set seed
    """
    generator = torch.Generator()
    if seed is not None:
        generator.manual_seed(seed)
    else:
        generator.seed()
    
    return generator


def worker_init_fn(worker_id: int, base_seed: int = 42) -> None:
    """
    Initialize worker processes with different seeds for data loading.
    
    Args:
        worker_id: Worker process ID
        base_seed: Base seed to derive worker seed from
    """
    worker_seed = base_seed + worker_id
    np.random.seed(worker_seed)
    random.seed(worker_seed)
    torch.manual_seed(worker_seed)
