"""
Variational Autoencoder models for human behavior pattern learning.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, <PERSON><PERSON>, Optional
import math

from .base import BaseModel, ModelConfig, get_activation_fn


class VariationalAutoencoder(BaseModel):
    """Variational Autoencoder for learning latent behavior representations."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        
        # VAE-specific configuration
        self.latent_dim = config.model_specific.get('latent_dim', 256)
        self.beta = config.model_specific.get('beta', 1.0)  # β-VAE parameter
        self.kl_annealing = config.model_specific.get('kl_annealing', True)
        
        # Encoder network
        self.encoder = BehaviorEncoder(
            input_dim=config.hidden_dim,
            hidden_dim=config.hidden_dim,
            latent_dim=self.latent_dim,
            num_layers=config.num_layers // 2,
            dropout=config.dropout,
            activation=config.activation
        )
        
        # Decoder network
        self.decoder = BehaviorDecoder(
            latent_dim=self.latent_dim,
            hidden_dim=config.hidden_dim,
            output_dim=config.output_dim,
            num_layers=config.num_layers // 2,
            dropout=config.dropout,
            activation=config.activation
        )
        
        # Prior network for hierarchical VAE
        self.prior_network = PriorNetwork(
            context_dim=config.context_dim,
            latent_dim=self.latent_dim,
            hidden_dim=config.hidden_dim // 2
        )
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def encode(self, x: torch.Tensor, context: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """Encode input to latent distribution parameters."""
        # Get encoder outputs
        mu, log_var = self.encoder(x)
        
        # Incorporate context if available
        if context is not None:
            prior_mu, prior_log_var = self.prior_network(context)
            # Combine posterior and prior (simplified approach)
            mu = 0.7 * mu + 0.3 * prior_mu
            log_var = 0.7 * log_var + 0.3 * prior_log_var
        
        return mu, log_var
    
    def reparameterize(self, mu: torch.Tensor, log_var: torch.Tensor) -> torch.Tensor:
        """Reparameterization trick for sampling from latent distribution."""
        std = torch.exp(0.5 * log_var)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z: torch.Tensor) -> torch.Tensor:
        """Decode latent representation to output."""
        return self.decoder(z)
    
    def forward(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Forward pass through VAE."""
        # Embed inputs
        x = self.embed_inputs(batch)  # [B, T*4, H]
        
        # Encode to latent space
        mu, log_var = self.encode(x, batch.get('context'))
        
        # Sample from latent distribution
        z = self.reparameterize(mu, log_var)
        
        # Decode to reconstruction
        reconstruction = self.decode(z)
        
        # Compute losses
        recon_loss = self.reconstruction_loss(reconstruction, x)
        kl_loss = self.kl_divergence_loss(mu, log_var)
        
        # Total loss with β-VAE weighting
        total_loss = recon_loss + self.beta * kl_loss
        
        return {
            'reconstruction': reconstruction,
            'mu': mu,
            'log_var': log_var,
            'z': z,
            'recon_loss': recon_loss,
            'kl_loss': kl_loss,
            'total_loss': total_loss,
        }
    
    def reconstruction_loss(self, reconstruction: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Compute reconstruction loss."""
        return F.mse_loss(reconstruction, target, reduction='mean')
    
    def kl_divergence_loss(self, mu: torch.Tensor, log_var: torch.Tensor) -> torch.Tensor:
        """Compute KL divergence loss."""
        kl_loss = -0.5 * torch.sum(1 + log_var - mu.pow(2) - log_var.exp(), dim=-1)
        return torch.mean(kl_loss)
    
    def generate(self, context: Dict[str, torch.Tensor], length: int, 
                num_samples: int = 1) -> Dict[str, torch.Tensor]:
        """Generate behavior sequences from latent space."""
        self.eval()
        
        batch_size = context['mouse'].shape[0] if 'mouse' in context else num_samples
        device = next(self.parameters()).device
        
        with torch.no_grad():
            # Sample from prior distribution
            if 'context' in context:
                # Use context-conditioned prior
                prior_mu, prior_log_var = self.prior_network(context['context'])
                z = self.reparameterize(prior_mu, prior_log_var)
            else:
                # Sample from standard normal prior
                z = torch.randn(batch_size, self.latent_dim, device=device)
            
            # Decode to behavior sequence
            generated = self.decode(z)
            
            # Reshape to modality format
            seq_len = length
            generated = generated.view(batch_size, seq_len, 4, -1)
            
            return {
                'mouse': generated[:, :, 0, :self.config.mouse_dim],
                'keyboard': generated[:, :, 1, :self.config.keyboard_dim],
                'scroll': generated[:, :, 2, :self.config.scroll_dim],
                'context': generated[:, :, 3, :self.config.context_dim],
                'latent': z,
            }
    
    def interpolate(self, start_context: Dict[str, torch.Tensor], 
                   end_context: Dict[str, torch.Tensor], 
                   num_steps: int = 10) -> Dict[str, torch.Tensor]:
        """Interpolate between two behavior patterns in latent space."""
        self.eval()
        
        with torch.no_grad():
            # Encode start and end contexts
            start_x = self.embed_inputs(start_context)
            end_x = self.embed_inputs(end_context)
            
            start_mu, start_log_var = self.encode(start_x)
            end_mu, end_log_var = self.encode(end_x)
            
            # Sample latent representations
            start_z = self.reparameterize(start_mu, start_log_var)
            end_z = self.reparameterize(end_mu, end_log_var)
            
            # Interpolate in latent space
            interpolations = []
            for i in range(num_steps):
                alpha = i / (num_steps - 1)
                z_interp = (1 - alpha) * start_z + alpha * end_z
                
                # Decode interpolated latent
                decoded = self.decode(z_interp)
                interpolations.append(decoded)
            
            return torch.stack(interpolations, dim=1)  # [B, num_steps, ...]


class BehaviorEncoder(nn.Module):
    """Encoder network for VAE."""
    
    def __init__(self, input_dim: int, hidden_dim: int, latent_dim: int,
                 num_layers: int, dropout: float = 0.1, activation: str = "gelu"):
        super().__init__()
        
        layers = []
        current_dim = input_dim
        
        # Hidden layers
        for i in range(num_layers):
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                get_activation_fn(activation),
                nn.Dropout(dropout),
                nn.LayerNorm(hidden_dim),
            ])
            current_dim = hidden_dim
        
        self.encoder_layers = nn.Sequential(*layers)
        
        # Output layers for mean and log variance
        self.mu_layer = nn.Linear(hidden_dim, latent_dim)
        self.log_var_layer = nn.Linear(hidden_dim, latent_dim)
        
        # Global average pooling for sequence data
        self.global_pool = nn.AdaptiveAvgPool1d(1)
    
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Encode input to latent distribution parameters."""
        # x shape: [B, T, H]
        batch_size, seq_len, hidden_dim = x.shape
        
        # Flatten sequence for processing
        x = x.view(batch_size * seq_len, hidden_dim)
        
        # Pass through encoder layers
        encoded = self.encoder_layers(x)
        
        # Reshape back to sequence format
        encoded = encoded.view(batch_size, seq_len, -1)
        
        # Global pooling to get fixed-size representation
        encoded = encoded.transpose(1, 2)  # [B, H, T]
        pooled = self.global_pool(encoded).squeeze(-1)  # [B, H]
        
        # Get mean and log variance
        mu = self.mu_layer(pooled)
        log_var = self.log_var_layer(pooled)
        
        return mu, log_var


class BehaviorDecoder(nn.Module):
    """Decoder network for VAE."""
    
    def __init__(self, latent_dim: int, hidden_dim: int, output_dim: int,
                 num_layers: int, dropout: float = 0.1, activation: str = "gelu"):
        super().__init__()
        
        layers = []
        current_dim = latent_dim
        
        # Hidden layers
        for i in range(num_layers):
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                get_activation_fn(activation),
                nn.Dropout(dropout),
                nn.LayerNorm(hidden_dim),
            ])
            current_dim = hidden_dim
        
        # Output layer
        layers.append(nn.Linear(hidden_dim, output_dim))
        
        self.decoder_layers = nn.Sequential(*layers)
    
    def forward(self, z: torch.Tensor) -> torch.Tensor:
        """Decode latent representation to output."""
        return self.decoder_layers(z)


class PriorNetwork(nn.Module):
    """Prior network for context-conditioned VAE."""
    
    def __init__(self, context_dim: int, latent_dim: int, hidden_dim: int):
        super().__init__()
        
        self.network = nn.Sequential(
            nn.Linear(context_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
        )
        
        self.mu_layer = nn.Linear(hidden_dim, latent_dim)
        self.log_var_layer = nn.Linear(hidden_dim, latent_dim)
    
    def forward(self, context: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Compute prior distribution parameters from context."""
        # Average context over sequence dimension if needed
        if context.dim() == 3:  # [B, T, C]
            context = context.mean(dim=1)  # [B, C]
        
        hidden = self.network(context)
        mu = self.mu_layer(hidden)
        log_var = self.log_var_layer(hidden)
        
        return mu, log_var


class BehaviorVAE(VariationalAutoencoder):
    """Specialized VAE for human behavior modeling with additional features."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        
        # Additional behavior-specific components
        self.temporal_encoder = TemporalEncoder(
            input_dim=config.hidden_dim,
            hidden_dim=config.hidden_dim,
            num_layers=2
        )
        
        # Disentanglement regularization
        self.disentanglement_weight = config.model_specific.get('disentanglement_weight', 0.1)
        
    def forward(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Enhanced forward pass with temporal encoding."""
        # Standard VAE forward pass
        outputs = super().forward(batch)
        
        # Add temporal encoding
        x = self.embed_inputs(batch)
        temporal_features = self.temporal_encoder(x)
        
        # Disentanglement loss (simplified β-TC-VAE approach)
        z = outputs['z']
        disentanglement_loss = self.compute_disentanglement_loss(z)
        
        outputs.update({
            'temporal_features': temporal_features,
            'disentanglement_loss': disentanglement_loss,
            'total_loss': outputs['total_loss'] + self.disentanglement_weight * disentanglement_loss,
        })
        
        return outputs
    
    def compute_disentanglement_loss(self, z: torch.Tensor) -> torch.Tensor:
        """Compute disentanglement regularization loss."""
        # Simplified approach: encourage independence between latent dimensions
        z_centered = z - z.mean(dim=0, keepdim=True)
        cov_matrix = torch.mm(z_centered.t(), z_centered) / (z.shape[0] - 1)
        
        # Penalize off-diagonal elements
        off_diagonal = cov_matrix - torch.diag(torch.diag(cov_matrix))
        disentanglement_loss = torch.sum(off_diagonal ** 2)
        
        return disentanglement_loss


class TemporalEncoder(nn.Module):
    """Temporal encoder for capturing time-dependent patterns."""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int):
        super().__init__()
        
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=0.1 if num_layers > 1 else 0,
            bidirectional=True
        )
        
        self.output_projection = nn.Linear(hidden_dim * 2, hidden_dim)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Encode temporal patterns."""
        lstm_out, _ = self.lstm(x)
        return self.output_projection(lstm_out)
