"""
Model architectures for human behavior learning.

This module contains state-of-the-art neural network architectures for learning
and generating human interaction patterns.
"""

from typing import Optional
from .base import BaseModel, ModelConfig
from .transformer import TransformerEncoder, MultiModalTransformer
from .vae import VariationalAutoencoder, BehaviorVAE
from .gan import GenerativeAdversarialNetwork, BehaviorGAN
from .hybrid import HybridBehaviorModel
from .temporal import TemporalConvNet, HierarchicalTCN
from .attention import MultiScaleAttention, CrossModalAttention
from .rl import PolicyNetwork, ValueNetwork, ActorCritic
from .main import HumanBehaviorModel


def create_model(model_type: str, config: Optional[ModelConfig] = None) -> HumanBehaviorModel:
    """
    Factory function to create human behavior models.

    Args:
        model_type: Type of model to create ('hybrid', 'transformer', 'vae', 'gan')
        config: Model configuration. If None, uses default configuration.

    Returns:
        HumanBehaviorModel instance

    Raises:
        ValueError: If model_type is not supported
    """
    return HumanBehaviorModel(model_type=model_type, config=config)


__all__ = [
    # Base classes
    "BaseModel",
    "ModelConfig",

    # Core architectures
    "TransformerEncoder",
    "MultiModalTransformer",
    "VariationalAutoencoder",
    "BehaviorVAE",
    "GenerativeAdversarialNetwork",
    "BehaviorGAN",
    "HybridBehaviorModel",

    # Specialized components
    "TemporalConvNet",
    "HierarchicalTCN",
    "MultiScaleAttention",
    "CrossModalAttention",

    # Reinforcement learning
    "PolicyNetwork",
    "ValueNetwork",
    "ActorCritic",

    # Main interface
    "HumanBehaviorModel",

    # Factory function
    "create_model",
]
