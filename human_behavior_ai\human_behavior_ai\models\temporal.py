"""
Temporal Convolutional Networks for high-frequency behavioral pattern recognition.

This module implements advanced temporal modeling architectures for processing
high-frequency interaction data (144Hz) with multi-scale temporal patterns.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Tuple
import math

from .base import BaseModel, ModelConfig


class TemporalConvNet(BaseModel):
    """
    Temporal Convolutional Network with dilated convolutions for multi-scale
    temporal pattern recognition in behavioral data.
    """
    
    def __init__(
        self,
        config: ModelConfig,
        input_channels: int = 3,  # x, y, timestamp
        num_channels: List[int] = [64, 128, 256, 512],
        kernel_size: int = 3,
        dropout: float = 0.1,
        activation: str = "relu"
    ):
        super().__init__(config)
        
        self.input_channels = input_channels
        self.num_channels = num_channels
        self.kernel_size = kernel_size
        self.dropout = dropout
        
        # Build temporal convolutional layers
        layers = []
        num_levels = len(num_channels)
        
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = input_channels if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            
            layers.append(
                TemporalBlock(
                    in_channels=in_channels,
                    out_channels=out_channels,
                    kernel_size=kernel_size,
                    stride=1,
                    dilation=dilation_size,
                    padding=(kernel_size-1) * dilation_size,
                    dropout=dropout,
                    activation=activation
                )
            )
        
        self.network = nn.Sequential(*layers)
        self.output_projection = nn.Linear(num_channels[-1], config.latent_dim)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through temporal convolutional network.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, input_channels)
            
        Returns:
            Temporal features of shape (batch_size, seq_len, latent_dim)
        """
        # Transpose for conv1d: (batch, channels, seq_len)
        x = x.transpose(1, 2)
        
        # Apply temporal convolutions
        x = self.network(x)
        
        # Transpose back: (batch, seq_len, channels)
        x = x.transpose(1, 2)
        
        # Project to latent dimension
        x = self.output_projection(x)
        
        return x


class TemporalBlock(nn.Module):
    """
    Individual temporal block with dilated convolution, normalization, and residual connection.
    """
    
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        kernel_size: int,
        stride: int,
        dilation: int,
        padding: int,
        dropout: float = 0.1,
        activation: str = "relu"
    ):
        super().__init__()
        
        self.conv1 = nn.Conv1d(
            in_channels, out_channels, kernel_size,
            stride=stride, padding=padding, dilation=dilation
        )
        self.norm1 = nn.BatchNorm1d(out_channels)
        
        self.conv2 = nn.Conv1d(
            out_channels, out_channels, kernel_size,
            stride=stride, padding=padding, dilation=dilation
        )
        self.norm2 = nn.BatchNorm1d(out_channels)
        
        self.dropout = nn.Dropout(dropout)
        
        # Activation function
        if activation == "relu":
            self.activation = nn.ReLU()
        elif activation == "gelu":
            self.activation = nn.GELU()
        elif activation == "swish":
            self.activation = nn.SiLU()
        else:
            self.activation = nn.ReLU()
        
        # Residual connection
        self.downsample = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else None
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through temporal block."""
        residual = x
        
        # First convolution
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.activation(out)
        out = self.dropout(out)
        
        # Second convolution
        out = self.conv2(out)
        out = self.norm2(out)
        
        # Residual connection
        if self.downsample is not None:
            residual = self.downsample(residual)
        
        out += residual
        out = self.activation(out)
        
        return out


class HierarchicalTCN(BaseModel):
    """
    Hierarchical Temporal Convolutional Network for multi-scale behavioral analysis.
    
    Processes behavioral data at multiple temporal scales simultaneously,
    capturing both fine-grained (millisecond) and coarse-grained (second) patterns.
    """
    
    def __init__(
        self,
        config: ModelConfig,
        input_channels: int = 3,
        scales: List[int] = [1, 4, 16, 64],  # Different temporal scales
        channels_per_scale: List[int] = [64, 128, 256, 512],
        kernel_size: int = 3,
        dropout: float = 0.1
    ):
        super().__init__(config)
        
        self.scales = scales
        self.input_channels = input_channels
        
        # Create TCN for each scale
        self.scale_networks = nn.ModuleList()
        for i, scale in enumerate(scales):
            tcn = TemporalConvNet(
                config=config,
                input_channels=input_channels,
                num_channels=[channels_per_scale[i]] * 4,
                kernel_size=kernel_size,
                dropout=dropout
            )
            self.scale_networks.append(tcn)
        
        # Cross-scale attention
        total_dim = len(scales) * config.latent_dim
        self.cross_scale_attention = nn.MultiheadAttention(
            embed_dim=total_dim,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # Final projection
        self.output_projection = nn.Linear(total_dim, config.latent_dim)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through hierarchical TCN.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, input_channels)
            
        Returns:
            Multi-scale temporal features of shape (batch_size, seq_len, latent_dim)
        """
        batch_size, seq_len, _ = x.shape
        scale_features = []
        
        # Process each temporal scale
        for i, (scale, tcn) in enumerate(zip(self.scales, self.scale_networks)):
            if scale == 1:
                # Original resolution
                scale_input = x
            else:
                # Downsample for coarser scales
                scale_input = F.avg_pool1d(
                    x.transpose(1, 2), 
                    kernel_size=scale, 
                    stride=scale
                ).transpose(1, 2)
            
            # Apply TCN
            scale_feat = tcn(scale_input)
            
            # Upsample back to original resolution if needed
            if scale > 1:
                scale_feat = F.interpolate(
                    scale_feat.transpose(1, 2),
                    size=seq_len,
                    mode='linear',
                    align_corners=False
                ).transpose(1, 2)
            
            scale_features.append(scale_feat)
        
        # Concatenate multi-scale features
        multi_scale_features = torch.cat(scale_features, dim=-1)
        
        # Apply cross-scale attention
        attended_features, _ = self.cross_scale_attention(
            multi_scale_features,
            multi_scale_features,
            multi_scale_features
        )
        
        # Final projection
        output = self.output_projection(attended_features)
        
        return output


class AdaptiveTemporalPooling(nn.Module):
    """
    Adaptive temporal pooling for variable-length sequences.
    """
    
    def __init__(self, input_dim: int, output_dim: int):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        
        self.attention = nn.Linear(input_dim, 1)
        self.projection = nn.Linear(input_dim, output_dim)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Apply adaptive temporal pooling.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, input_dim)
            mask: Optional mask for variable-length sequences
            
        Returns:
            Pooled features of shape (batch_size, output_dim)
        """
        # Compute attention weights
        attention_weights = self.attention(x).squeeze(-1)  # (batch_size, seq_len)
        
        if mask is not None:
            attention_weights = attention_weights.masked_fill(~mask, float('-inf'))
        
        attention_weights = F.softmax(attention_weights, dim=-1)
        
        # Apply attention pooling
        pooled = torch.sum(x * attention_weights.unsqueeze(-1), dim=1)
        
        # Project to output dimension
        output = self.projection(pooled)
        
        return output
