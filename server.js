const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs').promises;
const path = require('path');
const compression = require('compression');
const helmet = require('helmet');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet({
  contentSecurityPolicy: false // Allow inline scripts for development
}));
app.use(compression());
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));

// Serve static files
app.use(express.static('public'));

// Ensure data directories exist
const ensureDirectories = async () => {
  try {
    await fs.mkdir('data', { recursive: true });
    await fs.mkdir('data/sessions', { recursive: true });
  } catch (error) {
    console.error('Error creating directories:', error);
  }
};

// API Routes

// Start new recording session
app.post('/api/sessions/start', async (req, res) => {
  try {
    const sessionId = uuidv4();
    const session = {
      id: sessionId,
      startTime: Date.now(),
      status: 'active',
      metadata: req.body.metadata || {},
      dataPoints: 0
    };

    await fs.writeFile(
      path.join('data/sessions', `${sessionId}.json`),
      JSON.stringify(session, null, 2)
    );

    res.json({ sessionId, status: 'started' });
  } catch (error) {
    console.error('Error starting session:', error);
    res.status(500).json({ error: 'Failed to start session' });
  }
});

// Save interaction data
app.post('/api/sessions/:id/data', async (req, res) => {
  try {
    const { id } = req.params;
    const { data } = req.body;

    const sessionPath = path.join('data/sessions', `${id}.json`);
    const session = JSON.parse(await fs.readFile(sessionPath, 'utf8'));

    // Create data file if it doesn't exist
    const dataPath = path.join('data/sessions', `${id}_data.json`);
    let existingData = [];
    
    try {
      const dataContent = await fs.readFile(dataPath, 'utf8');
      existingData = JSON.parse(dataContent);
    } catch (error) {
      // File doesn't exist yet, start with empty array
    }

    // Append new data
    existingData.push(...data);
    session.dataPoints = existingData.length;
    session.lastUpdate = Date.now();

    // Save both session metadata and data
    await Promise.all([
      fs.writeFile(sessionPath, JSON.stringify(session, null, 2)),
      fs.writeFile(dataPath, JSON.stringify(existingData, null, 2))
    ]);

    res.json({ status: 'saved', dataPoints: session.dataPoints });
  } catch (error) {
    console.error('Error saving data:', error);
    res.status(500).json({ error: 'Failed to save data' });
  }
});

// Complete session
app.post('/api/sessions/:id/complete', async (req, res) => {
  try {
    const { id } = req.params;
    const sessionPath = path.join('data/sessions', `${id}.json`);
    const session = JSON.parse(await fs.readFile(sessionPath, 'utf8'));

    session.status = 'completed';
    session.endTime = Date.now();
    session.duration = session.endTime - session.startTime;

    await fs.writeFile(sessionPath, JSON.stringify(session, null, 2));

    res.json({ status: 'completed', duration: session.duration });
  } catch (error) {
    console.error('Error completing session:', error);
    res.status(500).json({ error: 'Failed to complete session' });
  }
});

// Get all sessions
app.get('/api/sessions', async (req, res) => {
  try {
    const files = await fs.readdir('data/sessions');
    const sessionFiles = files.filter(f => f.endsWith('.json') && !f.includes('_data'));
    
    const sessions = await Promise.all(
      sessionFiles.map(async (file) => {
        const content = await fs.readFile(path.join('data/sessions', file), 'utf8');
        return JSON.parse(content);
      })
    );

    res.json(sessions.sort((a, b) => b.startTime - a.startTime));
  } catch (error) {
    console.error('Error fetching sessions:', error);
    res.status(500).json({ error: 'Failed to fetch sessions' });
  }
});

// Get specific session data
app.get('/api/sessions/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const sessionPath = path.join('data/sessions', `${id}.json`);
    const dataPath = path.join('data/sessions', `${id}_data.json`);

    const session = JSON.parse(await fs.readFile(sessionPath, 'utf8'));
    let data = [];

    try {
      data = JSON.parse(await fs.readFile(dataPath, 'utf8'));
    } catch (error) {
      // No data file yet
    }

    res.json({ session, data });
  } catch (error) {
    console.error('Error fetching session:', error);
    res.status(500).json({ error: 'Failed to fetch session' });
  }
});

// Analysis endpoints
app.get('/api/analysis/summary', async (req, res) => {
  try {
    const files = await fs.readdir('data/sessions');
    const sessionFiles = files.filter(f => f.endsWith('.json') && !f.includes('_data'));
    
    let totalSessions = 0;
    let totalDataPoints = 0;
    let totalDuration = 0;
    let completedSessions = 0;

    for (const file of sessionFiles) {
      const content = await fs.readFile(path.join('data/sessions', file), 'utf8');
      const session = JSON.parse(content);
      
      totalSessions++;
      totalDataPoints += session.dataPoints || 0;
      
      if (session.status === 'completed' && session.duration) {
        totalDuration += session.duration;
        completedSessions++;
      }
    }

    res.json({
      totalSessions,
      completedSessions,
      totalDataPoints,
      averageDuration: completedSessions > 0 ? totalDuration / completedSessions : 0,
      averageDataPoints: totalSessions > 0 ? totalDataPoints / totalSessions : 0
    });
  } catch (error) {
    console.error('Error generating summary:', error);
    res.status(500).json({ error: 'Failed to generate summary' });
  }
});

// Delete session
app.delete('/api/sessions/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const sessionPath = path.join('data/sessions', `${id}.json`);
    const dataPath = path.join('data/sessions', `${id}_data.json`);

    // Check if session exists
    try {
      await fs.access(sessionPath);
    } catch {
      return res.status(404).json({ error: 'Session not found' });
    }

    // Delete both session metadata and data files
    const deletePromises = [fs.unlink(sessionPath)];

    // Check if data file exists before trying to delete it
    try {
      await fs.access(dataPath);
      deletePromises.push(fs.unlink(dataPath));
    } catch {
      // Data file doesn't exist, that's okay
    }

    await Promise.all(deletePromises);

    res.json({ status: 'deleted', message: 'Session deleted successfully' });
  } catch (error) {
    console.error('Error deleting session:', error);
    res.status(500).json({ error: 'Failed to delete session' });
  }
});

// Initialize server
const startServer = async () => {
  await ensureDirectories();
  
  app.listen(PORT, () => {
    console.log(`🚀 Interaction Recorder Server running on http://localhost:${PORT}`);
    console.log(`📊 Data directory: ${path.resolve('data')}`);
  });
};

startServer().catch(console.error);
