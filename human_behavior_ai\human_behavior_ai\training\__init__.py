"""
Training pipeline for human behavior models.
"""

from .base import BaseTrainer, TrainingConfig
from .multi_stage import MultiStageTrainer
from .adversarial import AdversarialTrainer
from .reinforcement import ReinforcementTrainer
from .hybrid_trainer import HybridBehaviorTrainer as HybridTrainer

__all__ = [
    # Base classes
    "BaseTrainer",
    "TrainingConfig",

    # Specialized trainers
    "MultiStageTrainer",
    "AdversarialTrainer",
    "ReinforcementTrainer",
    "HybridTrainer",

    # Utilities
    "get_trainer",
]


def get_trainer(model_type: str, model, config, train_loader, val_loader=None):
    """Get appropriate trainer for model type."""
    trainers = {
        'transformer': MultiStageTrainer,
        'vae': MultiStageTrainer,
        'gan': AdversarialTrainer,
        'hybrid': HybridTrainer,
    }

    trainer_class = trainers.get(model_type, MultiStageTrainer)
    return trainer_class(model, config, train_loader, val_loader)
