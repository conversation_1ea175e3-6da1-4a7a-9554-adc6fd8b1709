#!/usr/bin/env python3
"""
Simple test to verify the project structure and imports work correctly.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all main modules can be imported."""
    print("🧪 Testing HumanBehaviorAI imports...")
    
    try:
        # Test main package
        import human_behavior_ai
        print("✅ human_behavior_ai package imported successfully")
        
        # Test models
        from human_behavior_ai.models import ModelConfig
        print("✅ ModelConfig imported successfully")
        
        from human_behavior_ai.models.base import BaseModel
        print("✅ BaseModel imported successfully")
        
        from human_behavior_ai.models.transformer import MultiModalTransformer
        print("✅ MultiModalTransformer imported successfully")
        
        from human_behavior_ai.models.vae import VariationalAutoencoder
        print("✅ VariationalAutoencoder imported successfully")
        
        from human_behavior_ai.models.gan import GenerativeAdversarialNetwork
        print("✅ GenerativeAdversarialNetwork imported successfully")
        
        from human_behavior_ai.models.hybrid import HybridBehaviorModel
        print("✅ HybridBehaviorModel imported successfully")
        
        from human_behavior_ai.models.main import HumanBehaviorModel
        print("✅ HumanBehaviorModel imported successfully")
        
        # Test data
        from human_behavior_ai.data.dataset import InteractionDataset, BehaviorSequence
        print("✅ Data classes imported successfully")
        
        # Test training
        from human_behavior_ai.training import get_trainer
        print("✅ Training modules imported successfully")
        
        # Test NoDriver integration
        from human_behavior_ai.nodriver_integration import NoDriverIntegration
        print("✅ NoDriverIntegration imported successfully")
        
        print("\n🎉 All imports successful! Project structure is correct.")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_model_creation():
    """Test basic model creation without dependencies."""
    print("\n🧪 Testing model creation...")
    
    try:
        from human_behavior_ai.models import ModelConfig
        
        # Create a basic config
        config = ModelConfig(
            hidden_dim=128,
            num_layers=4,
            num_heads=8,
            mouse_dim=16,
            keyboard_dim=16,
            scroll_dim=8,
            context_dim=24,
        )
        
        print("✅ ModelConfig created successfully")
        print(f"   Hidden dim: {config.hidden_dim}")
        print(f"   Num layers: {config.num_layers}")
        print(f"   Num heads: {config.num_heads}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation error: {e}")
        return False


def test_project_info():
    """Display project information."""
    print("\n📊 Project Information:")
    print("=" * 50)
    
    # Count files
    total_files = 0
    python_files = 0
    
    for file_path in project_root.rglob("*"):
        if file_path.is_file():
            total_files += 1
            if file_path.suffix == ".py":
                python_files += 1
    
    print(f"📁 Total files: {total_files}")
    print(f"🐍 Python files: {python_files}")
    
    # Show directory structure
    print(f"\n📂 Project structure:")
    for item in sorted(project_root.iterdir()):
        if item.is_dir() and not item.name.startswith('.'):
            print(f"   📁 {item.name}/")
            # Show subdirectories
            for subitem in sorted(item.iterdir()):
                if subitem.is_dir():
                    print(f"      📁 {subitem.name}/")
                elif subitem.suffix == ".py":
                    print(f"      🐍 {subitem.name}")
        elif item.suffix in [".py", ".md", ".txt", ".yaml", ".yml"]:
            icon = {"py": "🐍", "md": "📚", "txt": "📄", "yaml": "⚙️", "yml": "⚙️"}.get(item.suffix[1:], "📄")
            print(f"   {icon} {item.name}")


def main():
    """Run all tests."""
    print("🚀 HumanBehaviorAI Project Structure Test")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    # Test model creation
    model_ok = test_model_creation()
    
    # Show project info
    test_project_info()
    
    # Summary
    print("\n" + "=" * 50)
    if imports_ok and model_ok:
        print("🎉 All tests passed! Project is ready for use.")
        print("\n📋 Next steps:")
        print("1. Install dependencies: pip install torch numpy pandas matplotlib")
        print("2. Prepare your interaction data")
        print("3. Run training: python scripts/train.py")
        print("4. Generate behaviors: python scripts/generate.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return imports_ok and model_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
