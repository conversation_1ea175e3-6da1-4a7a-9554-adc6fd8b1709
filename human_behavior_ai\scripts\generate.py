#!/usr/bin/env python3
"""
Generation script for human behavior AI models.
"""

import argparse
import logging
import sys
from pathlib import Path
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from human_behavior_ai.models import HumanBehaviorModel
from human_behavior_ai.utils import setup_logging, set_seed
from human_behavior_ai.visualization import plot_behavior_sequence, create_behavior_animation

logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Generate human behavior sequences')
    
    # Model configuration
    parser.add_argument('--model-path', type=str, required=True,
                       help='Path to trained model')
    parser.add_argument('--device', type=str, default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='Device to use for generation')
    
    # Generation configuration
    parser.add_argument('--num-samples', type=int, default=5,
                       help='Number of samples to generate')
    parser.add_argument('--sequence-length', type=int, default=1000,
                       help='Length of generated sequences')
    parser.add_argument('--temperature', type=float, default=1.0,
                       help='Sampling temperature')
    parser.add_argument('--top-k', type=int, default=50,
                       help='Top-k sampling parameter')
    parser.add_argument('--top-p', type=float, default=0.9,
                       help='Nucleus sampling parameter')
    
    # Context configuration
    parser.add_argument('--context-file', type=str,
                       help='Path to context file (JSON)')
    parser.add_argument('--task-type', type=str, default='general',
                       choices=['general', 'clicking', 'scrolling', 'typing', 'navigation'],
                       help='Type of task to generate behavior for')
    parser.add_argument('--viewport-width', type=int, default=1920,
                       help='Viewport width for context')
    parser.add_argument('--viewport-height', type=int, default=1080,
                       help='Viewport height for context')
    
    # Output configuration
    parser.add_argument('--output-dir', type=str, default='./generated',
                       help='Output directory for generated sequences')
    parser.add_argument('--output-format', type=str, default='json',
                       choices=['json', 'csv', 'numpy'],
                       help='Output format for generated data')
    parser.add_argument('--visualize', action='store_true',
                       help='Create visualizations of generated sequences')
    parser.add_argument('--animate', action='store_true',
                       help='Create animations of generated sequences')
    
    # Generation strategy
    parser.add_argument('--generation-strategy', type=str, default='hybrid',
                       choices=['transformer', 'vae', 'gan', 'hybrid'],
                       help='Generation strategy to use')
    parser.add_argument('--diversity-method', type=str, default='temperature',
                       choices=['temperature', 'nucleus', 'top_k'],
                       help='Method for ensuring diversity')
    
    # Analysis
    parser.add_argument('--analyze', action='store_true',
                       help='Analyze generated sequences')
    parser.add_argument('--compare-real', type=str,
                       help='Path to real data for comparison')
    
    # Debugging
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    return parser.parse_args()


def load_context(context_file: str) -> Dict:
    """Load context from file."""
    with open(context_file, 'r') as f:
        return json.load(f)


def create_default_context(args) -> Dict:
    """Create default context based on arguments."""
    context = {
        'task_type': args.task_type,
        'viewport': {
            'width': args.viewport_width,
            'height': args.viewport_height
        },
        'timestamp': 0.0,
        'session_id': 'generated',
    }
    
    # Task-specific context
    if args.task_type == 'clicking':
        context.update({
            'target_elements': [
                {'x': args.viewport_width * 0.3, 'y': args.viewport_height * 0.4},
                {'x': args.viewport_width * 0.7, 'y': args.viewport_height * 0.6},
            ]
        })
    elif args.task_type == 'scrolling':
        context.update({
            'page_height': args.viewport_height * 3,
            'scroll_target': args.viewport_height * 2
        })
    elif args.task_type == 'typing':
        context.update({
            'text_fields': [
                {'x': args.viewport_width * 0.5, 'y': args.viewport_height * 0.3},
            ],
            'target_text': 'This is a sample text for typing simulation.'
        })
    
    return context


def context_to_tensor(context: Dict, sequence_length: int, device: torch.device) -> Dict[str, torch.Tensor]:
    """Convert context dictionary to tensor format."""
    batch_size = 1
    
    # Create context tensor
    context_dim = 24  # Should match model configuration
    context_tensor = torch.zeros(batch_size, sequence_length, context_dim, device=device)
    
    # Encode basic context information
    if 'viewport' in context:
        context_tensor[0, :, 0] = context['viewport']['width'] / 1920.0  # Normalize
        context_tensor[0, :, 1] = context['viewport']['height'] / 1080.0
    
    # Encode task type
    task_types = ['general', 'clicking', 'scrolling', 'typing', 'navigation']
    task_type = context.get('task_type', 'general')
    if task_type in task_types:
        task_idx = task_types.index(task_type)
        context_tensor[0, :, 2 + task_idx] = 1.0
    
    # Create dummy data for other modalities
    mouse_tensor = torch.zeros(batch_size, sequence_length, 16, device=device)
    keyboard_tensor = torch.zeros(batch_size, sequence_length, 16, device=device)
    scroll_tensor = torch.zeros(batch_size, sequence_length, 8, device=device)
    
    # Set initial mouse position to center of viewport
    if 'viewport' in context:
        center_x = context['viewport']['width'] / 2
        center_y = context['viewport']['height'] / 2
        mouse_tensor[0, 0, 0] = center_x / 1920.0  # Normalize
        mouse_tensor[0, 0, 1] = center_y / 1080.0
    
    return {
        'mouse': mouse_tensor,
        'keyboard': keyboard_tensor,
        'scroll': scroll_tensor,
        'context': context_tensor,
    }


def save_generated_data(sequences: List[Dict], output_dir: Path, format: str):
    """Save generated sequences to files."""
    output_dir.mkdir(parents=True, exist_ok=True)
    
    for i, sequence in enumerate(sequences):
        if format == 'json':
            # Convert tensors to lists for JSON serialization
            json_data = {}
            for key, tensor in sequence.items():
                if isinstance(tensor, torch.Tensor):
                    json_data[key] = tensor.cpu().numpy().tolist()
                else:
                    json_data[key] = tensor
            
            output_file = output_dir / f'generated_sequence_{i:03d}.json'
            with open(output_file, 'w') as f:
                json.dump(json_data, f, indent=2)
        
        elif format == 'numpy':
            output_file = output_dir / f'generated_sequence_{i:03d}.npz'
            numpy_data = {}
            for key, tensor in sequence.items():
                if isinstance(tensor, torch.Tensor):
                    numpy_data[key] = tensor.cpu().numpy()
                else:
                    numpy_data[key] = tensor
            
            np.savez(output_file, **numpy_data)
        
        elif format == 'csv':
            # Flatten and save as CSV
            import pandas as pd
            
            flattened_data = {}
            for key, tensor in sequence.items():
                if isinstance(tensor, torch.Tensor):
                    tensor_np = tensor.cpu().numpy()
                    if tensor_np.ndim == 3:  # [B, T, D]
                        tensor_np = tensor_np[0]  # Remove batch dimension
                    
                    for dim in range(tensor_np.shape[1]):
                        flattened_data[f'{key}_{dim}'] = tensor_np[:, dim]
            
            df = pd.DataFrame(flattened_data)
            output_file = output_dir / f'generated_sequence_{i:03d}.csv'
            df.to_csv(output_file, index=False)
    
    logger.info(f"Saved {len(sequences)} sequences to {output_dir}")


def analyze_sequences(sequences: List[Dict]) -> Dict:
    """Analyze generated sequences."""
    analysis = {
        'num_sequences': len(sequences),
        'sequence_length': sequences[0]['mouse'].shape[1] if sequences else 0,
        'statistics': {}
    }
    
    if not sequences:
        return analysis
    
    # Compute statistics for each modality
    for modality in ['mouse', 'keyboard', 'scroll']:
        if modality in sequences[0]:
            all_data = torch.cat([seq[modality] for seq in sequences], dim=0)
            
            analysis['statistics'][modality] = {
                'mean': all_data.mean().item(),
                'std': all_data.std().item(),
                'min': all_data.min().item(),
                'max': all_data.max().item(),
            }
    
    # Mouse-specific analysis
    if 'mouse' in sequences[0]:
        mouse_data = torch.cat([seq['mouse'] for seq in sequences], dim=0)
        
        # Extract positions and velocities
        positions = mouse_data[:, :, :2]  # x, y
        velocities = mouse_data[:, :, 2:5]  # vx, vy, vmag
        
        # Compute movement statistics
        distances = torch.norm(positions[:, 1:] - positions[:, :-1], dim=-1)
        
        analysis['movement_statistics'] = {
            'avg_distance_per_step': distances.mean().item(),
            'max_distance_per_step': distances.max().item(),
            'total_distance': distances.sum(dim=1).mean().item(),
            'avg_velocity_magnitude': velocities[:, :, 2].mean().item(),
            'max_velocity_magnitude': velocities[:, :, 2].max().item(),
        }
    
    return analysis


def main():
    """Main generation function."""
    args = parse_args()
    
    # Setup logging
    setup_logging(level=logging.DEBUG if args.debug else logging.INFO)
    
    # Set random seed
    set_seed(args.seed)
    
    # Determine device
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    logger.info(f"Using device: {device}")
    
    # Load model
    logger.info(f"Loading model from {args.model_path}")
    model = HumanBehaviorModel.load_pretrained(args.model_path)
    model = model.to(device).eval()
    
    # Load or create context
    if args.context_file:
        context = load_context(args.context_file)
    else:
        context = create_default_context(args)
    
    logger.info(f"Using context: {context}")
    
    # Convert context to tensor format
    context_tensors = context_to_tensor(context, args.sequence_length, device)
    
    # Generate sequences
    logger.info(f"Generating {args.num_samples} sequences...")
    
    generated_sequences = []
    
    with torch.no_grad():
        if args.diversity_method == 'temperature':
            # Generate with different temperatures
            for i in range(args.num_samples):
                temperature = args.temperature * (0.8 + 0.4 * i / args.num_samples)
                
                sequence = model.generate(
                    context=context_tensors,
                    length=args.sequence_length,
                    generation_strategy=args.generation_strategy,
                    temperature=temperature
                )
                
                generated_sequences.append(sequence)
        
        else:
            # Use model's diverse generation method
            sequences = model.generate_diverse_behaviors(
                context=context_tensors,
                num_samples=args.num_samples,
                length=args.sequence_length,
                diversity_method=args.diversity_method
            )
            generated_sequences.extend(sequences)
    
    logger.info(f"Generated {len(generated_sequences)} sequences")
    
    # Create output directory
    output_dir = Path(args.output_dir)
    
    # Save generated data
    save_generated_data(generated_sequences, output_dir, args.output_format)
    
    # Analyze sequences
    if args.analyze:
        logger.info("Analyzing generated sequences...")
        analysis = analyze_sequences(generated_sequences)
        
        # Save analysis
        analysis_file = output_dir / 'analysis.json'
        with open(analysis_file, 'w') as f:
            json.dump(analysis, f, indent=2)
        
        logger.info(f"Analysis saved to {analysis_file}")
        
        # Print summary
        print("\n=== Generation Analysis ===")
        print(f"Number of sequences: {analysis['num_sequences']}")
        print(f"Sequence length: {analysis['sequence_length']}")
        
        if 'movement_statistics' in analysis:
            stats = analysis['movement_statistics']
            print(f"Average distance per step: {stats['avg_distance_per_step']:.2f}")
            print(f"Total distance per sequence: {stats['total_distance']:.2f}")
            print(f"Average velocity: {stats['avg_velocity_magnitude']:.2f}")
    
    # Create visualizations
    if args.visualize:
        logger.info("Creating visualizations...")
        
        viz_dir = output_dir / 'visualizations'
        viz_dir.mkdir(exist_ok=True)
        
        for i, sequence in enumerate(generated_sequences[:5]):  # Limit to first 5
            fig = plot_behavior_sequence(sequence)
            fig.savefig(viz_dir / f'sequence_{i:03d}.png', dpi=150, bbox_inches='tight')
            plt.close(fig)
        
        logger.info(f"Visualizations saved to {viz_dir}")
    
    # Create animations
    if args.animate:
        logger.info("Creating animations...")
        
        anim_dir = output_dir / 'animations'
        anim_dir.mkdir(exist_ok=True)
        
        for i, sequence in enumerate(generated_sequences[:3]):  # Limit to first 3
            animation = create_behavior_animation(sequence, context)
            animation.save(anim_dir / f'sequence_{i:03d}.gif', writer='pillow', fps=30)
        
        logger.info(f"Animations saved to {anim_dir}")
    
    logger.info("Generation completed successfully!")


if __name__ == '__main__':
    main()
