"""
Advanced attention mechanisms for multi-modal behavioral data processing.

This module implements specialized attention architectures for processing
multi-modal interaction data with cross-modal dependencies.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple
import math

from .base import BaseModel, ModelConfig


class MultiScaleAttention(nn.Module):
    """
    Multi-scale attention mechanism for capturing behavioral patterns
    at different temporal granularities.
    """
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int = 8,
        scales: list = [1, 2, 4, 8],
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.scales = scales
        self.head_dim = embed_dim // num_heads
        
        assert embed_dim % num_heads == 0, "embed_dim must be divisible by num_heads"
        
        # Multi-scale query, key, value projections
        self.scale_attentions = nn.ModuleList()
        for scale in scales:
            attention = nn.MultiheadAttention(
                embed_dim=embed_dim,
                num_heads=num_heads,
                dropout=dropout,
                batch_first=True
            )
            self.scale_attentions.append(attention)
        
        # Scale fusion
        self.scale_fusion = nn.Linear(len(scales) * embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(embed_dim)
        
    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        attn_mask: Optional[torch.Tensor] = None,
        key_padding_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply multi-scale attention.
        
        Args:
            query: Query tensor of shape (batch_size, seq_len, embed_dim)
            key: Key tensor of shape (batch_size, seq_len, embed_dim)
            value: Value tensor of shape (batch_size, seq_len, embed_dim)
            attn_mask: Optional attention mask
            key_padding_mask: Optional key padding mask
            
        Returns:
            Tuple of (attended_output, attention_weights)
        """
        batch_size, seq_len, _ = query.shape
        scale_outputs = []
        scale_weights = []
        
        # Apply attention at each scale
        for i, (scale, attention) in enumerate(zip(self.scales, self.scale_attentions)):
            if scale == 1:
                # Original scale
                scale_query, scale_key, scale_value = query, key, value
                scale_mask = attn_mask
                scale_padding_mask = key_padding_mask
            else:
                # Downsample for coarser scales
                scale_query = F.avg_pool1d(
                    query.transpose(1, 2), kernel_size=scale, stride=scale
                ).transpose(1, 2)
                scale_key = F.avg_pool1d(
                    key.transpose(1, 2), kernel_size=scale, stride=scale
                ).transpose(1, 2)
                scale_value = F.avg_pool1d(
                    value.transpose(1, 2), kernel_size=scale, stride=scale
                ).transpose(1, 2)
                
                # Adjust masks for downsampled sequences
                scale_mask = None  # Simplified for now
                scale_padding_mask = None
            
            # Apply attention
            scale_out, scale_attn = attention(
                scale_query, scale_key, scale_value,
                attn_mask=scale_mask,
                key_padding_mask=scale_padding_mask
            )
            
            # Upsample back to original resolution if needed
            if scale > 1:
                scale_out = F.interpolate(
                    scale_out.transpose(1, 2),
                    size=seq_len,
                    mode='linear',
                    align_corners=False
                ).transpose(1, 2)
            
            scale_outputs.append(scale_out)
            scale_weights.append(scale_attn)
        
        # Fuse multi-scale outputs
        fused_output = torch.cat(scale_outputs, dim=-1)
        fused_output = self.scale_fusion(fused_output)
        
        # Residual connection and normalization
        output = self.layer_norm(query + self.dropout(fused_output))
        
        # Average attention weights across scales
        avg_weights = torch.stack(scale_weights).mean(dim=0)
        
        return output, avg_weights


class CrossModalAttention(nn.Module):
    """
    Cross-modal attention for processing multi-modal behavioral data
    (mouse movements, keyboard input, scrolling, etc.).
    """
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int = 8,
        num_modalities: int = 3,  # mouse, keyboard, scroll
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.num_modalities = num_modalities
        
        # Modality-specific projections
        self.modality_projections = nn.ModuleList([
            nn.Linear(embed_dim, embed_dim) for _ in range(num_modalities)
        ])
        
        # Cross-modal attention layers
        self.cross_attentions = nn.ModuleList()
        for i in range(num_modalities):
            for j in range(num_modalities):
                if i != j:  # Cross-modal only
                    attention = nn.MultiheadAttention(
                        embed_dim=embed_dim,
                        num_heads=num_heads,
                        dropout=dropout,
                        batch_first=True
                    )
                    self.cross_attentions.append(attention)
        
        # Fusion layers
        self.fusion_projection = nn.Linear(num_modalities * embed_dim, embed_dim)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(embed_dim)
        
    def forward(
        self,
        modality_features: list,  # List of tensors, one per modality
        attn_mask: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        Apply cross-modal attention.
        
        Args:
            modality_features: List of feature tensors, each of shape 
                              (batch_size, seq_len, embed_dim)
            attn_mask: Optional attention mask
            
        Returns:
            Fused multi-modal features of shape (batch_size, seq_len, embed_dim)
        """
        assert len(modality_features) == self.num_modalities
        
        # Project modality features
        projected_features = []
        for i, (features, projection) in enumerate(zip(modality_features, self.modality_projections)):
            projected = projection(features)
            projected_features.append(projected)
        
        # Apply cross-modal attention
        attended_features = []
        attention_idx = 0
        
        for i in range(self.num_modalities):
            modality_attended = []
            
            for j in range(self.num_modalities):
                if i == j:
                    # Self-attention (identity for now)
                    modality_attended.append(projected_features[i])
                else:
                    # Cross-modal attention
                    cross_attention = self.cross_attentions[attention_idx]
                    attended, _ = cross_attention(
                        projected_features[i],  # query
                        projected_features[j],  # key
                        projected_features[j],  # value
                        attn_mask=attn_mask
                    )
                    modality_attended.append(attended)
                    attention_idx += 1
            
            # Combine attended features for this modality
            combined = torch.stack(modality_attended).mean(dim=0)
            attended_features.append(combined)
        
        # Fuse all modalities
        fused = torch.cat(attended_features, dim=-1)
        fused = self.fusion_projection(fused)
        
        # Residual connection with original features (using first modality as reference)
        output = self.layer_norm(projected_features[0] + self.dropout(fused))
        
        return output


class PositionalEncoding(nn.Module):
    """
    Positional encoding for temporal sequences with high-frequency data.
    """
    
    def __init__(self, embed_dim: int, max_len: int = 10000, dropout: float = 0.1):
        super().__init__()
        
        self.dropout = nn.Dropout(dropout)
        
        # Create positional encoding matrix
        pe = torch.zeros(max_len, embed_dim)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        div_term = torch.exp(
            torch.arange(0, embed_dim, 2).float() * 
            (-math.log(10000.0) / embed_dim)
        )
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Add positional encoding to input embeddings.
        
        Args:
            x: Input tensor of shape (batch_size, seq_len, embed_dim)
            
        Returns:
            Tensor with positional encoding added
        """
        x = x + self.pe[:x.size(1), :].transpose(0, 1)
        return self.dropout(x)


class AdaptiveAttention(nn.Module):
    """
    Adaptive attention mechanism that adjusts attention patterns
    based on behavioral context and user patterns.
    """
    
    def __init__(
        self,
        embed_dim: int,
        num_heads: int = 8,
        context_dim: int = 64,
        dropout: float = 0.1
    ):
        super().__init__()
        
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.context_dim = context_dim
        
        # Context encoder
        self.context_encoder = nn.Sequential(
            nn.Linear(embed_dim, context_dim),
            nn.ReLU(),
            nn.Linear(context_dim, context_dim)
        )
        
        # Adaptive attention weights
        self.attention_adapter = nn.Sequential(
            nn.Linear(context_dim, num_heads),
            nn.Softmax(dim=-1)
        )
        
        # Base attention mechanism
        self.base_attention = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(embed_dim)
        
    def forward(
        self,
        query: torch.Tensor,
        key: torch.Tensor,
        value: torch.Tensor,
        attn_mask: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Apply adaptive attention.
        
        Args:
            query: Query tensor of shape (batch_size, seq_len, embed_dim)
            key: Key tensor of shape (batch_size, seq_len, embed_dim)
            value: Value tensor of shape (batch_size, seq_len, embed_dim)
            attn_mask: Optional attention mask
            
        Returns:
            Tuple of (attended_output, attention_weights)
        """
        batch_size, seq_len, _ = query.shape
        
        # Encode context from query
        context = self.context_encoder(query.mean(dim=1))  # (batch_size, context_dim)
        
        # Generate adaptive attention weights
        head_weights = self.attention_adapter(context)  # (batch_size, num_heads)
        
        # Apply base attention
        attended, attn_weights = self.base_attention(
            query, key, value, attn_mask=attn_mask
        )
        
        # Apply adaptive weighting (simplified implementation)
        # In practice, this would modify the attention computation more directly
        weighted_attended = attended * head_weights.mean(dim=-1, keepdim=True).unsqueeze(1)
        
        # Residual connection and normalization
        output = self.layer_norm(query + self.dropout(weighted_attended))
        
        return output, attn_weights
