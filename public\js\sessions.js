// Sessions Management Module
class SessionsManager {
    constructor() {
        this.sessions = [];
        this.filteredSessions = [];
        this.currentFilter = '';
        this.sortBy = 'startTime';
        this.sortOrder = 'desc';
    }

    async loadSessions() {
        try {
            const response = await fetch('/api/sessions');
            this.sessions = await response.json();
            this.filteredSessions = [...this.sessions];
            this.renderSessions();
            return this.sessions;
        } catch (error) {
            console.error('Error loading sessions:', error);
            this.showError('Failed to load sessions');
            return [];
        }
    }

    renderSessions() {
        const container = document.getElementById('sessions-list');
        
        if (this.filteredSessions.length === 0) {
            container.innerHTML = this.getEmptyStateHTML();
            return;
        }

        // Sort sessions
        this.sortSessions();

        container.innerHTML = this.filteredSessions.map(session => 
            this.getSessionCardHTML(session)
        ).join('');
    }

    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <i class="fas fa-folder-open"></i>
                <h3>No Sessions Found</h3>
                <p>${this.currentFilter ? 'No sessions match your search criteria' : 'Start recording to create your first session'}</p>
                ${this.currentFilter ? `<button onclick="sessionsManager.clearFilter()" class="action-button">Clear Filter</button>` : ''}
            </div>
        `;
    }

    getSessionCardHTML(session) {
        const statusClass = session.status === 'completed' ? 'completed' : 'active';
        const statusIcon = session.status === 'completed' ? 'fa-check-circle' : 'fa-clock';
        const duration = session.duration ? this.formatDuration(session.duration) : 'In progress';
        const dataPoints = (session.dataPoints || 0).toLocaleString();
        const date = new Date(session.startTime).toLocaleDateString();
        const time = new Date(session.startTime).toLocaleTimeString();

        return `
            <div class="session-card" data-session-id="${session.id}">
                <div class="session-header">
                    <div class="session-status ${statusClass}">
                        <i class="fas ${statusIcon}"></i>
                        ${session.status}
                    </div>
                    <div class="session-date">
                        <div>${date}</div>
                        <div style="font-size: 0.75rem; opacity: 0.7;">${time}</div>
                    </div>
                </div>
                
                <div class="session-details">
                    <div class="detail-item">
                        <strong>Duration:</strong> 
                        <span>${duration}</span>
                    </div>
                    <div class="detail-item">
                        <strong>Data Points:</strong> 
                        <span>${dataPoints}</span>
                    </div>
                    <div class="detail-item">
                        <strong>Session ID:</strong> 
                        <span style="font-family: monospace; font-size: 0.875rem;">${session.id.substring(0, 8)}...</span>
                    </div>
                </div>

                <div class="session-metadata">
                    ${this.getMetadataHTML(session.metadata)}
                </div>

                <div class="session-actions">
                    <button class="action-btn view-btn" onclick="sessionsManager.viewSession('${session.id}')">
                        <i class="fas fa-eye"></i> View
                    </button>
                    <button class="action-btn analyze-btn" onclick="sessionsManager.analyzeSession('${session.id}')">
                        <i class="fas fa-chart-line"></i> Analyze
                    </button>
                    <button class="action-btn download-btn" onclick="sessionsManager.downloadSession('${session.id}')">
                        <i class="fas fa-download"></i> Export
                    </button>
                    <button class="action-btn delete-btn" onclick="sessionsManager.deleteSession('${session.id}')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;
    }

    getMetadataHTML(metadata) {
        if (!metadata) return '';

        return `
            <div class="metadata-section">
                <h4>Session Details</h4>
                <div class="metadata-grid">
                    ${metadata.screenResolution ? `<div class="metadata-item"><strong>Screen:</strong> ${metadata.screenResolution}</div>` : ''}
                    ${metadata.viewportSize ? `<div class="metadata-item"><strong>Viewport:</strong> ${metadata.viewportSize}</div>` : ''}
                    ${metadata.captureFrequency ? `<div class="metadata-item"><strong>Frequency:</strong> ${metadata.captureFrequency}Hz</div>` : ''}
                    ${metadata.userAgent ? `<div class="metadata-item"><strong>Browser:</strong> ${this.getBrowserName(metadata.userAgent)}</div>` : ''}
                </div>
            </div>
        `;
    }

    getBrowserName(userAgent) {
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return 'Unknown';
    }

    sortSessions() {
        this.filteredSessions.sort((a, b) => {
            let aValue = a[this.sortBy];
            let bValue = b[this.sortBy];

            // Handle different data types
            if (typeof aValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            if (this.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });
    }

    filterSessions(query) {
        this.currentFilter = query.toLowerCase();
        
        if (!query) {
            this.filteredSessions = [...this.sessions];
        } else {
            this.filteredSessions = this.sessions.filter(session => {
                const searchText = [
                    session.id,
                    session.status,
                    new Date(session.startTime).toLocaleDateString(),
                    new Date(session.startTime).toLocaleTimeString(),
                    session.dataPoints?.toString() || '',
                    session.metadata?.userAgent || ''
                ].join(' ').toLowerCase();

                return searchText.includes(query);
            });
        }

        this.renderSessions();
    }

    clearFilter() {
        this.currentFilter = '';
        document.getElementById('session-search').value = '';
        this.filteredSessions = [...this.sessions];
        this.renderSessions();
    }

    async viewSession(sessionId) {
        try {
            const response = await fetch(`/api/sessions/${sessionId}`);
            const sessionData = await response.json();
            
            this.showSessionModal(sessionData);
        } catch (error) {
            console.error('Error viewing session:', error);
            this.showError('Failed to load session data');
        }
    }

    showSessionModal(sessionData) {
        const { session, data } = sessionData;
        
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'session-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Session Details</h3>
                    <button class="modal-close" onclick="this.closest('.session-modal').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="modal-body">
                    <div class="session-overview">
                        <div class="overview-grid">
                            <div class="overview-item">
                                <strong>Session ID:</strong>
                                <span style="font-family: monospace;">${session.id}</span>
                            </div>
                            <div class="overview-item">
                                <strong>Status:</strong>
                                <span class="status-badge ${session.status}">${session.status}</span>
                            </div>
                            <div class="overview-item">
                                <strong>Start Time:</strong>
                                <span>${new Date(session.startTime).toLocaleString()}</span>
                            </div>
                            <div class="overview-item">
                                <strong>Duration:</strong>
                                <span>${session.duration ? this.formatDuration(session.duration) : 'N/A'}</span>
                            </div>
                            <div class="overview-item">
                                <strong>Data Points:</strong>
                                <span>${(session.dataPoints || 0).toLocaleString()}</span>
                            </div>
                        </div>
                    </div>

                    <div class="data-summary">
                        <h4>Data Summary</h4>
                        ${this.getDataSummaryHTML(data)}
                    </div>

                    <div class="data-preview">
                        <h4>Data Preview (First 10 entries)</h4>
                        <div class="data-table">
                            ${this.getDataPreviewHTML(data.slice(0, 10))}
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="action-button" onclick="sessionsManager.downloadSession('${session.id}')">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                    <button class="action-button secondary" onclick="this.closest('.session-modal').remove()">
                        Close
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    getDataSummaryHTML(data) {
        if (!data || data.length === 0) {
            return '<p>No data available</p>';
        }

        const summary = this.analyzeSessionData(data);
        
        return `
            <div class="summary-grid">
                <div class="summary-item">
                    <strong>Mouse Events:</strong> ${summary.mouseEvents}
                </div>
                <div class="summary-item">
                    <strong>Keyboard Events:</strong> ${summary.keyboardEvents}
                </div>
                <div class="summary-item">
                    <strong>Scroll Events:</strong> ${summary.scrollEvents}
                </div>
                <div class="summary-item">
                    <strong>Click Events:</strong> ${summary.clickEvents}
                </div>
                <div class="summary-item">
                    <strong>Avg Mouse Speed:</strong> ${summary.avgMouseSpeed.toFixed(2)} px/ms
                </div>
                <div class="summary-item">
                    <strong>Total Distance:</strong> ${summary.totalDistance.toFixed(0)} px
                </div>
            </div>
        `;
    }

    analyzeSessionData(data) {
        let mouseEvents = 0;
        let keyboardEvents = 0;
        let scrollEvents = 0;
        let clickEvents = 0;
        let totalDistance = 0;
        let totalSpeed = 0;
        let speedCount = 0;

        data.forEach(point => {
            switch (point.type) {
                case 'mouse_move':
                case 'mouse_position':
                case 'mouse_down':
                case 'mouse_up':
                    mouseEvents++;
                    if (point.data.velocity && point.data.velocity.magnitude) {
                        totalSpeed += point.data.velocity.magnitude;
                        speedCount++;
                    }
                    break;
                case 'key_down':
                case 'key_up':
                    keyboardEvents++;
                    break;
                case 'wheel':
                    scrollEvents++;
                    break;
                case 'click':
                    clickEvents++;
                    break;
            }
        });

        return {
            mouseEvents,
            keyboardEvents,
            scrollEvents,
            clickEvents,
            totalDistance,
            avgMouseSpeed: speedCount > 0 ? totalSpeed / speedCount : 0
        };
    }

    getDataPreviewHTML(data) {
        if (!data || data.length === 0) {
            return '<p>No data to preview</p>';
        }

        const headers = ['Timestamp', 'Type', 'Data'];
        const rows = data.map(point => [
            new Date(point.timestamp).toLocaleTimeString(),
            point.type,
            JSON.stringify(point.data).substring(0, 100) + '...'
        ]);

        return `
            <table class="preview-table">
                <thead>
                    <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
                </thead>
                <tbody>
                    ${rows.map(row => `<tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>`).join('')}
                </tbody>
            </table>
        `;
    }

    async analyzeSession(sessionId) {
        // Switch to analysis section and load session data
        if (window.app) {
            window.app.switchSection('analysis');
        }
        
        // TODO: Implement detailed session analysis
        console.log('Analyzing session:', sessionId);
    }

    async downloadSession(sessionId) {
        try {
            const response = await fetch(`/api/sessions/${sessionId}`);
            const sessionData = await response.json();
            
            const dataStr = JSON.stringify(sessionData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `interaction_session_${sessionId}_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            this.showSuccess('Session data exported successfully');
        } catch (error) {
            console.error('Error downloading session:', error);
            this.showError('Failed to export session data');
        }
    }

    async deleteSession(sessionId) {
        // Create a more sophisticated confirmation dialog
        const sessionCard = document.querySelector(`[data-session-id="${sessionId}"]`);
        const sessionDate = sessionCard ? sessionCard.querySelector('.session-date div').textContent : 'Unknown';

        const confirmed = confirm(
            `⚠️ Delete Session Confirmation\n\n` +
            `Session Date: ${sessionDate}\n` +
            `Session ID: ${sessionId.substring(0, 8)}...\n\n` +
            `This will permanently delete:\n` +
            `• All recorded interaction data\n` +
            `• Session metadata and statistics\n` +
            `• Analysis results\n\n` +
            `This action cannot be undone. Are you sure you want to continue?`
        );

        if (!confirmed) {
            return;
        }

        try {
            // Add loading state to delete button
            const deleteBtn = sessionCard?.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.disabled = true;
                deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
            }

            const response = await fetch(`/api/sessions/${sessionId}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            await response.json();

            // Animate session card removal
            if (sessionCard) {
                sessionCard.style.transition = 'all 0.3s ease-out';
                sessionCard.style.transform = 'translateX(-100%)';
                sessionCard.style.opacity = '0';

                setTimeout(() => {
                    sessionCard.remove();
                }, 300);
            }

            this.showSuccess('Session deleted successfully');

            // Refresh the sessions list after animation
            setTimeout(() => {
                this.loadSessions();
            }, 400);

        } catch (error) {
            console.error('Error deleting session:', error);
            this.showError('Failed to delete session: ' + error.message);

            // Restore delete button if it exists
            const deleteBtn = sessionCard?.querySelector('.delete-btn');
            if (deleteBtn) {
                deleteBtn.disabled = false;
                deleteBtn.innerHTML = '<i class="fas fa-trash"></i> Delete';
            }
        }
    }

    formatDuration(ms) {
        if (!ms) return '0m 0s';
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}m ${seconds}s`;
    }

    showError(message) {
        console.error(message);
        // TODO: Implement proper toast notifications
        alert(message);
    }

    showSuccess(message) {
        console.log(message);
        // TODO: Implement proper toast notifications
    }
}

// Initialize sessions manager
window.sessionsManager = new SessionsManager();
