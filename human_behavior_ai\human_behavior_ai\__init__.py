"""
HumanBehaviorAI: State-of-the-Art Human Interaction Pattern Learning

This package provides advanced machine learning models for learning and replicating
human interaction patterns for web automation with maximum bot detection evasion.
"""

__version__ = "1.0.0"
__author__ = "HumanBehaviorAI Team"
__email__ = "<EMAIL>"

# Core imports
from .models import (
    HumanBehaviorModel,
    TransformerEncoder,
    VariationalAutoencoder,
    GenerativeAdversarialNetwork,
    HybridBehaviorModel,
)

from .training import (
    MultiStageTrainer,
    AdversarialTrainer,
    ReinforcementTrainer,
    TrainingConfig,
)

from .data import (
    InteractionDataset,
    DataProcessor,
    DataLoader,
    BehaviorSequence,
    DataUtils,
)

# Version info
VERSION_INFO = {
    "version": __version__,
    "author": __author__,
    "email": __email__,
    "description": "State-of-the-Art Human Interaction Pattern Learning",
    "url": "https://github.com/your-org/human-behavior-ai",
}

# Configuration
DEFAULT_CONFIG = {
    "model": {
        "type": "hybrid",
        "hidden_dim": 512,
        "num_layers": 12,
        "num_heads": 8,
        "dropout": 0.1,
    },
    "training": {
        "batch_size": 32,
        "learning_rate": 1e-4,
        "num_epochs": 100,
        "warmup_steps": 1000,
    },
    "data": {
        "sequence_length": 1024,
        "sampling_rate": 144,
        "augmentation": True,
    },
    "evaluation": {
        "metrics": ["human_likeness", "detection_evasion", "diversity"],
        "benchmark_datasets": ["human_patterns", "bot_detection"],
    },
}

# Export all public APIs
__all__ = [
    # Core models
    "HumanBehaviorModel",
    "TransformerEncoder",
    "VariationalAutoencoder",
    "GenerativeAdversarialNetwork",
    "HybridBehaviorModel",

    # Training
    "MultiStageTrainer",
    "AdversarialTrainer",
    "ReinforcementTrainer",
    "TrainingConfig",

    # Data handling
    "InteractionDataset",
    "DataProcessor",
    "DataLoader",
    "BehaviorSequence",
    "DataUtils",

    # Constants
    "VERSION_INFO",
    "DEFAULT_CONFIG",
]

# Package-level configuration
import logging
import os
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Package directories
PACKAGE_ROOT = Path(__file__).parent
PROJECT_ROOT = PACKAGE_ROOT.parent
DATA_DIR = PROJECT_ROOT / "data"
MODELS_DIR = PROJECT_ROOT / "models"
CONFIGS_DIR = PROJECT_ROOT / "configs"
EXPERIMENTS_DIR = PROJECT_ROOT / "experiments"

# Create directories if they don't exist
for dir_path in [DATA_DIR, MODELS_DIR, EXPERIMENTS_DIR]:
    dir_path.mkdir(exist_ok=True)

# Environment variables
os.environ.setdefault("HUMAN_BEHAVIOR_AI_DATA_DIR", str(DATA_DIR))
os.environ.setdefault("HUMAN_BEHAVIOR_AI_MODELS_DIR", str(MODELS_DIR))
os.environ.setdefault("HUMAN_BEHAVIOR_AI_CONFIGS_DIR", str(CONFIGS_DIR))

def get_version():
    """Get the current version of the package."""
    return __version__

def get_config():
    """Get the default configuration."""
    return DEFAULT_CONFIG.copy()

def set_log_level(level):
    """Set the logging level for the package."""
    logging.getLogger("human_behavior_ai").setLevel(level)
