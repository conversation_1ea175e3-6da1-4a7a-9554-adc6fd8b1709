"""
Base training infrastructure for human behavior AI models.

This module provides the foundational training components including
base trainer classes, configuration management, and training utilities.
"""

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
import logging
from pathlib import Path
import json
import time
from abc import ABC, abstractmethod

from ..models.base import BaseModel, ModelConfig


@dataclass
class TrainingConfig:
    """Configuration for training human behavior AI models."""
    
    # Model configuration
    model_config: ModelConfig = field(default_factory=ModelConfig)
    
    # Training hyperparameters
    learning_rate: float = 1e-4
    batch_size: int = 32
    num_epochs: int = 100
    warmup_epochs: int = 10
    weight_decay: float = 1e-5
    gradient_clip_norm: float = 1.0
    
    # Optimization
    optimizer: str = "adamw"  # adamw, adam, sgd
    scheduler: str = "cosine"  # cosine, linear, exponential
    beta1: float = 0.9
    beta2: float = 0.999
    eps: float = 1e-8
    
    # Training strategy
    mixed_precision: bool = True
    gradient_accumulation_steps: int = 1
    max_grad_norm: float = 1.0
    
    # Validation and checkpointing
    validation_frequency: int = 1000  # steps
    checkpoint_frequency: int = 5000  # steps
    max_checkpoints: int = 5
    early_stopping_patience: int = 10
    
    # Logging
    log_frequency: int = 100  # steps
    wandb_project: Optional[str] = None
    wandb_entity: Optional[str] = None
    
    # Paths
    output_dir: str = "./outputs"
    checkpoint_dir: str = "./checkpoints"
    log_dir: str = "./logs"
    
    # Device and distributed training
    device: str = "auto"  # auto, cpu, cuda, cuda:0, etc.
    num_workers: int = 4
    pin_memory: bool = True
    
    # Curriculum learning
    curriculum_learning: bool = False
    curriculum_stages: List[Dict[str, Any]] = field(default_factory=list)
    
    # Regularization
    dropout_rate: float = 0.1
    label_smoothing: float = 0.0
    
    # Advanced training techniques
    use_ema: bool = False  # Exponential Moving Average
    ema_decay: float = 0.999
    use_swa: bool = False  # Stochastic Weight Averaging
    swa_start_epoch: int = 80
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        # Create directories
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        Path(self.checkpoint_dir).mkdir(parents=True, exist_ok=True)
        Path(self.log_dir).mkdir(parents=True, exist_ok=True)
        
        # Validate configuration
        assert self.learning_rate > 0, "Learning rate must be positive"
        assert self.batch_size > 0, "Batch size must be positive"
        assert self.num_epochs > 0, "Number of epochs must be positive"
        assert 0 <= self.dropout_rate <= 1, "Dropout rate must be between 0 and 1"


class BaseTrainer(ABC):
    """
    Abstract base class for training human behavior AI models.
    
    Provides common training infrastructure including:
    - Training loop management
    - Checkpointing and resuming
    - Logging and monitoring
    - Validation and early stopping
    - Mixed precision training
    """
    
    def __init__(
        self,
        model: BaseModel,
        config: TrainingConfig,
        train_dataloader: DataLoader,
        val_dataloader: Optional[DataLoader] = None,
        test_dataloader: Optional[DataLoader] = None
    ):
        self.model = model
        self.config = config
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        self.test_dataloader = test_dataloader

        # Setup logging first
        self.logger = self._setup_logger()

        # Setup device
        self.device = self._setup_device()
        self.model = self.model.to(self.device)

        # Setup optimizer and scheduler
        self.optimizer = self._setup_optimizer()
        self.scheduler = self._setup_scheduler()

        # Setup mixed precision training
        if self.config.mixed_precision:
            self.scaler = torch.cuda.amp.GradScaler()
        else:
            self.scaler = None

        # Training state
        self.current_epoch = 0
        self.current_step = 0
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        
        # Setup wandb if configured
        if self.config.wandb_project:
            self._setup_wandb()
    
    def _setup_device(self) -> torch.device:
        """Setup training device."""
        if self.config.device == "auto":
            if torch.cuda.is_available():
                device = torch.device("cuda")
                self.logger.info(f"Using CUDA device: {torch.cuda.get_device_name()}")
            else:
                device = torch.device("cpu")
                self.logger.info("Using CPU device")
        else:
            device = torch.device(self.config.device)
            self.logger.info(f"Using specified device: {device}")
        
        return device
    
    def _setup_optimizer(self) -> torch.optim.Optimizer:
        """Setup optimizer."""
        if self.config.optimizer.lower() == "adamw":
            optimizer = torch.optim.AdamW(
                self.model.parameters(),
                lr=self.config.learning_rate,
                betas=(self.config.beta1, self.config.beta2),
                eps=self.config.eps,
                weight_decay=self.config.weight_decay
            )
        elif self.config.optimizer.lower() == "adam":
            optimizer = torch.optim.Adam(
                self.model.parameters(),
                lr=self.config.learning_rate,
                betas=(self.config.beta1, self.config.beta2),
                eps=self.config.eps,
                weight_decay=self.config.weight_decay
            )
        elif self.config.optimizer.lower() == "sgd":
            optimizer = torch.optim.SGD(
                self.model.parameters(),
                lr=self.config.learning_rate,
                momentum=0.9,
                weight_decay=self.config.weight_decay
            )
        else:
            raise ValueError(f"Unsupported optimizer: {self.config.optimizer}")
        
        return optimizer
    
    def _setup_scheduler(self) -> Optional[torch.optim.lr_scheduler._LRScheduler]:
        """Setup learning rate scheduler."""
        if self.config.scheduler.lower() == "cosine":
            scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config.num_epochs,
                eta_min=self.config.learning_rate * 0.01
            )
        elif self.config.scheduler.lower() == "linear":
            scheduler = torch.optim.lr_scheduler.LinearLR(
                self.optimizer,
                start_factor=1.0,
                end_factor=0.01,
                total_iters=self.config.num_epochs
            )
        elif self.config.scheduler.lower() == "exponential":
            scheduler = torch.optim.lr_scheduler.ExponentialLR(
                self.optimizer,
                gamma=0.95
            )
        else:
            scheduler = None
        
        return scheduler
    
    def _setup_logger(self) -> logging.Logger:
        """Setup logging."""
        logger = logging.getLogger(self.__class__.__name__)
        logger.setLevel(logging.INFO)
        
        # Create file handler
        log_file = Path(self.config.log_dir) / "training.log"
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def _setup_wandb(self):
        """Setup Weights & Biases logging."""
        try:
            import wandb
            wandb.init(
                project=self.config.wandb_project,
                entity=self.config.wandb_entity,
                config=self.config.__dict__
            )
            self.use_wandb = True
        except ImportError:
            self.logger.warning("wandb not installed, skipping wandb logging")
            self.use_wandb = False
    
    @abstractmethod
    def compute_loss(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Compute training loss for a batch.
        
        Args:
            batch: Dictionary containing batch data
            
        Returns:
            Dictionary containing loss components
        """
        pass
    
    @abstractmethod
    def validation_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Perform validation step.
        
        Args:
            batch: Dictionary containing batch data
            
        Returns:
            Dictionary containing validation metrics
        """
        pass
    
    def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Perform single training step.
        
        Args:
            batch: Dictionary containing batch data
            
        Returns:
            Dictionary containing training metrics
        """
        self.model.train()
        
        # Move batch to device
        batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                for k, v in batch.items()}
        
        # Forward pass with mixed precision
        if self.scaler is not None:
            with torch.cuda.amp.autocast():
                loss_dict = self.compute_loss(batch)
        else:
            loss_dict = self.compute_loss(batch)
        
        total_loss = loss_dict['total_loss']
        
        # Backward pass
        if self.scaler is not None:
            self.scaler.scale(total_loss).backward()
            
            if (self.current_step + 1) % self.config.gradient_accumulation_steps == 0:
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config.max_grad_norm
                )
                self.scaler.step(self.optimizer)
                self.scaler.update()
                self.optimizer.zero_grad()
        else:
            total_loss.backward()
            
            if (self.current_step + 1) % self.config.gradient_accumulation_steps == 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config.max_grad_norm
                )
                self.optimizer.step()
                self.optimizer.zero_grad()
        
        return loss_dict
    
    def validate(self) -> Dict[str, float]:
        """Run validation loop."""
        if self.val_dataloader is None:
            return {}
        
        self.model.eval()
        val_metrics = {}
        
        with torch.no_grad():
            for batch in self.val_dataloader:
                batch_metrics = self.validation_step(batch)
                
                for key, value in batch_metrics.items():
                    if key not in val_metrics:
                        val_metrics[key] = []
                    val_metrics[key].append(value.item() if isinstance(value, torch.Tensor) else value)
        
        # Average metrics
        avg_metrics = {f"val_{key}": sum(values) / len(values) 
                      for key, values in val_metrics.items()}
        
        return avg_metrics
    
    def save_checkpoint(self, is_best: bool = False):
        """Save model checkpoint."""
        checkpoint = {
            'epoch': self.current_epoch,
            'step': self.current_step,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'best_val_loss': self.best_val_loss,
            'config': self.config
        }
        
        if self.scheduler is not None:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # Save regular checkpoint
        checkpoint_path = Path(self.config.checkpoint_dir) / f"checkpoint_step_{self.current_step}.pt"
        torch.save(checkpoint, checkpoint_path)
        
        # Save best checkpoint
        if is_best:
            best_path = Path(self.config.checkpoint_dir) / "best_model.pt"
            torch.save(checkpoint, best_path)
        
        self.logger.info(f"Checkpoint saved: {checkpoint_path}")
    
    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint."""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler is not None and 'scheduler_state_dict' in checkpoint:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.current_step = checkpoint['step']
        self.best_val_loss = checkpoint['best_val_loss']
        
        self.logger.info(f"Checkpoint loaded: {checkpoint_path}")
    
    def train(self):
        """Main training loop."""
        self.logger.info("Starting training...")
        
        for epoch in range(self.current_epoch, self.config.num_epochs):
            self.current_epoch = epoch
            epoch_start_time = time.time()
            
            # Training loop
            for batch_idx, batch in enumerate(self.train_dataloader):
                step_start_time = time.time()
                
                # Training step
                train_metrics = self.train_step(batch)
                
                # Update step counter
                self.current_step += 1
                
                # Logging
                if self.current_step % self.config.log_frequency == 0:
                    step_time = time.time() - step_start_time
                    self._log_metrics(train_metrics, step_time, "train")
                
                # Validation
                if self.current_step % self.config.validation_frequency == 0:
                    val_metrics = self.validate()
                    if val_metrics:
                        self._log_metrics(val_metrics, 0, "val")
                        
                        # Early stopping check
                        val_loss = val_metrics.get('val_total_loss', float('inf'))
                        if val_loss < self.best_val_loss:
                            self.best_val_loss = val_loss
                            self.patience_counter = 0
                            self.save_checkpoint(is_best=True)
                        else:
                            self.patience_counter += 1
                            
                        if self.patience_counter >= self.config.early_stopping_patience:
                            self.logger.info("Early stopping triggered")
                            return
                
                # Checkpointing
                if self.current_step % self.config.checkpoint_frequency == 0:
                    self.save_checkpoint()
            
            # End of epoch
            if self.scheduler is not None:
                self.scheduler.step()
            
            epoch_time = time.time() - epoch_start_time
            self.logger.info(f"Epoch {epoch} completed in {epoch_time:.2f}s")
        
        self.logger.info("Training completed!")
    
    def _log_metrics(self, metrics: Dict[str, float], step_time: float, prefix: str):
        """Log training metrics."""
        log_str = f"Step {self.current_step} | "
        log_str += " | ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        if step_time > 0:
            log_str += f" | Time: {step_time:.3f}s"
        
        self.logger.info(log_str)
        
        # Log to wandb if available
        if hasattr(self, 'use_wandb') and self.use_wandb:
            import wandb
            wandb_metrics = {f"{prefix}_{k}": v for k, v in metrics.items()}
            wandb_metrics['step'] = self.current_step
            wandb_metrics['epoch'] = self.current_epoch
            if step_time > 0:
                wandb_metrics['step_time'] = step_time
            wandb.log(wandb_metrics)
