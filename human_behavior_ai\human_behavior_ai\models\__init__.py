"""
Model architectures for human behavior learning.

This module contains state-of-the-art neural network architectures for learning
and generating human interaction patterns.
"""

from .base import BaseModel, ModelConfig
from .transformer import TransformerEncoder, MultiModalTransformer
from .vae import VariationalAutoencoder, BehaviorVAE
from .gan import GenerativeAdversarialNetwork, BehaviorGAN
from .hybrid import HybridBehaviorModel
from .temporal import TemporalConvNet, HierarchicalTCN
from .attention import MultiScaleAttention, CrossModalAttention
from .rl import PolicyNetwork, ValueNetwork, ActorCritic
from .main import HumanBehaviorModel

__all__ = [
    # Base classes
    "BaseModel",
    "ModelConfig",
    
    # Core architectures
    "TransformerEncoder",
    "MultiModalTransformer",
    "VariationalAutoencoder", 
    "BehaviorVAE",
    "GenerativeAdversarialNetwork",
    "BehaviorGAN",
    "HybridBehaviorModel",
    
    # Specialized components
    "TemporalConvNet",
    "HierarchicalTCN",
    "MultiScaleAttention",
    "CrossModalAttention",
    
    # Reinforcement learning
    "PolicyNetwork",
    "ValueNetwork", 
    "ActorCritic",
    
    # Main interface
    "HumanBehaviorModel",
]
