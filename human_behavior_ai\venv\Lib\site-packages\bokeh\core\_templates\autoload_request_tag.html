{#
Renders ``<script>`` tags that automatically load BokehJS (if necessary)
and then renders a Bokeh model or document. The document may be specified
as either an embedded doc ID or a server session ID. If a specific model
ID is not specified, the entire document will be rendered.

:param src_path: path to AUTOLOAD script
:type src_path: str

:param elementid: the a unique id for the script tag
:type elementid: str

:param modelid: The Bokeh model id for the object to render (if missing, render
    the whole doc; if present, only render the one model)
:type modelid: str

:param docid: The id of the embedded Bokeh document to render
:type docid: str

:param headers: A dictionary of HTTP request headers to include in the request
:type headers: dict

.. note::
    This script injects a ``<div>`` in place, so must be placed under ``<body>``.

#}
<script id="{{ elementid }}">
  (function() {
    const xhr = new XMLHttpRequest()
    xhr.responseType = 'blob';
    xhr.open('GET', "{{ src_path }}", true);
    {% for header, value in headers.items() %}
    xhr.setRequestHeader("{{ header }}", "{{ value }}")
    {% endfor %}
    xhr.withCredentials = {{ with_credentials | tojson }};
    xhr.onload = function (event) {
      const script = document.createElement('script');
      const src = URL.createObjectURL(event.target.response);
      script.src = src;
      document.body.appendChild(script);
    };
    xhr.send();
  })();
</script>
