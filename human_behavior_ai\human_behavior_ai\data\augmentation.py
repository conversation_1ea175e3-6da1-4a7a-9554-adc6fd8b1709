"""
Data augmentation for behavioral interaction data.

This module provides various augmentation techniques to increase
the diversity of training data and improve model robustness.
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Callable
import random
from dataclasses import dataclass

from .dataset import BehaviorSequence


@dataclass
class AugmentationConfig:
    """Configuration for data augmentation."""
    
    # Noise augmentation
    add_gaussian_noise: bool = True
    noise_std: float = 0.01
    noise_probability: float = 0.5
    
    # Temporal augmentation
    time_stretch: bool = True
    stretch_range: Tuple[float, float] = (0.8, 1.2)
    stretch_probability: float = 0.3
    
    # Spatial augmentation
    spatial_jitter: bool = True
    jitter_std: float = 0.005
    jitter_probability: float = 0.4
    
    # Sequence augmentation
    random_crop: bool = True
    crop_ratio_range: Tuple[float, float] = (0.7, 1.0)
    crop_probability: float = 0.3
    
    # Dropout augmentation
    feature_dropout: bool = True
    dropout_rate: float = 0.1
    dropout_probability: float = 0.2
    
    # Velocity augmentation
    velocity_scaling: bool = True
    velocity_scale_range: Tuple[float, float] = (0.9, 1.1)
    velocity_probability: float = 0.3


class DataAugmentation:
    """
    Data augmentation pipeline for behavioral sequences.
    
    Applies various augmentation techniques to increase data diversity
    while preserving the essential characteristics of human behavior.
    """
    
    def __init__(self, config: AugmentationConfig):
        self.config = config
        
        # Register augmentation functions
        self.augmentations = [
            (self._add_gaussian_noise, config.add_gaussian_noise, config.noise_probability),
            (self._time_stretch, config.time_stretch, config.stretch_probability),
            (self._spatial_jitter, config.spatial_jitter, config.jitter_probability),
            (self._random_crop, config.random_crop, config.crop_probability),
            (self._feature_dropout, config.feature_dropout, config.dropout_probability),
            (self._velocity_scaling, config.velocity_scaling, config.velocity_probability),
        ]
    
    def augment_sequence(self, sequence: BehaviorSequence) -> BehaviorSequence:
        """
        Apply augmentation to a single behavior sequence.
        
        Args:
            sequence: Input behavior sequence
            
        Returns:
            Augmented behavior sequence
        """
        augmented_features = sequence.features.clone()
        
        # Apply each augmentation with its probability
        for aug_func, enabled, probability in self.augmentations:
            if enabled and random.random() < probability:
                augmented_features = aug_func(augmented_features)
        
        # Create new sequence with augmented features
        augmented_sequence = BehaviorSequence(
            features=augmented_features,
            task_type=sequence.task_type,
            metadata=sequence.metadata.copy() if sequence.metadata else {}
        )
        
        # Mark as augmented
        if augmented_sequence.metadata:
            augmented_sequence.metadata['augmented'] = True
        
        return augmented_sequence
    
    def augment_batch(self, sequences: List[BehaviorSequence]) -> List[BehaviorSequence]:
        """
        Apply augmentation to a batch of sequences.
        
        Args:
            sequences: List of behavior sequences
            
        Returns:
            List of augmented behavior sequences
        """
        return [self.augment_sequence(seq) for seq in sequences]
    
    def _add_gaussian_noise(self, features: torch.Tensor) -> torch.Tensor:
        """Add Gaussian noise to features."""
        noise = torch.randn_like(features) * self.config.noise_std
        return features + noise
    
    def _time_stretch(self, features: torch.Tensor) -> torch.Tensor:
        """Apply temporal stretching/compression."""
        seq_len, feature_dim = features.shape
        
        # Random stretch factor
        stretch_factor = random.uniform(*self.config.stretch_range)
        new_len = int(seq_len * stretch_factor)
        
        if new_len < 10:  # Minimum sequence length
            return features
        
        # Interpolate to new length
        indices = torch.linspace(0, seq_len - 1, new_len)
        
        # Linear interpolation
        stretched_features = torch.zeros(new_len, feature_dim)
        for i, idx in enumerate(indices):
            idx_floor = int(torch.floor(idx))
            idx_ceil = min(int(torch.ceil(idx)), seq_len - 1)
            
            if idx_floor == idx_ceil:
                stretched_features[i] = features[idx_floor]
            else:
                alpha = idx - idx_floor
                stretched_features[i] = (1 - alpha) * features[idx_floor] + alpha * features[idx_ceil]
        
        return stretched_features
    
    def _spatial_jitter(self, features: torch.Tensor) -> torch.Tensor:
        """Add spatial jitter to coordinate features."""
        jittered_features = features.clone()
        
        # Assume first two features are x, y coordinates
        if features.size(1) >= 2:
            jitter = torch.randn(features.size(0), 2) * self.config.jitter_std
            jittered_features[:, :2] += jitter
        
        return jittered_features
    
    def _random_crop(self, features: torch.Tensor) -> torch.Tensor:
        """Randomly crop a portion of the sequence."""
        seq_len = features.size(0)
        
        # Random crop ratio
        crop_ratio = random.uniform(*self.config.crop_ratio_range)
        crop_len = int(seq_len * crop_ratio)
        
        if crop_len < 10:  # Minimum sequence length
            return features
        
        # Random start position
        max_start = seq_len - crop_len
        start_idx = random.randint(0, max_start) if max_start > 0 else 0
        end_idx = start_idx + crop_len
        
        return features[start_idx:end_idx]
    
    def _feature_dropout(self, features: torch.Tensor) -> torch.Tensor:
        """Apply feature dropout (randomly zero out some features)."""
        dropout_mask = torch.rand_like(features) > self.config.dropout_rate
        return features * dropout_mask
    
    def _velocity_scaling(self, features: torch.Tensor) -> torch.Tensor:
        """Scale velocity-related features."""
        scaled_features = features.clone()
        
        # Assume velocity features are at indices 3, 4, 5 (vx, vy, v_mag)
        if features.size(1) >= 6:
            scale_factor = random.uniform(*self.config.velocity_scale_range)
            scaled_features[:, 3:6] *= scale_factor
        
        return scaled_features


class AdvancedAugmentation:
    """
    Advanced augmentation techniques for behavioral data.
    
    Implements more sophisticated augmentation methods that preserve
    the statistical properties of human behavior patterns.
    """
    
    def __init__(self, config: AugmentationConfig):
        self.config = config
    
    def mixup_sequences(self, seq1: BehaviorSequence, seq2: BehaviorSequence, 
                       alpha: float = 0.2) -> BehaviorSequence:
        """
        Apply MixUp augmentation between two sequences.
        
        Args:
            seq1: First behavior sequence
            seq2: Second behavior sequence
            alpha: MixUp parameter
            
        Returns:
            Mixed behavior sequence
        """
        # Sample mixing coefficient
        lam = np.random.beta(alpha, alpha)
        
        # Ensure sequences have same length (pad if necessary)
        len1, len2 = seq1.features.size(0), seq2.features.size(0)
        max_len = max(len1, len2)
        
        # Pad sequences
        features1 = self._pad_sequence(seq1.features, max_len)
        features2 = self._pad_sequence(seq2.features, max_len)
        
        # Mix features
        mixed_features = lam * features1 + (1 - lam) * features2
        
        # Create mixed sequence
        mixed_sequence = BehaviorSequence(
            features=mixed_features,
            task_type=seq1.task_type,  # Use first sequence's task type
            metadata={
                'mixup': True,
                'lambda': lam,
                'seq1_task': seq1.task_type,
                'seq2_task': seq2.task_type
            }
        )
        
        return mixed_sequence
    
    def cutmix_sequences(self, seq1: BehaviorSequence, seq2: BehaviorSequence,
                        alpha: float = 1.0) -> BehaviorSequence:
        """
        Apply CutMix augmentation between two sequences.
        
        Args:
            seq1: First behavior sequence
            seq2: Second behavior sequence
            alpha: CutMix parameter
            
        Returns:
            Cut-mixed behavior sequence
        """
        # Sample mixing coefficient
        lam = np.random.beta(alpha, alpha)
        
        # Ensure sequences have same length
        len1, len2 = seq1.features.size(0), seq2.features.size(0)
        max_len = max(len1, len2)
        
        features1 = self._pad_sequence(seq1.features, max_len)
        features2 = self._pad_sequence(seq2.features, max_len)
        
        # Determine cut region
        cut_len = int(max_len * (1 - lam))
        cut_start = random.randint(0, max_len - cut_len) if cut_len < max_len else 0
        cut_end = cut_start + cut_len
        
        # Apply cut and mix
        mixed_features = features1.clone()
        mixed_features[cut_start:cut_end] = features2[cut_start:cut_end]
        
        # Create mixed sequence
        mixed_sequence = BehaviorSequence(
            features=mixed_features,
            task_type=seq1.task_type,
            metadata={
                'cutmix': True,
                'lambda': lam,
                'cut_start': cut_start,
                'cut_end': cut_end,
                'seq1_task': seq1.task_type,
                'seq2_task': seq2.task_type
            }
        )
        
        return mixed_sequence
    
    def _pad_sequence(self, features: torch.Tensor, target_length: int) -> torch.Tensor:
        """Pad sequence to target length."""
        current_length = features.size(0)
        
        if current_length >= target_length:
            return features[:target_length]
        
        # Pad with zeros
        padding = torch.zeros(target_length - current_length, features.size(1))
        return torch.cat([features, padding], dim=0)


def get_augmentation_pipeline(config: Optional[AugmentationConfig] = None) -> DataAugmentation:
    """
    Get a configured augmentation pipeline.
    
    Args:
        config: Augmentation configuration
        
    Returns:
        Configured DataAugmentation instance
    """
    if config is None:
        config = AugmentationConfig()
    
    return DataAugmentation(config)


def create_augmented_dataset(sequences: List[BehaviorSequence], 
                           augmentation_factor: int = 2,
                           config: Optional[AugmentationConfig] = None) -> List[BehaviorSequence]:
    """
    Create an augmented dataset by applying augmentations to existing sequences.
    
    Args:
        sequences: Original behavior sequences
        augmentation_factor: Number of augmented versions per original sequence
        config: Augmentation configuration
        
    Returns:
        List containing original and augmented sequences
    """
    augmenter = get_augmentation_pipeline(config)
    augmented_sequences = sequences.copy()  # Start with original sequences
    
    # Generate augmented versions
    for _ in range(augmentation_factor):
        for sequence in sequences:
            augmented_seq = augmenter.augment_sequence(sequence)
            augmented_sequences.append(augmented_seq)
    
    return augmented_sequences


class TaskSpecificAugmentation:
    """
    Task-specific augmentation strategies.
    
    Applies different augmentation techniques based on the task type
    to preserve task-relevant behavioral characteristics.
    """
    
    def __init__(self):
        self.task_augmenters = {
            'mouse_tracking': self._mouse_tracking_augmentation,
            'click_sequence': self._click_sequence_augmentation,
            'drag_drop': self._drag_drop_augmentation,
            'scroll_test': self._scroll_test_augmentation,
            'typing_test': self._typing_test_augmentation,
        }
    
    def augment_by_task(self, sequence: BehaviorSequence) -> BehaviorSequence:
        """Apply task-specific augmentation."""
        augmenter = self.task_augmenters.get(sequence.task_type, self._default_augmentation)
        return augmenter(sequence)
    
    def _mouse_tracking_augmentation(self, sequence: BehaviorSequence) -> BehaviorSequence:
        """Augmentation specific to mouse tracking tasks."""
        # Focus on spatial and velocity augmentations
        config = AugmentationConfig(
            spatial_jitter=True,
            jitter_std=0.003,  # Smaller jitter for precise tracking
            velocity_scaling=True,
            velocity_scale_range=(0.95, 1.05),  # Subtle velocity changes
            add_gaussian_noise=True,
            noise_std=0.005
        )
        augmenter = DataAugmentation(config)
        return augmenter.augment_sequence(sequence)
    
    def _click_sequence_augmentation(self, sequence: BehaviorSequence) -> BehaviorSequence:
        """Augmentation specific to click sequence tasks."""
        # Focus on timing and spatial variations
        config = AugmentationConfig(
            time_stretch=True,
            stretch_range=(0.9, 1.1),
            spatial_jitter=True,
            jitter_std=0.01,  # Larger jitter for click targets
            add_gaussian_noise=False  # Preserve click precision
        )
        augmenter = DataAugmentation(config)
        return augmenter.augment_sequence(sequence)
    
    def _drag_drop_augmentation(self, sequence: BehaviorSequence) -> BehaviorSequence:
        """Augmentation specific to drag and drop tasks."""
        config = AugmentationConfig(
            velocity_scaling=True,
            velocity_scale_range=(0.8, 1.2),  # More variation in drag speed
            spatial_jitter=True,
            jitter_std=0.008,
            time_stretch=True,
            stretch_range=(0.85, 1.15)
        )
        augmenter = DataAugmentation(config)
        return augmenter.augment_sequence(sequence)
    
    def _scroll_test_augmentation(self, sequence: BehaviorSequence) -> BehaviorSequence:
        """Augmentation specific to scroll test tasks."""
        config = AugmentationConfig(
            velocity_scaling=True,
            velocity_scale_range=(0.7, 1.3),  # High variation in scroll speed
            time_stretch=True,
            stretch_range=(0.8, 1.2),
            add_gaussian_noise=True,
            noise_std=0.01
        )
        augmenter = DataAugmentation(config)
        return augmenter.augment_sequence(sequence)
    
    def _typing_test_augmentation(self, sequence: BehaviorSequence) -> BehaviorSequence:
        """Augmentation specific to typing test tasks."""
        config = AugmentationConfig(
            time_stretch=True,
            stretch_range=(0.9, 1.1),  # Subtle timing variations
            feature_dropout=True,
            dropout_rate=0.05,  # Light dropout for typing patterns
            add_gaussian_noise=True,
            noise_std=0.003  # Small noise for typing precision
        )
        augmenter = DataAugmentation(config)
        return augmenter.augment_sequence(sequence)
    
    def _default_augmentation(self, sequence: BehaviorSequence) -> BehaviorSequence:
        """Default augmentation for unknown task types."""
        config = AugmentationConfig()  # Use default configuration
        augmenter = DataAugmentation(config)
        return augmenter.augment_sequence(sequence)
