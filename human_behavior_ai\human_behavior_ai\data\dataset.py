"""
Dataset classes for human interaction data.
"""

import torch
from torch.utils.data import Dataset
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
import logging
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class BehaviorSequence:
    """Container for a single behavior sequence."""
    
    mouse_data: np.ndarray  # [T, mouse_dim]
    keyboard_data: np.ndarray  # [T, keyboard_dim]
    scroll_data: np.ndarray  # [T, scroll_dim]
    context_data: np.ndarray  # [T, context_dim]
    timestamps: np.ndarray  # [T]
    metadata: Dict
    
    def __len__(self) -> int:
        return len(self.timestamps)
    
    def to_dict(self) -> Dict[str, np.ndarray]:
        """Convert to dictionary format."""
        return {
            'mouse': self.mouse_data,
            'keyboard': self.keyboard_data,
            'scroll': self.scroll_data,
            'context': self.context_data,
            'timestamps': self.timestamps,
        }
    
    def to_tensor_dict(self, device: Optional[torch.device] = None) -> Dict[str, torch.Tensor]:
        """Convert to tensor dictionary."""
        tensor_dict = {}
        for key, value in self.to_dict().items():
            tensor = torch.from_numpy(value).float()
            if device is not None:
                tensor = tensor.to(device)
            tensor_dict[key] = tensor
        return tensor_dict


class InteractionDataset(Dataset):
    """Dataset for human interaction sequences."""
    
    def __init__(self, 
                 data_path: Union[str, Path],
                 sequence_length: int = 1024,
                 overlap: float = 0.5,
                 normalize: bool = True,
                 augment: bool = False,
                 filter_min_length: int = 100,
                 sampling_rate: int = 144):
        """
        Initialize interaction dataset.
        
        Args:
            data_path: Path to data directory containing session files
            sequence_length: Length of sequences to extract
            overlap: Overlap between consecutive sequences (0.0 to 1.0)
            normalize: Whether to normalize the data
            augment: Whether to apply data augmentation
            filter_min_length: Minimum sequence length to include
            sampling_rate: Expected sampling rate (Hz)
        """
        self.data_path = Path(data_path)
        self.sequence_length = sequence_length
        self.overlap = overlap
        self.normalize = normalize
        self.augment = augment
        self.filter_min_length = filter_min_length
        self.sampling_rate = sampling_rate
        
        # Load and process data
        self.sequences = self._load_sequences()
        self.sequence_indices = self._create_sequence_indices()
        
        # Compute normalization statistics
        if self.normalize:
            self.normalization_stats = self._compute_normalization_stats()
        else:
            self.normalization_stats = None
        
        logger.info(f"Loaded {len(self.sequences)} sessions, {len(self.sequence_indices)} sequences")
    
    def _load_sequences(self) -> List[BehaviorSequence]:
        """Load all behavior sequences from data directory."""
        sequences = []
        
        # Find all session files
        session_files = list(self.data_path.glob("*.json"))
        
        for session_file in session_files:
            try:
                sequence = self._load_session_file(session_file)
                if len(sequence) >= self.filter_min_length:
                    sequences.append(sequence)
                else:
                    logger.debug(f"Filtered out short sequence: {len(sequence)} < {self.filter_min_length}")
            except Exception as e:
                logger.warning(f"Failed to load {session_file}: {e}")
        
        return sequences
    
    def _load_session_file(self, session_file: Path) -> BehaviorSequence:
        """Load a single session file."""
        with open(session_file, 'r') as f:
            session_data = json.load(f)
        
        # Extract interaction events
        events = session_data.get('events', [])
        if not events:
            raise ValueError(f"No events found in {session_file}")
        
        # Process events into structured arrays
        mouse_data, keyboard_data, scroll_data, context_data, timestamps = self._process_events(events)
        
        # Create behavior sequence
        sequence = BehaviorSequence(
            mouse_data=mouse_data,
            keyboard_data=keyboard_data,
            scroll_data=scroll_data,
            context_data=context_data,
            timestamps=timestamps,
            metadata=session_data.get('metadata', {})
        )
        
        return sequence
    
    def _process_events(self, events: List[Dict]) -> Tuple[np.ndarray, ...]:
        """Process raw events into structured arrays."""
        # Sort events by timestamp
        events = sorted(events, key=lambda x: x.get('timestamp', 0))
        
        # Initialize data lists
        mouse_positions = []
        mouse_velocities = []
        mouse_buttons = []
        keyboard_events = []
        scroll_events = []
        timestamps = []
        
        # Process each event
        for event in events:
            timestamp = event.get('timestamp', 0)
            event_type = event.get('type', '')
            data = event.get('data', {})
            
            timestamps.append(timestamp)
            
            if event_type == 'mouse_position':
                pos = data.get('position', {'x': 0, 'y': 0})
                vel = data.get('velocity', {'x': 0, 'y': 0, 'magnitude': 0})
                mouse_positions.append([pos['x'], pos['y']])
                mouse_velocities.append([vel['x'], vel['y'], vel['magnitude']])
                mouse_buttons.append([0, 0, 0])  # No button info in position events
                
                # Default values for other modalities
                keyboard_events.append([0] * 16)
                scroll_events.append([0] * 8)
                
            elif event_type == 'click':
                pos = data.get('position', {'x': 0, 'y': 0})
                button = data.get('button', 0)
                mouse_positions.append([pos['x'], pos['y']])
                mouse_velocities.append([0, 0, 0])
                
                # Encode button state
                button_state = [0, 0, 0]
                if button < 3:
                    button_state[button] = 1
                mouse_buttons.append(button_state)
                
                keyboard_events.append([0] * 16)
                scroll_events.append([0] * 8)
                
            elif event_type == 'wheel':
                delta = data.get('delta', {'x': 0, 'y': 0, 'z': 0})
                
                # Use last mouse position or default
                if mouse_positions:
                    mouse_positions.append(mouse_positions[-1])
                    mouse_velocities.append([0, 0, 0])
                    mouse_buttons.append([0, 0, 0])
                else:
                    mouse_positions.append([0, 0])
                    mouse_velocities.append([0, 0, 0])
                    mouse_buttons.append([0, 0, 0])
                
                keyboard_events.append([0] * 16)
                scroll_events.append([delta['x'], delta['y'], delta.get('z', 0)] + [0] * 5)
                
            elif event_type in ['keydown', 'keyup']:
                key_code = data.get('key', 0)
                is_down = 1 if event_type == 'keydown' else 0
                
                # Use last mouse state
                if mouse_positions:
                    mouse_positions.append(mouse_positions[-1])
                    mouse_velocities.append([0, 0, 0])
                    mouse_buttons.append(mouse_buttons[-1])
                else:
                    mouse_positions.append([0, 0])
                    mouse_velocities.append([0, 0, 0])
                    mouse_buttons.append([0, 0, 0])
                
                # Encode keyboard event
                keyboard_event = [key_code, is_down] + [0] * 14
                keyboard_events.append(keyboard_event)
                scroll_events.append([0] * 8)
            
            else:
                # Default values for unknown events
                if mouse_positions:
                    mouse_positions.append(mouse_positions[-1])
                    mouse_velocities.append([0, 0, 0])
                    mouse_buttons.append(mouse_buttons[-1])
                else:
                    mouse_positions.append([0, 0])
                    mouse_velocities.append([0, 0, 0])
                    mouse_buttons.append([0, 0, 0])
                
                keyboard_events.append([0] * 16)
                scroll_events.append([0] * 8)
        
        # Convert to numpy arrays
        timestamps = np.array(timestamps, dtype=np.float32)
        
        # Combine mouse data
        mouse_data = np.concatenate([
            np.array(mouse_positions, dtype=np.float32),
            np.array(mouse_velocities, dtype=np.float32),
            np.array(mouse_buttons, dtype=np.float32)
        ], axis=1)  # Shape: [T, 8] (x, y, vx, vy, vmag, btn1, btn2, btn3)
        
        # Pad to expected dimensions
        if mouse_data.shape[1] < 16:
            padding = np.zeros((mouse_data.shape[0], 16 - mouse_data.shape[1]), dtype=np.float32)
            mouse_data = np.concatenate([mouse_data, padding], axis=1)
        
        keyboard_data = np.array(keyboard_events, dtype=np.float32)
        scroll_data = np.array(scroll_events, dtype=np.float32)
        
        # Create context data (task info, viewport, etc.)
        context_data = np.zeros((len(timestamps), 24), dtype=np.float32)
        # TODO: Extract actual context information from metadata
        
        return mouse_data, keyboard_data, scroll_data, context_data, timestamps
    
    def _create_sequence_indices(self) -> List[Tuple[int, int, int]]:
        """Create indices for sequence extraction."""
        indices = []
        
        for seq_idx, sequence in enumerate(self.sequences):
            seq_len = len(sequence)
            
            if seq_len < self.sequence_length:
                # Pad short sequences
                indices.append((seq_idx, 0, seq_len))
            else:
                # Extract overlapping windows
                step_size = int(self.sequence_length * (1 - self.overlap))
                
                for start_idx in range(0, seq_len - self.sequence_length + 1, step_size):
                    end_idx = start_idx + self.sequence_length
                    indices.append((seq_idx, start_idx, end_idx))
        
        return indices
    
    def _compute_normalization_stats(self) -> Dict[str, Dict[str, np.ndarray]]:
        """Compute normalization statistics."""
        stats = {}
        
        # Collect all data
        all_mouse = []
        all_keyboard = []
        all_scroll = []
        all_context = []
        
        for sequence in self.sequences:
            all_mouse.append(sequence.mouse_data)
            all_keyboard.append(sequence.keyboard_data)
            all_scroll.append(sequence.scroll_data)
            all_context.append(sequence.context_data)
        
        # Compute statistics for each modality
        for name, data_list in [
            ('mouse', all_mouse),
            ('keyboard', all_keyboard),
            ('scroll', all_scroll),
            ('context', all_context)
        ]:
            if data_list:
                combined_data = np.concatenate(data_list, axis=0)
                stats[name] = {
                    'mean': np.mean(combined_data, axis=0),
                    'std': np.std(combined_data, axis=0) + 1e-8,  # Add small epsilon
                    'min': np.min(combined_data, axis=0),
                    'max': np.max(combined_data, axis=0),
                }
        
        return stats
    
    def _normalize_data(self, data: np.ndarray, modality: str) -> np.ndarray:
        """Normalize data using computed statistics."""
        if self.normalization_stats is None or modality not in self.normalization_stats:
            return data
        
        stats = self.normalization_stats[modality]
        return (data - stats['mean']) / stats['std']
    
    def _denormalize_data(self, data: np.ndarray, modality: str) -> np.ndarray:
        """Denormalize data using computed statistics."""
        if self.normalization_stats is None or modality not in self.normalization_stats:
            return data
        
        stats = self.normalization_stats[modality]
        return data * stats['std'] + stats['mean']
    
    def __len__(self) -> int:
        return len(self.sequence_indices)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a single sequence."""
        seq_idx, start_idx, end_idx = self.sequence_indices[idx]
        sequence = self.sequences[seq_idx]
        
        # Extract sequence data
        mouse_data = sequence.mouse_data[start_idx:end_idx]
        keyboard_data = sequence.keyboard_data[start_idx:end_idx]
        scroll_data = sequence.scroll_data[start_idx:end_idx]
        context_data = sequence.context_data[start_idx:end_idx]
        timestamps = sequence.timestamps[start_idx:end_idx]
        
        # Pad if necessary
        actual_length = len(mouse_data)
        if actual_length < self.sequence_length:
            pad_length = self.sequence_length - actual_length
            
            mouse_data = np.pad(mouse_data, ((0, pad_length), (0, 0)), mode='constant')
            keyboard_data = np.pad(keyboard_data, ((0, pad_length), (0, 0)), mode='constant')
            scroll_data = np.pad(scroll_data, ((0, pad_length), (0, 0)), mode='constant')
            context_data = np.pad(context_data, ((0, pad_length), (0, 0)), mode='constant')
            timestamps = np.pad(timestamps, (0, pad_length), mode='constant')
        
        # Normalize if requested
        if self.normalize:
            mouse_data = self._normalize_data(mouse_data, 'mouse')
            keyboard_data = self._normalize_data(keyboard_data, 'keyboard')
            scroll_data = self._normalize_data(scroll_data, 'scroll')
            context_data = self._normalize_data(context_data, 'context')
        
        # Convert to tensors
        return {
            'mouse': torch.from_numpy(mouse_data).float(),
            'keyboard': torch.from_numpy(keyboard_data).float(),
            'scroll': torch.from_numpy(scroll_data).float(),
            'context': torch.from_numpy(context_data).float(),
            'timestamps': torch.from_numpy(timestamps).float(),
            'sequence_length': torch.tensor(actual_length, dtype=torch.long),
            'metadata': sequence.metadata,
        }
    
    def get_normalization_stats(self) -> Optional[Dict]:
        """Get normalization statistics."""
        return self.normalization_stats
    
    def get_sequence_info(self) -> Dict:
        """Get information about the dataset."""
        total_length = sum(len(seq) for seq in self.sequences)
        avg_length = total_length / len(self.sequences) if self.sequences else 0
        
        return {
            'num_sessions': len(self.sequences),
            'num_sequences': len(self.sequence_indices),
            'total_length': total_length,
            'avg_session_length': avg_length,
            'sequence_length': self.sequence_length,
            'overlap': self.overlap,
            'sampling_rate': self.sampling_rate,
        }
