"""
Reinforcement Learning components for adaptive behavioral strategy learning.

This module implements RL architectures for learning optimal behavioral strategies
that can adapt to different contexts and evade detection systems.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict, Any
import numpy as np

from .base import BaseModel, ModelConfig


class PolicyNetwork(BaseModel):
    """
    Policy network for generating behavioral actions based on current state.
    
    Uses actor-critic architecture with continuous action spaces for
    mouse movements, typing patterns, and scrolling behaviors.
    """
    
    def __init__(
        self,
        config: ModelConfig,
        state_dim: int = 512,
        action_dim: int = 3,  # x, y, action_type
        hidden_dims: list = [256, 128, 64],
        activation: str = "relu"
    ):
        super().__init__(config)
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dims = hidden_dims
        
        # Build policy network layers
        layers = []
        prev_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                self._get_activation(activation),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.backbone = nn.Sequential(*layers)
        
        # Action mean and log std
        self.action_mean = nn.Linear(prev_dim, action_dim)
        self.action_log_std = nn.Parameter(torch.zeros(action_dim))
        
        # Value head for actor-critic
        self.value_head = nn.Linear(prev_dim, 1)
        
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function."""
        if activation == "relu":
            return nn.ReLU()
        elif activation == "gelu":
            return nn.GELU()
        elif activation == "tanh":
            return nn.Tanh()
        else:
            return nn.ReLU()
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass through policy network.
        
        Args:
            state: State tensor of shape (batch_size, state_dim)
            
        Returns:
            Tuple of (action_mean, action_log_std, value)
        """
        features = self.backbone(state)
        
        action_mean = self.action_mean(features)
        action_log_std = self.action_log_std.expand_as(action_mean)
        value = self.value_head(features)
        
        return action_mean, action_log_std, value
    
    def sample_action(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Sample action from policy distribution.
        
        Args:
            state: State tensor of shape (batch_size, state_dim)
            
        Returns:
            Tuple of (action, log_prob)
        """
        action_mean, action_log_std, _ = self.forward(state)
        action_std = torch.exp(action_log_std)
        
        # Sample from normal distribution
        normal = torch.distributions.Normal(action_mean, action_std)
        action = normal.sample()
        log_prob = normal.log_prob(action).sum(dim=-1)
        
        return action, log_prob
    
    def evaluate_action(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Evaluate action under current policy.
        
        Args:
            state: State tensor of shape (batch_size, state_dim)
            action: Action tensor of shape (batch_size, action_dim)
            
        Returns:
            Tuple of (log_prob, entropy, value)
        """
        action_mean, action_log_std, value = self.forward(state)
        action_std = torch.exp(action_log_std)
        
        normal = torch.distributions.Normal(action_mean, action_std)
        log_prob = normal.log_prob(action).sum(dim=-1)
        entropy = normal.entropy().sum(dim=-1)
        
        return log_prob, entropy, value


class ValueNetwork(BaseModel):
    """
    Value network for estimating state values in RL training.
    """
    
    def __init__(
        self,
        config: ModelConfig,
        state_dim: int = 512,
        hidden_dims: list = [256, 128, 64],
        activation: str = "relu"
    ):
        super().__init__(config)
        
        self.state_dim = state_dim
        self.hidden_dims = hidden_dims
        
        # Build value network
        layers = []
        prev_dim = state_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                self._get_activation(activation),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        layers.append(nn.Linear(prev_dim, 1))
        self.network = nn.Sequential(*layers)
        
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function."""
        if activation == "relu":
            return nn.ReLU()
        elif activation == "gelu":
            return nn.GELU()
        elif activation == "tanh":
            return nn.Tanh()
        else:
            return nn.ReLU()
    
    def forward(self, state: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through value network.
        
        Args:
            state: State tensor of shape (batch_size, state_dim)
            
        Returns:
            State values of shape (batch_size, 1)
        """
        return self.network(state)


class ActorCritic(BaseModel):
    """
    Combined Actor-Critic network for behavioral policy learning.
    
    Integrates policy and value networks for efficient RL training
    with behavioral pattern adaptation.
    """
    
    def __init__(
        self,
        config: ModelConfig,
        state_dim: int = 512,
        action_dim: int = 3,
        hidden_dims: list = [256, 128, 64],
        shared_layers: int = 2,
        activation: str = "relu"
    ):
        super().__init__(config)
        
        self.state_dim = state_dim
        self.action_dim = action_dim
        self.hidden_dims = hidden_dims
        self.shared_layers = shared_layers
        
        # Shared feature extractor
        shared_layers_list = []
        prev_dim = state_dim
        
        for i in range(shared_layers):
            hidden_dim = hidden_dims[i] if i < len(hidden_dims) else hidden_dims[-1]
            shared_layers_list.extend([
                nn.Linear(prev_dim, hidden_dim),
                self._get_activation(activation),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.shared_network = nn.Sequential(*shared_layers_list)
        
        # Actor head (policy)
        actor_layers = []
        for i in range(shared_layers, len(hidden_dims)):
            hidden_dim = hidden_dims[i]
            actor_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                self._get_activation(activation),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        self.actor_backbone = nn.Sequential(*actor_layers)
        self.action_mean = nn.Linear(prev_dim, action_dim)
        self.action_log_std = nn.Parameter(torch.zeros(action_dim))
        
        # Critic head (value)
        critic_layers = []
        prev_dim = hidden_dims[shared_layers - 1] if shared_layers > 0 else state_dim
        
        for i in range(shared_layers, len(hidden_dims)):
            hidden_dim = hidden_dims[i]
            critic_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                self._get_activation(activation),
                nn.Dropout(0.1)
            ])
            prev_dim = hidden_dim
        
        critic_layers.append(nn.Linear(prev_dim, 1))
        self.critic_backbone = nn.Sequential(*critic_layers)
        
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function."""
        if activation == "relu":
            return nn.ReLU()
        elif activation == "gelu":
            return nn.GELU()
        elif activation == "tanh":
            return nn.Tanh()
        else:
            return nn.ReLU()
    
    def forward(self, state: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Forward pass through actor-critic network.
        
        Args:
            state: State tensor of shape (batch_size, state_dim)
            
        Returns:
            Tuple of (action_mean, action_log_std, value)
        """
        shared_features = self.shared_network(state)
        
        # Actor forward
        actor_features = self.actor_backbone(shared_features)
        action_mean = self.action_mean(actor_features)
        action_log_std = self.action_log_std.expand_as(action_mean)
        
        # Critic forward
        value = self.critic_backbone(shared_features)
        
        return action_mean, action_log_std, value
    
    def act(self, state: torch.Tensor, deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Generate action from current policy.
        
        Args:
            state: State tensor of shape (batch_size, state_dim)
            deterministic: Whether to use deterministic policy
            
        Returns:
            Tuple of (action, log_prob, value)
        """
        action_mean, action_log_std, value = self.forward(state)
        
        if deterministic:
            action = action_mean
            log_prob = torch.zeros_like(action_mean[:, 0])
        else:
            action_std = torch.exp(action_log_std)
            normal = torch.distributions.Normal(action_mean, action_std)
            action = normal.sample()
            log_prob = normal.log_prob(action).sum(dim=-1)
        
        return action, log_prob, value
    
    def evaluate(self, state: torch.Tensor, action: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Evaluate state-action pairs.
        
        Args:
            state: State tensor of shape (batch_size, state_dim)
            action: Action tensor of shape (batch_size, action_dim)
            
        Returns:
            Tuple of (log_prob, entropy, value)
        """
        action_mean, action_log_std, value = self.forward(state)
        action_std = torch.exp(action_log_std)
        
        normal = torch.distributions.Normal(action_mean, action_std)
        log_prob = normal.log_prob(action).sum(dim=-1)
        entropy = normal.entropy().sum(dim=-1)
        
        return log_prob, entropy, value.squeeze(-1)


class BehavioralRewardModel(nn.Module):
    """
    Reward model for learning human-like behavioral patterns.
    
    Provides rewards based on how closely generated behaviors
    match human patterns and avoid detection.
    """
    
    def __init__(
        self,
        feature_dim: int = 512,
        hidden_dims: list = [256, 128, 64],
        num_reward_components: int = 4  # naturalness, diversity, stealth, task_completion
    ):
        super().__init__()
        
        self.feature_dim = feature_dim
        self.num_reward_components = num_reward_components
        
        # Reward component networks
        self.reward_networks = nn.ModuleList()
        for _ in range(num_reward_components):
            layers = []
            prev_dim = feature_dim
            
            for hidden_dim in hidden_dims:
                layers.extend([
                    nn.Linear(prev_dim, hidden_dim),
                    nn.ReLU(),
                    nn.Dropout(0.1)
                ])
                prev_dim = hidden_dim
            
            layers.append(nn.Linear(prev_dim, 1))
            self.reward_networks.append(nn.Sequential(*layers))
        
        # Reward component weights
        self.reward_weights = nn.Parameter(torch.ones(num_reward_components))
        
    def forward(self, behavioral_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute multi-component reward.
        
        Args:
            behavioral_features: Features extracted from behavioral sequence
            
        Returns:
            Dictionary containing individual and total rewards
        """
        component_rewards = []
        
        for network in self.reward_networks:
            reward = network(behavioral_features)
            component_rewards.append(reward)
        
        component_rewards = torch.stack(component_rewards, dim=-1)
        
        # Weighted combination
        weights = F.softmax(self.reward_weights, dim=0)
        total_reward = (component_rewards * weights).sum(dim=-1)
        
        return {
            'total_reward': total_reward,
            'component_rewards': component_rewards,
            'reward_weights': weights
        }
