<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interaction Recorder - AI Training Data Collection</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/themes.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-mouse-pointer"></i>
                    Interaction Recorder
                </h1>
                <div class="header-controls">
                    <button id="theme-toggle" class="theme-toggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </header>

        <div class="app-body">
            <!-- Sidebar Navigation -->
            <nav class="sidebar">
                <div class="sidebar-content">
                    <ul class="nav-menu">
                        <li class="nav-item active" data-section="recording">
                            <i class="fas fa-record-vinyl"></i>
                            <span>Recording</span>
                        </li>
                        <li class="nav-item" data-section="sessions">
                            <i class="fas fa-folder-open"></i>
                            <span>Saved Sessions</span>
                        </li>
                        <li class="nav-item" data-section="analysis">
                            <i class="fas fa-chart-line"></i>
                            <span>Analysis</span>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content Area -->
            <main class="main-content">
                <!-- Recording Section -->
                <section id="recording-section" class="content-section active">
                    <div class="section-header">
                        <h2>Interaction Recording</h2>
                        <p>Capture detailed user interaction data for AI training</p>
                    </div>

                    <div class="recording-container">
                        <!-- Initial Recording Setup -->
                        <div id="recording-setup" class="recording-setup">
                            <div class="setup-card">
                                <div class="setup-icon">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                                <h3>Start New Recording Session</h3>
                                <p>This session will capture high-frequency interaction data including:</p>
                                <ul class="feature-list">
                                    <li><i class="fas fa-mouse"></i> Mouse movements at 144Hz precision</li>
                                    <li><i class="fas fa-keyboard"></i> Typing patterns and rhythms</li>
                                    <li><i class="fas fa-scroll"></i> Scrolling behaviors and patterns</li>
                                    <li><i class="fas fa-clock"></i> Precise timing and velocity data</li>
                                </ul>
                                
                                <div class="session-info">
                                    <div class="info-item">
                                        <strong>Duration:</strong> ~10 minutes
                                    </div>
                                    <div class="info-item">
                                        <strong>Tasks:</strong> Guided interaction scenarios
                                    </div>
                                    <div class="info-item">
                                        <strong>Data Rate:</strong> 144Hz capture frequency
                                    </div>
                                </div>

                                <button id="start-recording" class="start-button">
                                    <i class="fas fa-record-vinyl"></i>
                                    Start Recording Session
                                </button>
                            </div>
                        </div>

                        <!-- Active Recording Interface -->
                        <div id="recording-active" class="recording-active hidden">
                            <div class="recording-header">
                                <div class="recording-status">
                                    <div class="status-indicator recording"></div>
                                    <span>Recording Active</span>
                                </div>
                                <div class="recording-timer">
                                    <i class="fas fa-clock"></i>
                                    <span id="session-timer">00:00</span>
                                </div>
                                <div class="data-counter">
                                    <i class="fas fa-database"></i>
                                    <span id="data-points">0</span> points
                                </div>
                            </div>

                            <div class="task-container">
                                <div class="task-progress">
                                    <div class="progress-bar">
                                        <div id="progress-fill" class="progress-fill"></div>
                                    </div>
                                    <span id="task-counter">Task 1 of 8</span>
                                </div>

                                <div id="current-task" class="current-task">
                                    <h3 id="task-title">Loading task...</h3>
                                    <p id="task-description">Please wait while we prepare your first task.</p>
                                    <div id="task-content" class="task-content">
                                        <!-- Dynamic task content will be inserted here -->
                                    </div>
                                </div>

                                <div class="task-controls">
                                    <button id="complete-task" class="task-button" disabled>
                                        Complete Task
                                    </button>
                                    <button id="skip-task" class="task-button secondary">
                                        Skip Task
                                    </button>
                                </div>
                            </div>

                            <div class="recording-controls">
                                <button id="pause-recording" class="control-button">
                                    <i class="fas fa-pause"></i>
                                    Pause
                                </button>
                                <button id="stop-recording" class="control-button danger">
                                    <i class="fas fa-stop"></i>
                                    Stop Recording
                                </button>
                            </div>
                        </div>

                        <!-- Recording Complete -->
                        <div id="recording-complete" class="recording-complete hidden">
                            <div class="complete-card">
                                <div class="complete-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <h3>Recording Complete!</h3>
                                <p>Your interaction data has been successfully captured and saved.</p>
                                
                                <div class="session-summary">
                                    <div class="summary-item">
                                        <strong>Duration:</strong> <span id="final-duration">--:--</span>
                                    </div>
                                    <div class="summary-item">
                                        <strong>Data Points:</strong> <span id="final-data-points">0</span>
                                    </div>
                                    <div class="summary-item">
                                        <strong>Tasks Completed:</strong> <span id="final-tasks">0/8</span>
                                    </div>
                                </div>

                                <div class="complete-actions">
                                    <button id="view-session" class="action-button">
                                        <i class="fas fa-eye"></i>
                                        View Session Data
                                    </button>
                                    <button id="new-session" class="action-button secondary">
                                        <i class="fas fa-plus"></i>
                                        Start New Session
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Sessions Section -->
                <section id="sessions-section" class="content-section">
                    <div class="section-header">
                        <h2>Saved Sessions</h2>
                        <p>View and manage recorded interaction sessions</p>
                    </div>

                    <div class="sessions-container">
                        <div class="sessions-controls">
                            <button id="refresh-sessions" class="control-button">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="session-search" placeholder="Search sessions...">
                            </div>
                        </div>

                        <div id="sessions-list" class="sessions-list">
                            <!-- Sessions will be loaded dynamically -->
                        </div>
                    </div>
                </section>

                <!-- Analysis Section -->
                <section id="analysis-section" class="content-section">
                    <div class="section-header">
                        <h2>Data Analysis</h2>
                        <p>Analyze captured interaction patterns and statistics</p>
                    </div>

                    <div class="analysis-container">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="total-sessions">0</h3>
                                    <p>Total Sessions</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="total-data-points">0</h3>
                                    <p>Data Points</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="avg-duration">0m</h3>
                                    <p>Avg Duration</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-content">
                                    <h3 id="completed-sessions">0</h3>
                                    <p>Completed</p>
                                </div>
                            </div>
                        </div>

                        <div class="analysis-charts">
                            <div class="chart-container">
                                <h3>Session Activity</h3>
                                <div id="activity-chart" class="chart-placeholder">
                                    <i class="fas fa-chart-bar"></i>
                                    <p>Chart visualization will be displayed here</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/app.js"></script>
    <script src="js/recorder.js"></script>
    <script src="js/sessions.js"></script>
    <script src="js/analysis.js"></script>
</body>
</html>
