// Analysis Module for Interaction Data
class AnalysisManager {
    constructor() {
        this.analysisData = null;
        this.charts = {};
        this.currentSession = null;
    }

    async loadAnalysisData() {
        try {
            const response = await fetch('/api/analysis/summary');
            this.analysisData = await response.json();
            this.renderAnalysisOverview();
            return this.analysisData;
        } catch (error) {
            console.error('Error loading analysis data:', error);
            this.showError('Failed to load analysis data');
            return null;
        }
    }

    renderAnalysisOverview() {
        if (!this.analysisData) return;

        const data = this.analysisData;
        
        // Update stat cards
        document.getElementById('total-sessions').textContent = data.totalSessions;
        document.getElementById('total-data-points').textContent = data.totalDataPoints.toLocaleString();
        document.getElementById('avg-duration').textContent = this.formatDuration(data.averageDuration);
        document.getElementById('completed-sessions').textContent = data.completedSessions;

        // Generate charts
        this.generateActivityChart();
    }

    generateActivityChart() {
        const chartContainer = document.getElementById('activity-chart');
        
        // Simple bar chart representation (in a real app, you'd use Chart.js or D3.js)
        chartContainer.innerHTML = `
            <div class="chart-bars">
                <div class="chart-bar" style="height: 80%;">
                    <div class="bar-label">Sessions</div>
                    <div class="bar-value">${this.analysisData.totalSessions}</div>
                </div>
                <div class="chart-bar" style="height: 60%;">
                    <div class="bar-label">Completed</div>
                    <div class="bar-value">${this.analysisData.completedSessions}</div>
                </div>
                <div class="chart-bar" style="height: 100%;">
                    <div class="bar-label">Data Points</div>
                    <div class="bar-value">${(this.analysisData.totalDataPoints / 1000).toFixed(1)}K</div>
                </div>
                <div class="chart-bar" style="height: 45%;">
                    <div class="bar-label">Avg Duration</div>
                    <div class="bar-value">${Math.floor(this.analysisData.averageDuration / 60000)}m</div>
                </div>
            </div>
        `;
    }

    async analyzeSession(sessionId) {
        try {
            const response = await fetch(`/api/sessions/${sessionId}`);
            const sessionData = await response.json();
            
            this.currentSession = sessionData;
            this.renderSessionAnalysis(sessionData);
        } catch (error) {
            console.error('Error analyzing session:', error);
            this.showError('Failed to analyze session');
        }
    }

    renderSessionAnalysis(sessionData) {
        const { session, data } = sessionData;
        
        // Create detailed analysis view
        const analysisContainer = document.querySelector('.analysis-container');
        
        // Add session-specific analysis section
        let sessionAnalysisSection = document.getElementById('session-analysis');
        if (!sessionAnalysisSection) {
            sessionAnalysisSection = document.createElement('div');
            sessionAnalysisSection.id = 'session-analysis';
            sessionAnalysisSection.className = 'session-analysis-section';
            analysisContainer.appendChild(sessionAnalysisSection);
        }

        const analysis = this.performDetailedAnalysis(data);
        
        sessionAnalysisSection.innerHTML = `
            <div class="analysis-header">
                <h3>Session Analysis: ${session.id.substring(0, 8)}...</h3>
                <button class="close-analysis" onclick="analysisManager.closeSessionAnalysis()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="analysis-content">
                <div class="analysis-metrics">
                    <div class="metric-card">
                        <h4>Mouse Behavior</h4>
                        <div class="metric-grid">
                            <div class="metric-item">
                                <strong>Total Distance:</strong> ${analysis.mouse.totalDistance.toFixed(0)} px
                            </div>
                            <div class="metric-item">
                                <strong>Average Speed:</strong> ${analysis.mouse.avgSpeed.toFixed(2)} px/ms
                            </div>
                            <div class="metric-item">
                                <strong>Max Speed:</strong> ${analysis.mouse.maxSpeed.toFixed(2)} px/ms
                            </div>
                            <div class="metric-item">
                                <strong>Click Count:</strong> ${analysis.mouse.clickCount}
                            </div>
                            <div class="metric-item">
                                <strong>Dwell Time:</strong> ${analysis.mouse.dwellTime.toFixed(0)} ms
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <h4>Keyboard Behavior</h4>
                        <div class="metric-grid">
                            <div class="metric-item">
                                <strong>Key Presses:</strong> ${analysis.keyboard.totalKeys}
                            </div>
                            <div class="metric-item">
                                <strong>Avg Typing Speed:</strong> ${analysis.keyboard.avgTypingSpeed.toFixed(1)} WPM
                            </div>
                            <div class="metric-item">
                                <strong>Pauses:</strong> ${analysis.keyboard.pauseCount}
                            </div>
                            <div class="metric-item">
                                <strong>Corrections:</strong> ${analysis.keyboard.corrections}
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <h4>Scrolling Behavior</h4>
                        <div class="metric-grid">
                            <div class="metric-item">
                                <strong>Scroll Events:</strong> ${analysis.scrolling.eventCount}
                            </div>
                            <div class="metric-item">
                                <strong>Total Scroll:</strong> ${analysis.scrolling.totalDistance.toFixed(0)} px
                            </div>
                            <div class="metric-item">
                                <strong>Avg Scroll Speed:</strong> ${analysis.scrolling.avgSpeed.toFixed(2)} px/event
                            </div>
                            <div class="metric-item">
                                <strong>Direction Changes:</strong> ${analysis.scrolling.directionChanges}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analysis-charts">
                    <div class="chart-row">
                        <div class="chart-container">
                            <h4>Mouse Movement Heatmap</h4>
                            <div id="mouse-heatmap" class="chart-placeholder">
                                ${this.generateMouseHeatmap(analysis.mouse.positions)}
                            </div>
                        </div>
                        
                        <div class="chart-container">
                            <h4>Speed Over Time</h4>
                            <div id="speed-chart" class="chart-placeholder">
                                ${this.generateSpeedChart(analysis.mouse.speedOverTime)}
                            </div>
                        </div>
                    </div>

                    <div class="chart-row">
                        <div class="chart-container">
                            <h4>Typing Rhythm</h4>
                            <div id="typing-rhythm" class="chart-placeholder">
                                ${this.generateTypingRhythm(analysis.keyboard.rhythm)}
                            </div>
                        </div>
                        
                        <div class="chart-container">
                            <h4>Event Timeline</h4>
                            <div id="event-timeline" class="chart-placeholder">
                                ${this.generateEventTimeline(data)}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analysis-export">
                    <button class="action-button" onclick="analysisManager.exportAnalysis()">
                        <i class="fas fa-download"></i> Export Analysis
                    </button>
                    <button class="action-button secondary" onclick="analysisManager.generateReport()">
                        <i class="fas fa-file-alt"></i> Generate Report
                    </button>
                </div>
            </div>
        `;
    }

    performDetailedAnalysis(data) {
        const analysis = {
            mouse: {
                totalDistance: 0,
                avgSpeed: 0,
                maxSpeed: 0,
                clickCount: 0,
                dwellTime: 0,
                positions: [],
                speedOverTime: []
            },
            keyboard: {
                totalKeys: 0,
                avgTypingSpeed: 0,
                pauseCount: 0,
                corrections: 0,
                rhythm: []
            },
            scrolling: {
                eventCount: 0,
                totalDistance: 0,
                avgSpeed: 0,
                directionChanges: 0
            }
        };

        let lastMousePos = null;
        let lastKeyTime = null;
        let keySequence = [];
        let lastScrollDirection = null;
        let speeds = [];

        data.forEach((point, index) => {
            switch (point.type) {
                case 'mouse_move':
                case 'mouse_position':
                    if (lastMousePos) {
                        const distance = Math.sqrt(
                            Math.pow(point.data.x - lastMousePos.x, 2) + 
                            Math.pow(point.data.y - lastMousePos.y, 2)
                        );
                        analysis.mouse.totalDistance += distance;
                        
                        if (point.data.velocity && point.data.velocity.magnitude) {
                            const speed = point.data.velocity.magnitude;
                            speeds.push(speed);
                            analysis.mouse.maxSpeed = Math.max(analysis.mouse.maxSpeed, speed);
                            analysis.mouse.speedOverTime.push({
                                time: point.timestamp,
                                speed: speed
                            });
                        }
                    }
                    
                    analysis.mouse.positions.push({
                        x: point.data.x,
                        y: point.data.y,
                        timestamp: point.timestamp
                    });
                    
                    lastMousePos = { x: point.data.x, y: point.data.y };
                    break;

                case 'click':
                    analysis.mouse.clickCount++;
                    break;

                case 'key_down':
                    analysis.keyboard.totalKeys++;
                    
                    if (lastKeyTime) {
                        const interval = point.timestamp - lastKeyTime;
                        keySequence.push(interval);
                        
                        // Detect pauses (>500ms between keys)
                        if (interval > 500) {
                            analysis.keyboard.pauseCount++;
                        }
                        
                        analysis.keyboard.rhythm.push({
                            time: point.timestamp,
                            interval: interval,
                            key: point.data.key
                        });
                    }
                    
                    // Detect corrections (backspace)
                    if (point.data.key === 'Backspace') {
                        analysis.keyboard.corrections++;
                    }
                    
                    lastKeyTime = point.timestamp;
                    break;

                case 'wheel':
                    analysis.scrolling.eventCount++;
                    const scrollDistance = Math.abs(point.data.deltaY);
                    analysis.scrolling.totalDistance += scrollDistance;
                    
                    const scrollDirection = point.data.deltaY > 0 ? 'down' : 'up';
                    if (lastScrollDirection && lastScrollDirection !== scrollDirection) {
                        analysis.scrolling.directionChanges++;
                    }
                    lastScrollDirection = scrollDirection;
                    break;
            }
        });

        // Calculate averages
        if (speeds.length > 0) {
            analysis.mouse.avgSpeed = speeds.reduce((a, b) => a + b, 0) / speeds.length;
        }

        if (keySequence.length > 0) {
            const avgInterval = keySequence.reduce((a, b) => a + b, 0) / keySequence.length;
            // Rough WPM calculation (assuming 5 chars per word)
            analysis.keyboard.avgTypingSpeed = (60000 / avgInterval) / 5;
        }

        if (analysis.scrolling.eventCount > 0) {
            analysis.scrolling.avgSpeed = analysis.scrolling.totalDistance / analysis.scrolling.eventCount;
        }

        return analysis;
    }

    generateMouseHeatmap(positions) {
        if (positions.length === 0) return '<p>No mouse position data</p>';

        // Simple heatmap visualization
        const canvas = document.createElement('canvas');
        canvas.width = 400;
        canvas.height = 300;
        canvas.style.border = '1px solid var(--border-color)';
        canvas.style.borderRadius = '4px';
        
        const ctx = canvas.getContext('2d');
        ctx.fillStyle = 'var(--bg-primary)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw heat points
        positions.forEach(pos => {
            const x = (pos.x / window.innerWidth) * canvas.width;
            const y = (pos.y / window.innerHeight) * canvas.height;
            
            ctx.beginPath();
            ctx.arc(x, y, 2, 0, 2 * Math.PI);
            ctx.fillStyle = 'rgba(59, 130, 246, 0.3)';
            ctx.fill();
        });

        return canvas.outerHTML;
    }

    generateSpeedChart(speedData) {
        if (speedData.length === 0) return '<p>No speed data available</p>';

        // Simple line chart representation
        const maxSpeed = Math.max(...speedData.map(d => d.speed));
        const points = speedData.map((d, i) => {
            const x = (i / speedData.length) * 100;
            const y = 100 - (d.speed / maxSpeed) * 80;
            return `${x},${y}`;
        }).join(' ');

        return `
            <svg width="100%" height="200" style="border: 1px solid var(--border-color); border-radius: 4px;">
                <polyline points="${points}" fill="none" stroke="var(--accent-primary)" stroke-width="2"/>
                <text x="10" y="20" fill="var(--text-secondary)" font-size="12">Max: ${maxSpeed.toFixed(2)} px/ms</text>
            </svg>
        `;
    }

    generateTypingRhythm(rhythmData) {
        if (rhythmData.length === 0) return '<p>No typing data available</p>';

        const maxInterval = Math.max(...rhythmData.map(d => d.interval));
        const bars = rhythmData.slice(0, 50).map((d, i) => {
            const height = (d.interval / maxInterval) * 80;
            const x = (i / 50) * 100;
            return `<rect x="${x}%" y="${100 - height}%" width="1.8%" height="${height}%" fill="var(--accent-primary)"/>`;
        }).join('');

        return `
            <svg width="100%" height="150" style="border: 1px solid var(--border-color); border-radius: 4px;">
                ${bars}
                <text x="10" y="20" fill="var(--text-secondary)" font-size="12">Keystroke Intervals</text>
            </svg>
        `;
    }

    generateEventTimeline(data) {
        const eventTypes = ['mouse_move', 'click', 'key_down', 'wheel'];
        const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'];
        
        const timeline = eventTypes.map((type, i) => {
            const events = data.filter(d => d.type === type);
            const count = events.length;
            const percentage = (count / data.length) * 100;
            
            return `
                <div class="timeline-bar" style="width: ${percentage}%; background: ${colors[i]};">
                    <span class="timeline-label">${type}: ${count}</span>
                </div>
            `;
        }).join('');

        return `
            <div class="timeline-container">
                ${timeline}
            </div>
        `;
    }

    closeSessionAnalysis() {
        const section = document.getElementById('session-analysis');
        if (section) {
            section.remove();
        }
        this.currentSession = null;
    }

    exportAnalysis() {
        if (!this.currentSession) return;

        const analysis = this.performDetailedAnalysis(this.currentSession.data);
        const exportData = {
            session: this.currentSession.session,
            analysis: analysis,
            exportTime: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `analysis_${this.currentSession.session.id}_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    generateReport() {
        if (!this.currentSession) return;

        const analysis = this.performDetailedAnalysis(this.currentSession.data);
        const report = this.createTextReport(analysis);

        const dataBlob = new Blob([report], { type: 'text/plain' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `report_${this.currentSession.session.id}_${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    createTextReport(analysis) {
        return `
INTERACTION ANALYSIS REPORT
Generated: ${new Date().toLocaleString()}
Session ID: ${this.currentSession.session.id}

MOUSE BEHAVIOR
- Total Distance Traveled: ${analysis.mouse.totalDistance.toFixed(0)} pixels
- Average Speed: ${analysis.mouse.avgSpeed.toFixed(2)} px/ms
- Maximum Speed: ${analysis.mouse.maxSpeed.toFixed(2)} px/ms
- Click Count: ${analysis.mouse.clickCount}
- Position Samples: ${analysis.mouse.positions.length}

KEYBOARD BEHAVIOR
- Total Key Presses: ${analysis.keyboard.totalKeys}
- Average Typing Speed: ${analysis.keyboard.avgTypingSpeed.toFixed(1)} WPM
- Typing Pauses: ${analysis.keyboard.pauseCount}
- Corrections Made: ${analysis.keyboard.corrections}

SCROLLING BEHAVIOR
- Scroll Events: ${analysis.scrolling.eventCount}
- Total Scroll Distance: ${analysis.scrolling.totalDistance.toFixed(0)} pixels
- Average Scroll Speed: ${analysis.scrolling.avgSpeed.toFixed(2)} px/event
- Direction Changes: ${analysis.scrolling.directionChanges}

This data can be used for training AI models to replicate human-like interaction patterns.
        `.trim();
    }

    formatDuration(ms) {
        if (!ms) return '0m 0s';
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}m ${seconds}s`;
    }

    showError(message) {
        console.error(message);
        alert(message);
    }
}

// Initialize analysis manager
window.analysisManager = new AnalysisManager();
