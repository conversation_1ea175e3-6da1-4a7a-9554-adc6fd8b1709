"""
Transformer-based models for human behavior learning.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Optional, Tuple
import math

from .base import BaseModel, ModelConfig, get_activation_fn


class MultiHeadAttention(nn.Module):
    """Multi-head attention with optional cross-modal capabilities."""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.d_k)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        batch_size, seq_len = query.shape[:2]
        
        # Linear projections
        Q = self.w_q(query).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        # Attention
        attention_output, attention_weights = self.scaled_dot_product_attention(Q, K, V, mask)
        
        # Concatenate heads
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        
        # Final linear projection
        output = self.w_o(attention_output)
        
        return output, attention_weights
    
    def scaled_dot_product_attention(self, Q: torch.Tensor, K: torch.Tensor, V: torch.Tensor,
                                   mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        # Compute attention scores
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        # Apply mask if provided
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # Softmax
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # Apply attention to values
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights


class TransformerBlock(nn.Module):
    """Transformer block with multi-head attention and feed-forward network."""
    
    def __init__(self, d_model: int, num_heads: int, d_ff: int, dropout: float = 0.1, activation: str = "gelu"):
        super().__init__()
        
        self.attention = MultiHeadAttention(d_model, num_heads, dropout)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            get_activation_fn(activation),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        # Self-attention with residual connection
        attn_output, _ = self.attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Feed-forward with residual connection
        ff_output = self.feed_forward(x)
        x = self.norm2(x + ff_output)
        
        return x


class TransformerEncoder(BaseModel):
    """Transformer encoder for human behavior sequence modeling."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        
        # Transformer layers
        self.layers = nn.ModuleList([
            TransformerBlock(
                d_model=config.hidden_dim,
                num_heads=config.num_heads,
                d_ff=config.hidden_dim * 4,
                dropout=config.dropout,
                activation=config.activation
            )
            for _ in range(config.num_layers)
        ])
        
        # Output projections
        self.output_norm = nn.LayerNorm(config.hidden_dim)
        self.output_projection = nn.Linear(config.hidden_dim, config.output_dim)
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def forward(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Forward pass through transformer encoder."""
        # Embed inputs
        x = self.embed_inputs(batch)  # [B, T*4, H]
        
        # Create attention mask if needed
        mask = batch.get('attention_mask', None)
        
        # Pass through transformer layers
        for layer in self.layers:
            x = layer(x, mask)
        
        # Output normalization and projection
        x = self.output_norm(x)
        output = self.output_projection(x)
        
        return {
            'hidden_states': x,
            'predictions': output,
            'attention_weights': None  # Could store attention weights if needed
        }
    
    def generate(self, context: Dict[str, torch.Tensor], length: int) -> Dict[str, torch.Tensor]:
        """Generate behavior sequences autoregressively."""
        self.eval()
        
        batch_size = context['mouse'].shape[0]
        device = context['mouse'].device
        
        # Initialize generation with context
        generated_mouse = [context['mouse']]
        generated_keyboard = [context['keyboard']]
        generated_scroll = [context['scroll']]
        generated_context = [context['context']]
        
        with torch.no_grad():
            for _ in range(length):
                # Prepare current sequence
                current_batch = {
                    'mouse': torch.cat(generated_mouse, dim=1),
                    'keyboard': torch.cat(generated_keyboard, dim=1),
                    'scroll': torch.cat(generated_scroll, dim=1),
                    'context': torch.cat(generated_context, dim=1),
                }
                
                # Forward pass
                outputs = self.forward(current_batch)
                predictions = outputs['predictions']
                
                # Get last prediction and reshape to modalities
                last_pred = predictions[:, -4:, :]  # Last 4 tokens (one per modality)
                
                # Split prediction into modalities
                mouse_pred = last_pred[:, 0:1, :self.config.mouse_dim]
                keyboard_pred = last_pred[:, 1:2, :self.config.keyboard_dim]
                scroll_pred = last_pred[:, 2:3, :self.config.scroll_dim]
                context_pred = last_pred[:, 3:4, :self.config.context_dim]
                
                # Add noise for diversity
                mouse_pred += torch.randn_like(mouse_pred) * 0.01
                keyboard_pred += torch.randn_like(keyboard_pred) * 0.01
                scroll_pred += torch.randn_like(scroll_pred) * 0.01
                
                # Append to generated sequences
                generated_mouse.append(mouse_pred)
                generated_keyboard.append(keyboard_pred)
                generated_scroll.append(scroll_pred)
                generated_context.append(context_pred)
        
        return {
            'mouse': torch.cat(generated_mouse[1:], dim=1),  # Exclude initial context
            'keyboard': torch.cat(generated_keyboard[1:], dim=1),
            'scroll': torch.cat(generated_scroll[1:], dim=1),
            'context': torch.cat(generated_context[1:], dim=1),
        }


class MultiModalTransformer(TransformerEncoder):
    """Enhanced transformer with cross-modal attention capabilities."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        
        # Cross-modal attention layers
        self.cross_modal_layers = nn.ModuleList([
            CrossModalAttentionBlock(
                d_model=config.hidden_dim,
                num_heads=config.num_heads,
                dropout=config.dropout
            )
            for _ in range(config.num_layers // 2)  # Fewer cross-modal layers
        ])
        
        # Modality-specific output heads
        self.mouse_head = nn.Linear(config.hidden_dim, config.mouse_dim)
        self.keyboard_head = nn.Linear(config.hidden_dim, config.keyboard_dim)
        self.scroll_head = nn.Linear(config.hidden_dim, config.scroll_dim)
        self.context_head = nn.Linear(config.hidden_dim, config.context_dim)
    
    def forward(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Forward pass with cross-modal attention."""
        # Standard transformer encoding
        outputs = super().forward(batch)
        hidden_states = outputs['hidden_states']  # [B, T*4, H]
        
        batch_size, seq_len_x4, hidden_dim = hidden_states.shape
        seq_len = seq_len_x4 // 4
        
        # Reshape to separate modalities
        hidden_states = hidden_states.view(batch_size, seq_len, 4, hidden_dim)
        
        # Apply cross-modal attention
        for cross_modal_layer in self.cross_modal_layers:
            hidden_states = cross_modal_layer(hidden_states)
        
        # Generate modality-specific outputs
        mouse_output = self.mouse_head(hidden_states[:, :, 0, :])  # [B, T, mouse_dim]
        keyboard_output = self.keyboard_head(hidden_states[:, :, 1, :])
        scroll_output = self.scroll_head(hidden_states[:, :, 2, :])
        context_output = self.context_head(hidden_states[:, :, 3, :])
        
        return {
            'hidden_states': hidden_states,
            'mouse_predictions': mouse_output,
            'keyboard_predictions': keyboard_output,
            'scroll_predictions': scroll_output,
            'context_predictions': context_output,
        }


class CrossModalAttentionBlock(nn.Module):
    """Cross-modal attention between different interaction modalities."""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        
        self.mouse_to_others = MultiHeadAttention(d_model, num_heads, dropout)
        self.keyboard_to_others = MultiHeadAttention(d_model, num_heads, dropout)
        self.scroll_to_others = MultiHeadAttention(d_model, num_heads, dropout)
        self.context_to_others = MultiHeadAttention(d_model, num_heads, dropout)
        
        self.norm_layers = nn.ModuleList([nn.LayerNorm(d_model) for _ in range(4)])
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Apply cross-modal attention."""
        # x shape: [B, T, 4, H] where 4 represents [mouse, keyboard, scroll, context]
        batch_size, seq_len, num_modalities, hidden_dim = x.shape
        
        # Extract modalities
        mouse, keyboard, scroll, context = x[:, :, 0], x[:, :, 1], x[:, :, 2], x[:, :, 3]
        
        # Create combined context for each modality (excluding self)
        keyboard_scroll_context = torch.cat([keyboard, scroll, context], dim=1)  # [B, 3T, H]
        mouse_scroll_context = torch.cat([mouse, scroll, context], dim=1)
        mouse_keyboard_context = torch.cat([mouse, keyboard, context], dim=1)
        mouse_keyboard_scroll = torch.cat([mouse, keyboard, scroll], dim=1)
        
        # Apply cross-modal attention
        mouse_attended, _ = self.mouse_to_others(mouse, keyboard_scroll_context, keyboard_scroll_context)
        keyboard_attended, _ = self.keyboard_to_others(keyboard, mouse_scroll_context, mouse_scroll_context)
        scroll_attended, _ = self.scroll_to_others(scroll, mouse_keyboard_context, mouse_keyboard_context)
        context_attended, _ = self.context_to_others(context, mouse_keyboard_scroll, mouse_keyboard_scroll)
        
        # Apply residual connections and layer norm
        mouse = self.norm_layers[0](mouse + self.dropout(mouse_attended))
        keyboard = self.norm_layers[1](keyboard + self.dropout(keyboard_attended))
        scroll = self.norm_layers[2](scroll + self.dropout(scroll_attended))
        context = self.norm_layers[3](context + self.dropout(context_attended))
        
        # Recombine modalities
        output = torch.stack([mouse, keyboard, scroll, context], dim=2)  # [B, T, 4, H]
        
        return output
