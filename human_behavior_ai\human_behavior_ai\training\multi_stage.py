"""
Multi-stage training pipeline for progressive model development.

This module implements curriculum learning and progressive training
strategies for building robust human behavior models.
"""

import torch
import torch.nn as nn
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
import numpy as np

from .base import BaseTrainer, TrainingConfig
from .hybrid_trainer import HybridBehaviorTrainer, HybridTrainingConfig


@dataclass
class StageConfig:
    """Configuration for a single training stage."""
    name: str
    duration_epochs: int
    learning_rate: float
    batch_size: int
    loss_weights: Dict[str, float]
    data_complexity: float = 1.0  # 0.0 to 1.0, controls data difficulty
    model_components: List[str] = None  # Which components to train
    
    def __post_init__(self):
        if self.model_components is None:
            self.model_components = ["all"]


class MultiStageTrainer:
    """
    Multi-stage trainer implementing curriculum learning and progressive training.
    
    Supports:
    - Curriculum learning with increasing data complexity
    - Progressive component activation
    - Adaptive learning rate scheduling
    - Stage-specific loss weighting
    """
    
    def __init__(
        self,
        base_trainer: BaseTrainer,
        stages: List[StageConfig],
        transition_criteria: Optional[Dict[str, Callable]] = None
    ):
        self.base_trainer = base_trainer
        self.stages = stages
        self.transition_criteria = transition_criteria or {}
        
        self.current_stage_idx = 0
        self.current_stage = self.stages[0]
        self.stage_epoch = 0
        
        # Stage history tracking
        self.stage_history = []
        self.stage_metrics = {}
        
    def _setup_stage(self, stage: StageConfig):
        """Setup trainer for current stage."""
        # Update learning rate
        for param_group in self.base_trainer.optimizer.param_groups:
            param_group['lr'] = stage.learning_rate
        
        # Update batch size (if supported by dataloader)
        if hasattr(self.base_trainer.train_dataloader, 'batch_size'):
            self.base_trainer.train_dataloader.batch_size = stage.batch_size
        
        # Update loss weights (if hybrid trainer)
        if isinstance(self.base_trainer, HybridBehaviorTrainer):
            self._update_hybrid_weights(stage)
        
        # Log stage transition
        self.base_trainer.logger.info(f"Starting stage: {stage.name}")
        self.base_trainer.logger.info(f"  Duration: {stage.duration_epochs} epochs")
        self.base_trainer.logger.info(f"  Learning rate: {stage.learning_rate}")
        self.base_trainer.logger.info(f"  Batch size: {stage.batch_size}")
        self.base_trainer.logger.info(f"  Loss weights: {stage.loss_weights}")
    
    def _update_hybrid_weights(self, stage: StageConfig):
        """Update loss weights for hybrid trainer."""
        # Update the hybrid config with stage-specific weights
        for key, weight in stage.loss_weights.items():
            if hasattr(self.base_trainer.hybrid_config, f"{key}_weight"):
                setattr(self.base_trainer.hybrid_config, f"{key}_weight", weight)
    
    def _should_transition_to_next_stage(self) -> bool:
        """Check if should transition to next stage."""
        # Check duration-based transition
        if self.stage_epoch >= self.current_stage.duration_epochs:
            return True
        
        # Check criteria-based transition
        stage_name = self.current_stage.name
        if stage_name in self.transition_criteria:
            criterion_func = self.transition_criteria[stage_name]
            if criterion_func(self.stage_metrics):
                return True
        
        return False
    
    def _transition_to_next_stage(self):
        """Transition to next training stage."""
        # Save current stage metrics
        self.stage_history.append({
            'stage': self.current_stage.name,
            'epochs': self.stage_epoch,
            'final_metrics': self.stage_metrics.copy()
        })
        
        # Move to next stage
        self.current_stage_idx += 1
        if self.current_stage_idx < len(self.stages):
            self.current_stage = self.stages[self.current_stage_idx]
            self.stage_epoch = 0
            self._setup_stage(self.current_stage)
            return True
        else:
            self.base_trainer.logger.info("All training stages completed!")
            return False
    
    def train(self):
        """Execute multi-stage training."""
        self.base_trainer.logger.info("Starting multi-stage training...")
        
        # Setup first stage
        self._setup_stage(self.current_stage)
        
        # Main training loop
        while self.current_stage_idx < len(self.stages):
            # Train for one epoch
            self._train_epoch()
            
            # Update stage metrics
            self._update_stage_metrics()
            
            # Check for stage transition
            if self._should_transition_to_next_stage():
                if not self._transition_to_next_stage():
                    break
            
            self.stage_epoch += 1
        
        self.base_trainer.logger.info("Multi-stage training completed!")
        self._log_training_summary()
    
    def _train_epoch(self):
        """Train for one epoch with current stage configuration."""
        self.base_trainer.model.train()
        epoch_metrics = {}
        
        for batch_idx, batch in enumerate(self.base_trainer.train_dataloader):
            # Apply data complexity filtering if needed
            if self.current_stage.data_complexity < 1.0:
                batch = self._filter_batch_complexity(batch)
            
            # Perform training step
            step_metrics = self.base_trainer.train_step(batch)
            
            # Accumulate metrics
            for key, value in step_metrics.items():
                if key not in epoch_metrics:
                    epoch_metrics[key] = []
                epoch_metrics[key].append(value.item() if isinstance(value, torch.Tensor) else value)
            
            self.base_trainer.current_step += 1
            
            # Logging
            if self.base_trainer.current_step % self.base_trainer.config.log_frequency == 0:
                self._log_stage_progress(step_metrics)
        
        # Average epoch metrics
        self.stage_metrics = {
            key: np.mean(values) for key, values in epoch_metrics.items()
        }
        
        # Validation
        if self.base_trainer.val_dataloader is not None:
            val_metrics = self.base_trainer.validate()
            self.stage_metrics.update(val_metrics)
    
    def _filter_batch_complexity(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Filter batch based on data complexity for curriculum learning."""
        # This is a simplified implementation
        # In practice, you would implement more sophisticated complexity measures
        
        complexity_threshold = self.current_stage.data_complexity
        
        if 'sequences' in batch:
            sequences = batch['sequences']
            batch_size, seq_len, _ = sequences.shape
            
            # Simple complexity measure: sequence variance
            sequence_complexity = torch.var(sequences, dim=(1, 2))
            complexity_percentile = torch.quantile(sequence_complexity, complexity_threshold)
            
            # Keep only sequences below complexity threshold
            mask = sequence_complexity <= complexity_percentile
            
            # Filter batch
            filtered_batch = {}
            for key, value in batch.items():
                if isinstance(value, torch.Tensor) and value.size(0) == batch_size:
                    filtered_batch[key] = value[mask]
                else:
                    filtered_batch[key] = value
            
            return filtered_batch
        
        return batch
    
    def _update_stage_metrics(self):
        """Update metrics for current stage."""
        # Add stage-specific information
        self.stage_metrics['stage'] = self.current_stage.name
        self.stage_metrics['stage_epoch'] = self.stage_epoch
        self.stage_metrics['stage_progress'] = self.stage_epoch / self.current_stage.duration_epochs
    
    def _log_stage_progress(self, step_metrics: Dict[str, Any]):
        """Log progress within current stage."""
        progress = self.stage_epoch / self.current_stage.duration_epochs
        log_str = f"Stage: {self.current_stage.name} ({progress:.1%}) | "
        log_str += f"Step {self.base_trainer.current_step} | "
        log_str += " | ".join([f"{k}: {v:.4f}" for k, v in step_metrics.items() 
                              if isinstance(v, (int, float))])
        
        self.base_trainer.logger.info(log_str)
    
    def _log_training_summary(self):
        """Log summary of multi-stage training."""
        self.base_trainer.logger.info("\n" + "="*50)
        self.base_trainer.logger.info("MULTI-STAGE TRAINING SUMMARY")
        self.base_trainer.logger.info("="*50)
        
        for i, stage_info in enumerate(self.stage_history):
            self.base_trainer.logger.info(f"Stage {i+1}: {stage_info['stage']}")
            self.base_trainer.logger.info(f"  Epochs: {stage_info['epochs']}")
            
            # Log key metrics
            final_metrics = stage_info['final_metrics']
            if 'total_loss' in final_metrics:
                self.base_trainer.logger.info(f"  Final Loss: {final_metrics['total_loss']:.4f}")
            if 'val_total_loss' in final_metrics:
                self.base_trainer.logger.info(f"  Final Val Loss: {final_metrics['val_total_loss']:.4f}")
        
        self.base_trainer.logger.info("="*50)


def create_default_curriculum() -> List[StageConfig]:
    """Create default curriculum learning stages."""
    stages = [
        StageConfig(
            name="foundation",
            duration_epochs=20,
            learning_rate=1e-3,
            batch_size=16,
            loss_weights={
                "reconstruction": 1.0,
                "kl_divergence": 0.1,
                "adversarial": 0.0,
                "rl_reward": 0.0,
                "consistency": 0.1
            },
            data_complexity=0.3,
            model_components=["vae"]
        ),
        StageConfig(
            name="generative",
            duration_epochs=30,
            learning_rate=5e-4,
            batch_size=24,
            loss_weights={
                "reconstruction": 0.8,
                "kl_divergence": 0.1,
                "adversarial": 0.5,
                "rl_reward": 0.0,
                "consistency": 0.2
            },
            data_complexity=0.6,
            model_components=["vae", "gan"]
        ),
        StageConfig(
            name="adaptive",
            duration_epochs=50,
            learning_rate=2e-4,
            batch_size=32,
            loss_weights={
                "reconstruction": 0.6,
                "kl_divergence": 0.1,
                "adversarial": 0.4,
                "rl_reward": 0.3,
                "consistency": 0.2
            },
            data_complexity=1.0,
            model_components=["all"]
        )
    ]
    
    return stages


def create_transition_criteria() -> Dict[str, Callable]:
    """Create default transition criteria for stages."""
    def foundation_criterion(metrics: Dict[str, float]) -> bool:
        """Transition from foundation stage when reconstruction loss is low."""
        return metrics.get('reconstruction_loss', float('inf')) < 0.1
    
    def generative_criterion(metrics: Dict[str, float]) -> bool:
        """Transition from generative stage when adversarial loss stabilizes."""
        return (metrics.get('adversarial_loss', float('inf')) < 0.5 and
                metrics.get('reconstruction_loss', float('inf')) < 0.05)
    
    return {
        'foundation': foundation_criterion,
        'generative': generative_criterion
    }
