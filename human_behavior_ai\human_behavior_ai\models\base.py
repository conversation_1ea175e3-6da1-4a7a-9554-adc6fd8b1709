"""
Base model classes and configurations for human behavior learning.
"""

import torch
import torch.nn as nn
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class ModelConfig:
    """Base configuration class for all models."""
    
    # Model architecture
    hidden_dim: int = 512
    num_layers: int = 12
    num_heads: int = 8
    dropout: float = 0.1
    activation: str = "gelu"
    
    # Input/Output dimensions
    input_dim: int = 64  # Combined feature dimension
    output_dim: int = 64
    sequence_length: int = 1024
    
    # Multi-modal settings
    mouse_dim: int = 16  # x, y, vx, vy, ax, ay, buttons, etc.
    keyboard_dim: int = 16  # key codes, timing, modifiers, etc.
    scroll_dim: int = 8   # deltaX, deltaY, deltaZ, etc.
    context_dim: int = 24  # task, website, viewport, etc.
    
    # Training settings
    learning_rate: float = 1e-4
    weight_decay: float = 1e-5
    gradient_clip: float = 1.0
    
    # Advanced features
    use_positional_encoding: bool = True
    use_layer_norm: bool = True
    use_residual_connections: bool = True
    use_attention_dropout: bool = True
    
    # Model-specific parameters (can be overridden by subclasses)
    model_specific: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            "hidden_dim": self.hidden_dim,
            "num_layers": self.num_layers,
            "num_heads": self.num_heads,
            "dropout": self.dropout,
            "activation": self.activation,
            "input_dim": self.input_dim,
            "output_dim": self.output_dim,
            "sequence_length": self.sequence_length,
            "mouse_dim": self.mouse_dim,
            "keyboard_dim": self.keyboard_dim,
            "scroll_dim": self.scroll_dim,
            "context_dim": self.context_dim,
            "learning_rate": self.learning_rate,
            "weight_decay": self.weight_decay,
            "gradient_clip": self.gradient_clip,
            "use_positional_encoding": self.use_positional_encoding,
            "use_layer_norm": self.use_layer_norm,
            "use_residual_connections": self.use_residual_connections,
            "use_attention_dropout": self.use_attention_dropout,
            "model_specific": self.model_specific,
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "ModelConfig":
        """Create config from dictionary."""
        return cls(**config_dict)
    
    def save(self, path: Path) -> None:
        """Save config to file."""
        with open(path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, path: Path) -> "ModelConfig":
        """Load config from file."""
        with open(path, 'r') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


class BaseModel(nn.Module, ABC):
    """Base class for all human behavior models."""
    
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config
        self.model_name = self.__class__.__name__
        
        # Initialize common components
        self._setup_embeddings()
        self._setup_positional_encoding()
        
    def _setup_embeddings(self):
        """Setup input embeddings for different modalities."""
        self.mouse_embedding = nn.Linear(self.config.mouse_dim, self.config.hidden_dim)
        self.keyboard_embedding = nn.Linear(self.config.keyboard_dim, self.config.hidden_dim)
        self.scroll_embedding = nn.Linear(self.config.scroll_dim, self.config.hidden_dim)
        self.context_embedding = nn.Linear(self.config.context_dim, self.config.hidden_dim)
        
        # Modality type embeddings
        self.modality_embeddings = nn.Embedding(4, self.config.hidden_dim)  # mouse, keyboard, scroll, context
        
    def _setup_positional_encoding(self):
        """Setup positional encoding for temporal sequences."""
        if self.config.use_positional_encoding:
            self.positional_encoding = PositionalEncoding(
                self.config.hidden_dim, 
                max_len=self.config.sequence_length
            )
        else:
            self.positional_encoding = None
    
    def embed_inputs(self, batch: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Embed multi-modal inputs into unified representation."""
        batch_size, seq_len = batch['mouse'].shape[:2]
        device = batch['mouse'].device
        
        # Embed each modality
        mouse_emb = self.mouse_embedding(batch['mouse'])  # [B, T, H]
        keyboard_emb = self.keyboard_embedding(batch['keyboard'])
        scroll_emb = self.scroll_embedding(batch['scroll'])
        context_emb = self.context_embedding(batch['context'])
        
        # Add modality type embeddings
        modality_ids = torch.arange(4, device=device).unsqueeze(0).unsqueeze(0)  # [1, 1, 4]
        modality_ids = modality_ids.expand(batch_size, seq_len, -1)  # [B, T, 4]
        
        modality_type_emb = self.modality_embeddings(modality_ids)  # [B, T, 4, H]
        
        # Combine embeddings
        embeddings = torch.stack([mouse_emb, keyboard_emb, scroll_emb, context_emb], dim=2)  # [B, T, 4, H]
        embeddings = embeddings + modality_type_emb
        
        # Flatten to sequence format
        embeddings = embeddings.view(batch_size, seq_len * 4, self.config.hidden_dim)  # [B, T*4, H]
        
        # Add positional encoding
        if self.positional_encoding is not None:
            embeddings = self.positional_encoding(embeddings)
        
        return embeddings
    
    @abstractmethod
    def forward(self, batch: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Forward pass of the model."""
        pass
    
    @abstractmethod
    def generate(self, context: Dict[str, torch.Tensor], length: int) -> Dict[str, torch.Tensor]:
        """Generate behavior sequences."""
        pass
    
    def get_num_parameters(self) -> int:
        """Get total number of trainable parameters."""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)
    
    def save_pretrained(self, path: Path) -> None:
        """Save model and configuration."""
        path.mkdir(parents=True, exist_ok=True)
        
        # Save model state
        torch.save(self.state_dict(), path / "model.pt")
        
        # Save configuration
        self.config.save(path / "config.json")
        
        # Save model info
        model_info = {
            "model_name": self.model_name,
            "num_parameters": self.get_num_parameters(),
            "config": self.config.to_dict(),
        }
        with open(path / "model_info.json", 'w') as f:
            json.dump(model_info, f, indent=2)
        
        logger.info(f"Model saved to {path}")
    
    @classmethod
    def load_pretrained(cls, path: Path) -> "BaseModel":
        """Load pretrained model."""
        # Load configuration
        config = ModelConfig.load(path / "config.json")
        
        # Create model instance
        model = cls(config)
        
        # Load model state
        state_dict = torch.load(path / "model.pt", map_location="cpu")
        model.load_state_dict(state_dict)
        
        logger.info(f"Model loaded from {path}")
        return model


class PositionalEncoding(nn.Module):
    """Positional encoding for transformer models."""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-torch.log(torch.tensor(10000.0)) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Add positional encoding to input."""
        return x + self.pe[:x.size(1), :].transpose(0, 1)


def get_activation_fn(activation: str) -> nn.Module:
    """Get activation function by name."""
    activations = {
        "relu": nn.ReLU(),
        "gelu": nn.GELU(),
        "swish": nn.SiLU(),
        "tanh": nn.Tanh(),
        "leaky_relu": nn.LeakyReLU(0.1),
    }
    
    if activation not in activations:
        raise ValueError(f"Unknown activation: {activation}")
    
    return activations[activation]
