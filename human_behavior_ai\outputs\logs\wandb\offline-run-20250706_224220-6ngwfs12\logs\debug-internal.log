{"time":"2025-07-06T22:42:21.4083958+05:30","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-06T22:42:23.9462246+05:30","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-07-06T22:42:23.9481465+05:30","level":"INFO","msg":"stream: created new stream","id":"6ngwfs12"}
{"time":"2025-07-06T22:42:23.9481465+05:30","level":"INFO","msg":"stream: started","id":"6ngwfs12"}
{"time":"2025-07-06T22:42:23.9481465+05:30","level":"INFO","msg":"handler: started","stream_id":"6ngwfs12"}
{"time":"2025-07-06T22:42:23.9481465+05:30","level":"INFO","msg":"sender: started","stream_id":"6ngwfs12"}
{"time":"2025-07-06T22:42:23.9481465+05:30","level":"INFO","msg":"writer: Do: started","stream_id":"6ngwfs12"}
{"time":"2025-07-06T22:42:23.9500599+05:30","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-07-06T22:42:29.0111856+05:30","level":"INFO","msg":"stream: closing","id":"6ngwfs12"}
{"time":"2025-07-06T22:42:29.0111856+05:30","level":"INFO","msg":"handler: closed","stream_id":"6ngwfs12"}
{"time":"2025-07-06T22:42:29.0117107+05:30","level":"INFO","msg":"writer: Close: closed","stream_id":"6ngwfs12"}
{"time":"2025-07-06T22:42:29.0117107+05:30","level":"INFO","msg":"sender: closed","stream_id":"6ngwfs12"}
{"time":"2025-07-06T22:42:29.0136025+05:30","level":"INFO","msg":"stream: closed","id":"6ngwfs12"}
