"""
Utility functions for data processing and manipulation.

This module provides various utility functions for working with
behavioral interaction data, including normalization, validation,
and statistical analysis.
"""

import torch
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path
import json
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats

from .dataset import BehaviorSequence


class DataUtils:
    """
    Utility class for data processing and analysis.
    
    Provides static methods for common data operations
    including validation, statistics, and visualization.
    """
    
    @staticmethod
    def validate_session_file(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Validate a session file and return validation results.
        
        Args:
            file_path: Path to the session JSON file
            
        Returns:
            Dictionary containing validation results
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            return {'valid': False, 'error': 'File does not exist'}
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            return {'valid': False, 'error': f'Invalid JSON: {e}'}
        
        # Check required fields
        required_fields = ['sessionId', 'startTime', 'endTime', 'tasks']
        missing_fields = [field for field in required_fields if field not in data]
        
        if missing_fields:
            return {'valid': False, 'error': f'Missing fields: {missing_fields}'}
        
        # Validate tasks
        tasks = data.get('tasks', [])
        task_validation = []
        
        for i, task in enumerate(tasks):
            task_result = DataUtils._validate_task(task, i)
            task_validation.append(task_result)
        
        # Summary statistics
        total_interactions = sum(len(task.get('interactions', [])) for task in tasks)
        valid_tasks = sum(1 for result in task_validation if result['valid'])
        
        return {
            'valid': all(result['valid'] for result in task_validation),
            'file_path': str(file_path),
            'num_tasks': len(tasks),
            'valid_tasks': valid_tasks,
            'total_interactions': total_interactions,
            'task_validation': task_validation,
            'session_duration': data.get('endTime', 0) - data.get('startTime', 0)
        }
    
    @staticmethod
    def _validate_task(task: Dict[str, Any], task_index: int) -> Dict[str, Any]:
        """Validate a single task."""
        required_fields = ['type', 'startTime', 'endTime', 'interactions']
        missing_fields = [field for field in required_fields if field not in task]
        
        if missing_fields:
            return {
                'valid': False,
                'task_index': task_index,
                'error': f'Missing fields: {missing_fields}'
            }
        
        # Validate interactions
        interactions = task.get('interactions', [])
        if not interactions:
            return {
                'valid': False,
                'task_index': task_index,
                'error': 'No interactions found'
            }
        
        # Check interaction format
        required_interaction_fields = ['timestamp', 'x', 'y', 'type']
        invalid_interactions = 0
        
        for interaction in interactions:
            missing_interaction_fields = [
                field for field in required_interaction_fields 
                if field not in interaction
            ]
            if missing_interaction_fields:
                invalid_interactions += 1
        
        return {
            'valid': invalid_interactions == 0,
            'task_index': task_index,
            'task_type': task.get('type', 'unknown'),
            'num_interactions': len(interactions),
            'invalid_interactions': invalid_interactions,
            'duration': task.get('endTime', 0) - task.get('startTime', 0)
        }
    
    @staticmethod
    def compute_sequence_statistics(sequences: List[BehaviorSequence]) -> Dict[str, Any]:
        """
        Compute comprehensive statistics for a list of behavior sequences.
        
        Args:
            sequences: List of behavior sequences
            
        Returns:
            Dictionary containing statistical information
        """
        if not sequences:
            return {'error': 'No sequences provided'}
        
        # Basic statistics
        sequence_lengths = [seq.features.size(0) for seq in sequences]
        feature_dims = [seq.features.size(1) for seq in sequences]
        task_types = [seq.task_type for seq in sequences]
        
        # Task type distribution
        task_type_counts = {}
        for task_type in task_types:
            task_type_counts[task_type] = task_type_counts.get(task_type, 0) + 1
        
        # Feature statistics
        all_features = torch.cat([seq.features for seq in sequences], dim=0)
        feature_stats = {
            'mean': all_features.mean(dim=0).tolist(),
            'std': all_features.std(dim=0).tolist(),
            'min': all_features.min(dim=0)[0].tolist(),
            'max': all_features.max(dim=0)[0].tolist(),
            'median': all_features.median(dim=0)[0].tolist()
        }
        
        return {
            'num_sequences': len(sequences),
            'sequence_lengths': {
                'min': min(sequence_lengths),
                'max': max(sequence_lengths),
                'mean': np.mean(sequence_lengths),
                'std': np.std(sequence_lengths),
                'median': np.median(sequence_lengths),
                'percentiles': {
                    '25': np.percentile(sequence_lengths, 25),
                    '75': np.percentile(sequence_lengths, 75),
                    '95': np.percentile(sequence_lengths, 95)
                }
            },
            'feature_dimensions': {
                'min': min(feature_dims),
                'max': max(feature_dims),
                'mode': max(set(feature_dims), key=feature_dims.count)
            },
            'task_type_distribution': task_type_counts,
            'feature_statistics': feature_stats
        }
    
    @staticmethod
    def detect_anomalies(sequences: List[BehaviorSequence], 
                        method: str = 'isolation_forest') -> List[Dict[str, Any]]:
        """
        Detect anomalous sequences using various methods.
        
        Args:
            sequences: List of behavior sequences
            method: Anomaly detection method ('isolation_forest', 'statistical')
            
        Returns:
            List of anomaly detection results
        """
        if method == 'statistical':
            return DataUtils._statistical_anomaly_detection(sequences)
        elif method == 'isolation_forest':
            return DataUtils._isolation_forest_anomaly_detection(sequences)
        else:
            raise ValueError(f"Unknown anomaly detection method: {method}")
    
    @staticmethod
    def _statistical_anomaly_detection(sequences: List[BehaviorSequence]) -> List[Dict[str, Any]]:
        """Statistical anomaly detection based on sequence characteristics."""
        anomalies = []
        
        # Compute sequence characteristics
        characteristics = []
        for i, seq in enumerate(sequences):
            features = seq.features
            char = {
                'index': i,
                'length': features.size(0),
                'mean_magnitude': features.norm(dim=1).mean().item(),
                'std_magnitude': features.norm(dim=1).std().item(),
                'feature_mean': features.mean().item(),
                'feature_std': features.std().item()
            }
            characteristics.append(char)
        
        # Convert to arrays for analysis
        lengths = np.array([c['length'] for c in characteristics])
        mean_mags = np.array([c['mean_magnitude'] for c in characteristics])
        std_mags = np.array([c['std_magnitude'] for c in characteristics])
        
        # Detect outliers using z-score
        z_threshold = 3.0
        
        for i, char in enumerate(characteristics):
            anomaly_scores = {}
            
            # Length anomaly
            length_z = abs(lengths[i] - lengths.mean()) / (lengths.std() + 1e-8)
            if length_z > z_threshold:
                anomaly_scores['length'] = length_z
            
            # Magnitude anomaly
            mag_z = abs(mean_mags[i] - mean_mags.mean()) / (mean_mags.std() + 1e-8)
            if mag_z > z_threshold:
                anomaly_scores['magnitude'] = mag_z
            
            # Variability anomaly
            std_z = abs(std_mags[i] - std_mags.mean()) / (std_mags.std() + 1e-8)
            if std_z > z_threshold:
                anomaly_scores['variability'] = std_z
            
            if anomaly_scores:
                anomalies.append({
                    'sequence_index': i,
                    'task_type': sequences[i].task_type,
                    'anomaly_type': 'statistical',
                    'scores': anomaly_scores,
                    'total_score': sum(anomaly_scores.values())
                })
        
        return anomalies
    
    @staticmethod
    def _isolation_forest_anomaly_detection(sequences: List[BehaviorSequence]) -> List[Dict[str, Any]]:
        """Isolation forest anomaly detection."""
        try:
            from sklearn.ensemble import IsolationForest
        except ImportError:
            return [{'error': 'sklearn not available for isolation forest'}]
        
        # Extract features for anomaly detection
        features_list = []
        for seq in sequences:
            # Compute summary statistics for each sequence
            features = seq.features
            summary = torch.cat([
                features.mean(dim=0),
                features.std(dim=0),
                features.min(dim=0)[0],
                features.max(dim=0)[0],
                torch.tensor([features.size(0)], dtype=torch.float32)  # sequence length
            ])
            features_list.append(summary.numpy())
        
        # Fit isolation forest
        X = np.array(features_list)
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        anomaly_labels = iso_forest.fit_predict(X)
        anomaly_scores = iso_forest.score_samples(X)
        
        # Collect anomalies
        anomalies = []
        for i, (label, score) in enumerate(zip(anomaly_labels, anomaly_scores)):
            if label == -1:  # Anomaly
                anomalies.append({
                    'sequence_index': i,
                    'task_type': sequences[i].task_type,
                    'anomaly_type': 'isolation_forest',
                    'score': float(score),
                    'severity': 'high' if score < -0.5 else 'medium'
                })
        
        return anomalies
    
    @staticmethod
    def visualize_sequence_statistics(sequences: List[BehaviorSequence], 
                                    save_path: Optional[str] = None):
        """
        Create visualizations of sequence statistics.
        
        Args:
            sequences: List of behavior sequences
            save_path: Optional path to save the plot
        """
        if not sequences:
            print("No sequences to visualize")
            return
        
        # Compute statistics
        stats = DataUtils.compute_sequence_statistics(sequences)
        
        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Behavior Sequence Statistics', fontsize=16)
        
        # Sequence length distribution
        lengths = [seq.features.size(0) for seq in sequences]
        axes[0, 0].hist(lengths, bins=30, alpha=0.7, edgecolor='black')
        axes[0, 0].set_title('Sequence Length Distribution')
        axes[0, 0].set_xlabel('Sequence Length')
        axes[0, 0].set_ylabel('Frequency')
        
        # Task type distribution
        task_types = [seq.task_type for seq in sequences]
        task_counts = stats['task_type_distribution']
        axes[0, 1].bar(task_counts.keys(), task_counts.values())
        axes[0, 1].set_title('Task Type Distribution')
        axes[0, 1].set_xlabel('Task Type')
        axes[0, 1].set_ylabel('Count')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # Feature magnitude distribution
        all_features = torch.cat([seq.features for seq in sequences], dim=0)
        feature_magnitudes = all_features.norm(dim=1).numpy()
        axes[1, 0].hist(feature_magnitudes, bins=50, alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('Feature Magnitude Distribution')
        axes[1, 0].set_xlabel('Feature Magnitude')
        axes[1, 0].set_ylabel('Frequency')
        
        # Feature correlation heatmap (first 10 features)
        if all_features.size(1) > 1:
            n_features = min(10, all_features.size(1))
            corr_matrix = np.corrcoef(all_features[:, :n_features].T)
            im = axes[1, 1].imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
            axes[1, 1].set_title(f'Feature Correlation (First {n_features} Features)')
            axes[1, 1].set_xlabel('Feature Index')
            axes[1, 1].set_ylabel('Feature Index')
            plt.colorbar(im, ax=axes[1, 1])
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {save_path}")
        else:
            plt.show()


def normalize_data(data: torch.Tensor, method: str = 'standard') -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
    """
    Normalize data using specified method.
    
    Args:
        data: Input data tensor
        method: Normalization method ('standard', 'minmax', 'robust')
        
    Returns:
        Tuple of (normalized_data, normalization_params)
    """
    if method == 'standard':
        mean = data.mean(dim=0, keepdim=True)
        std = data.std(dim=0, keepdim=True)
        normalized = (data - mean) / (std + 1e-8)
        params = {'mean': mean, 'std': std}
    
    elif method == 'minmax':
        min_val = data.min(dim=0, keepdim=True)[0]
        max_val = data.max(dim=0, keepdim=True)[0]
        normalized = (data - min_val) / (max_val - min_val + 1e-8)
        params = {'min': min_val, 'max': max_val}
    
    elif method == 'robust':
        median = data.median(dim=0, keepdim=True)[0]
        mad = torch.median(torch.abs(data - median), dim=0, keepdim=True)[0]
        normalized = (data - median) / (mad + 1e-8)
        params = {'median': median, 'mad': mad}
    
    else:
        raise ValueError(f"Unknown normalization method: {method}")
    
    return normalized, params


def denormalize_data(normalized_data: torch.Tensor, params: Dict[str, torch.Tensor], 
                    method: str = 'standard') -> torch.Tensor:
    """
    Denormalize data using stored parameters.
    
    Args:
        normalized_data: Normalized data tensor
        params: Normalization parameters
        method: Normalization method used
        
    Returns:
        Denormalized data tensor
    """
    if method == 'standard':
        return normalized_data * params['std'] + params['mean']
    
    elif method == 'minmax':
        return normalized_data * (params['max'] - params['min']) + params['min']
    
    elif method == 'robust':
        return normalized_data * params['mad'] + params['median']
    
    else:
        raise ValueError(f"Unknown normalization method: {method}")


def split_sequences_by_task(sequences: List[BehaviorSequence]) -> Dict[str, List[BehaviorSequence]]:
    """
    Split sequences by task type.
    
    Args:
        sequences: List of behavior sequences
        
    Returns:
        Dictionary mapping task types to sequences
    """
    task_sequences = {}
    
    for sequence in sequences:
        task_type = sequence.task_type
        if task_type not in task_sequences:
            task_sequences[task_type] = []
        task_sequences[task_type].append(sequence)
    
    return task_sequences


def filter_sequences_by_length(sequences: List[BehaviorSequence], 
                             min_length: int = 10, 
                             max_length: Optional[int] = None) -> List[BehaviorSequence]:
    """
    Filter sequences by length criteria.
    
    Args:
        sequences: List of behavior sequences
        min_length: Minimum sequence length
        max_length: Maximum sequence length (optional)
        
    Returns:
        Filtered list of sequences
    """
    filtered = []
    
    for sequence in sequences:
        seq_len = sequence.features.size(0)
        
        if seq_len < min_length:
            continue
        
        if max_length is not None and seq_len > max_length:
            continue
        
        filtered.append(sequence)
    
    return filtered
