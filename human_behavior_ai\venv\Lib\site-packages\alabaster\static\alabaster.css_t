{%- set theme_body_bg = theme_body_bg or theme_base_bg %}
{%- set theme_sidebar_header = theme_sidebar_header or theme_gray_1 %}
{%- set theme_sidebar_link = theme_sidebar_link or theme_gray_1 %}
{%- set theme_anchor_hover_fg = theme_anchor_hover_fg or theme_gray_1 %}

{%- set theme_footnote_border = theme_footnote_border or theme_gray_2 %}
{%- set theme_pre_bg = theme_pre_bg or 'unset' %}

{%- set theme_head_font_family = theme_head_font_family or theme_font_family %}

{#- set up admonition styling #}
{#- - basic level #}
{%- set theme_admonition_xref_bg = theme_admonition_xref_bg or theme_xref_bg %}
{%- set theme_admonition_bg = theme_admonition_bg or theme_gray_2 %}
{%- set theme_note_bg = theme_note_bg or theme_gray_2 %}
{%- set theme_seealso_bg = theme_seealso_bg or theme_gray_2 %}

{#- - critical level #}
{%- set theme_danger_bg = theme_danger_bg or theme_pink_1 %}
{%- set theme_danger_border = theme_danger_border or theme_pink_2 %}
{%- set theme_danger_shadow = theme_danger_shadow or theme_pink_3 %}

{%- set theme_error_bg = theme_error_bg or theme_pink_1 %}
{%- set theme_error_border = theme_error_border or theme_pink_2 %}
{%- set theme_error_shadow = theme_error_shadow or theme_pink_3 %}

{#- - warning level #}
{%- set theme_caution_bg = theme_caution_bg or theme_pink_1 %}
{%- set theme_caution_border = theme_caution_border or theme_pink_2 %}

{%- set theme_attention_bg = theme_attention_bg or theme_pink_1 %}
{%- set theme_attention_border = theme_attention_border or theme_pink_2 %}

{%- set theme_warn_bg = theme_warn_bg or theme_pink_1 %}
{%- set theme_warn_border = theme_warn_border or theme_pink_2 %}

{#- - normal level #}
{%- set theme_important_bg = theme_important_bg or theme_gray_2 %}
{%- set theme_tip_bg = theme_tip_bg or theme_gray_2 %}
{%- set theme_hint_bg = theme_hint_bg or theme_gray_2 %}

{#- /set up admonition styling #}

{%- set theme_shadow = theme_shadow or theme_gray_2 %}


{%- set theme_topic_bg = theme_topic_bg or theme_gray_2 %}

{%- set theme_narrow_sidebar_link = theme_narrow_sidebar_link or theme_gray_3 %}
{%- set theme_sidebar_hr = theme_sidebar_hr or theme_gray_3 %}

{%- set theme_relbar_border = theme_relbar_border or theme_gray_2 -%}

/* -- page layout ----------------------------------------------------------- */

body {
    font-family: {{ theme_font_family }};
    font-size: {{ theme_font_size }};
    background-color: {{ theme_base_bg }};
    color: {{ theme_base_text }};
    margin: 0;
    padding: 0;
}


div.document {
    width: {{ theme_page_width }};
    margin: 30px auto 0 auto;
}

div.documentwrapper {
    float: left;
    width: 100%;
}

div.bodywrapper {
    margin: 0 0 0 {{ theme_sidebar_width }};
}

div.sphinxsidebar {
    width: {{ theme_sidebar_width }};
    font-size: 14px;
    line-height: 1.5;
}

hr {
    border: 1px solid {{ theme_hr_border }};
}

div.body {
    background-color: {{ theme_body_bg  }};
    color: {{ theme_body_text }};
    padding: 0 30px 0 30px;
}

div.body > .section {
    text-align: {{ theme_body_text_align }};
}

div.footer {
    width: {{ theme_page_width }};
    margin: 20px auto 30px auto;
    font-size: 14px;
    color: {{ theme_footer_text }};
    text-align: right;
}

div.footer a {
    color: {{ theme_footer_text }};
}

p.caption {
    font-family: {{ theme_caption_font_family }};
    font-size: {{ theme_caption_font_size }};
}

{% if theme_show_related|lower == 'false' %}
div.relations {
    display: none;
}
{% endif %}

div.sphinxsidebar {
    max-height: 100%;
    overflow-y: auto;
}

div.sphinxsidebar a {
    color: {{ theme_sidebar_link }};
    text-decoration: none;
    border-bottom: 1px dotted {{ theme_sidebar_link_underscore }};
}

div.sphinxsidebar a:hover {
    border-bottom: 1px solid {{ theme_sidebar_link_underscore }};
}

div.sphinxsidebarwrapper {
    padding: 18px 10px;
}

div.sphinxsidebarwrapper p.logo {
    padding: 0;
    margin: -10px 0 0 0px;
    text-align: center;
}

div.sphinxsidebarwrapper h1.logo {
    margin-top: -10px;
    text-align: center;
    margin-bottom: 5px;
    text-align: {{ theme_logo_text_align }};
}

div.sphinxsidebarwrapper h1.logo-name {
    margin-top: 0px;
}

div.sphinxsidebarwrapper p.blurb {
    margin-top: 0;
    font-style: {{ theme_description_font_style }};
}

div.sphinxsidebar h3,
div.sphinxsidebar h4 {
    font-family: {{ theme_head_font_family }};
    color: {{ theme_sidebar_header }};
    font-size: 24px;
    font-weight: normal;
    margin: 0 0 5px 0;
    padding: 0;
}

div.sphinxsidebar h4 {
    font-size: 20px;
}

div.sphinxsidebar h3 a {
    color: {{ theme_sidebar_link }};
}

div.sphinxsidebar p.logo a,
div.sphinxsidebar h3 a,
div.sphinxsidebar p.logo a:hover,
div.sphinxsidebar h3 a:hover {
    border: none;
}

div.sphinxsidebar p {
    color: {{ theme_sidebar_text }};
    margin: 10px 0;
}

div.sphinxsidebar ul {
    margin: 10px 0;
    padding: 0;
    color: {{ theme_sidebar_list }};
}

div.sphinxsidebar ul li.toctree-l1 > a {
    font-size: 120%;
}

div.sphinxsidebar ul li.toctree-l2 > a {
    font-size: 110%;
}

div.sphinxsidebar input {
    border: 1px solid {{ theme_sidebar_search_button }};
    font-family: {{ theme_font_family }};
    font-size: 1em;
}

div.sphinxsidebar #searchbox {
    margin: 1em 0;
}

div.sphinxsidebar .search > div {
    display: table-cell;
}

div.sphinxsidebar hr {
    border: none;
    height: 1px;
    color: {{ theme_sidebar_hr }};
    background: {{ theme_sidebar_hr }};

    text-align: left;
    margin-left: 0;
    width: 50%;
}

div.sphinxsidebar .badge {
    border-bottom: none;
}

div.sphinxsidebar .badge:hover {
    border-bottom: none;
}

/* To address an issue with donation coming after search */
div.sphinxsidebar h3.donation {
    margin-top: 10px;
}

/* -- body styles ----------------------------------------------------------- */

a {
    color: {{ theme_link }};
    text-decoration: underline;
}

a:hover {
    color: {{ theme_link_hover }};
    text-decoration: underline;
}

div.body h1,
div.body h2,
div.body h3,
div.body h4,
div.body h5,
div.body h6 {
    font-family: {{ theme_head_font_family }};
    font-weight: normal;
    margin: 30px 0px 10px 0px;
    padding: 0;
}

div.body h1 { margin-top: 0; padding-top: 0; font-size: 240%; }
div.body h2 { font-size: 180%; }
div.body h3 { font-size: 150%; }
div.body h4 { font-size: 130%; }
div.body h5 { font-size: 100%; }
div.body h6 { font-size: 100%; }

a.headerlink {
    color: {{ theme_anchor }};
    padding: 0 4px;
    text-decoration: none;
}

a.headerlink:hover {
    color: {{ theme_anchor_hover_fg }};
    background: {{ theme_anchor_hover_bg }};
}

div.body p, div.body dd, div.body li {
    line-height: 1.4em;
}

div.admonition {
    margin: 20px 0px;
    padding: 10px 30px;
    background-color: {{ theme_admonition_bg }};
    border: 1px solid {{ theme_admonition_border }};
}

div.admonition tt.xref, div.admonition code.xref, div.admonition a tt {
    background-color: {{ theme_admonition_xref_bg }};
    border-bottom: 1px solid {{ theme_admonition_xref_border }};
}

div.admonition p.admonition-title {
    font-family: {{ theme_head_font_family }};
    font-weight: normal;
    font-size: 24px;
    margin: 0 0 10px 0;
    padding: 0;
    line-height: 1;
}

div.admonition p.last {
    margin-bottom: 0;
}

{%- if theme_code_highlight_bg  %}
div.highlight {
    background-color: {{ theme_code_highlight_bg }};
}
{%- endif %}

dt:target, .highlight {
    background: {{ theme_highlight_bg }};
}

div.warning {
    background-color: {{ theme_warn_bg }};
    border: 1px solid {{ theme_warn_border }};
}

div.danger {
    background-color: {{ theme_danger_bg }};
    border: 1px solid {{ theme_danger_border }};
    -moz-box-shadow: 2px 2px 4px {{ theme_danger_shadow }};
    -webkit-box-shadow: 2px 2px 4px {{ theme_danger_shadow }};
    box-shadow: 2px 2px 4px {{ theme_danger_shadow }};
}

div.error {
    background-color: {{ theme_error_bg }};
    border: 1px solid {{ theme_error_border }};
    -moz-box-shadow: 2px 2px 4px {{ theme_error_shadow }};
    -webkit-box-shadow: 2px 2px 4px {{ theme_error_shadow }};
    box-shadow: 2px 2px 4px {{ theme_error_shadow }};
}

div.caution {
    background-color: {{ theme_caution_bg }};
    border: 1px solid {{ theme_caution_border }};
}

div.attention {
    background-color: {{ theme_attention_bg }};
    border: 1px solid {{ theme_attention_border }};
}

div.important {
    background-color: {{ theme_important_bg }};
    border: 1px solid {{ theme_important_border }};
}

div.note {
    background-color: {{ theme_note_bg }};
    border: 1px solid {{ theme_note_border }};
}

div.tip {
    background-color: {{ theme_tip_bg }};
    border: 1px solid {{ theme_tip_border }};
}

div.hint {
    background-color: {{ theme_hint_bg }};
    border: 1px solid {{ theme_hint_border }};
}

div.seealso {
    background-color: {{ theme_seealso_bg }};
    border: 1px solid {{ theme_seealso_border }};
}

div.topic {
    background-color: {{ theme_topic_bg }};
}

p.admonition-title {
    display: inline;
}

p.admonition-title:after {
    content: ":";
}

pre, tt, code {
    font-family: {{theme_code_font_family}};
    font-size: {{ theme_code_font_size }};
}

.hll {
    background-color: {{theme_code_highlight}};
    margin: 0 -12px;
    padding: 0 12px;
    display: block;
}

img.screenshot {
}

tt.descname, tt.descclassname, code.descname, code.descclassname {
    font-size: 0.95em;
}

tt.descname, code.descname {
    padding-right: 0.08em;
}

img.screenshot {
    -moz-box-shadow: 2px 2px 4px {{ theme_shadow }};
    -webkit-box-shadow: 2px 2px 4px {{ theme_shadow }};
    box-shadow: 2px 2px 4px {{ theme_shadow }};
}

table.docutils {
    border: 1px solid {{ theme_table_border }};
    -moz-box-shadow: 2px 2px 4px {{ theme_shadow }};
    -webkit-box-shadow: 2px 2px 4px {{ theme_shadow }};
    box-shadow: 2px 2px 4px {{ theme_shadow }};
}

table.docutils td, table.docutils th {
    border: 1px solid {{ theme_table_border }};
    padding: 0.25em 0.7em;
}

table.field-list, table.footnote {
    border: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

table.footnote {
    margin: 15px 0;
    width: 100%;
    border: 1px solid {{ theme_footnote_border }};
    background: {{ theme_footnote_bg }};
    font-size: 0.9em;
}

table.footnote + table.footnote {
    margin-top: -15px;
    border-top: none;
}

table.field-list th {
    padding: 0 0.8em 0 0;
}

table.field-list td {
    padding: 0;
}

table.field-list p {
    margin-bottom: 0.8em;
}

/* Cloned from
 * https://github.com/sphinx-doc/sphinx/commit/ef60dbfce09286b20b7385333d63a60321784e68
 */
.field-name {
    -moz-hyphens: manual;
    -ms-hyphens: manual;
    -webkit-hyphens: manual;
    hyphens: manual;
}

table.footnote td.label {
    width: .1px;
    padding: 0.3em 0 0.3em 0.5em;
}

table.footnote td {
    padding: 0.3em 0.5em;
}

dl {
    margin-left: 0;
    margin-right: 0;
    margin-top: 0;
    padding: 0;
}

dl dd {
    margin-left: 30px;
}

blockquote {
    margin: 0 0 0 30px;
    padding: 0;
}

ul, ol {
    /* Matches the 30px from the narrow-screen "li > ul" selector below */
    margin: 10px 0 10px 30px;
    padding: 0;
}

pre {
    background: {{ theme_pre_bg }};
    padding: 7px 30px;
    margin: 15px 0px;
    line-height: 1.3em;
}

div.viewcode-block:target {
    background: {{ theme_viewcode_target_bg }};
}

dl pre, blockquote pre, li pre {
    margin-left: 0;
    padding-left: 30px;
}

tt, code {
    background-color: {{ theme_code_bg }};
    color: {{ theme_code_text }};
    /* padding: 1px 2px; */
}

tt.xref, code.xref, a tt {
    background-color: {{ theme_xref_bg }};
    border-bottom: 1px solid {{ theme_xref_border }};
}

a.reference {
    text-decoration: none;
    border-bottom: 1px dotted {{ theme_link }};
}

a.reference:hover {
    border-bottom: 1px solid {{ theme_link_hover }};
}

/* Don't put an underline on images */
a.image-reference, a.image-reference:hover {
    border-bottom: none;
}

a.footnote-reference {
    text-decoration: none;
    font-size: 0.7em;
    vertical-align: top;
    border-bottom: 1px dotted {{ theme_link }};
}

a.footnote-reference:hover {
    border-bottom: 1px solid {{ theme_link_hover }};
}

a:hover tt, a:hover code {
    background: {{ theme_code_hover }};
}


{%- if theme_fixed_sidebar|lower == 'true' %}
div.sphinxsidebar {
    position: fixed;
    margin-left: 0;
}
{%- endif %}

@media screen and (max-width: {{ theme_page_width }}) {

    body {
        margin: 0;
        padding: 20px 30px;
    }

    div.documentwrapper {
        float: none;
        background: {{ theme_base_bg }};
        margin-left: 0;
        margin-top: 0;
        margin-right: 0;
        margin-bottom: 0;
    }

    div.sphinxsidebar {
        display: block;
        float: none;
        width: unset;
        {%- if theme_fixed_sidebar|lower == 'true' %}
        margin: -20px -30px 20px -30px;
        position: static;
        {%- else %}
        margin: 50px -30px -20px -30px;
        {%- endif %}
        padding: 10px 20px;
        background: {{ theme_narrow_sidebar_bg }};
        color: {{ theme_narrow_sidebar_fg }};
    }

    div.sphinxsidebar h3, div.sphinxsidebar h4, div.sphinxsidebar p,
    div.sphinxsidebar h3 a {
        color: {{ theme_base_bg }};
    }

    div.sphinxsidebar a {
        color: {{ theme_narrow_sidebar_link }};
    }

    div.sphinxsidebar p.logo {
        display: none;
    }

    div.document {
        width: 100%;
        margin: 0;
    }

    div.footer {
        display: none;
    }

    div.bodywrapper {
        margin: 0;
    }

    div.body {
        min-height: 0;
        min-width: auto; /* fixes width on small screens, breaks .hll */
        padding: 0;
    }
    
    .hll {
        /* "fixes" the breakage */
        width: max-content;
    }

    .rtd_doc_footer {
        display: none;
    }

    .document {
        width: auto;
    }

    .footer {
        width: auto;
    }

    .github {
        display: none;
    }

    ul {
        margin-left: 0;
    }

    li > ul {
       /* Matches the 30px from the "ul, ol" selector above */
        margin-left: 30px;
    }
}


/* misc. */

.revsys-inline {
    display: none!important;
}

/* Hide ugly table cell borders in ..bibliography:: directive output */
table.docutils.citation, table.docutils.citation td, table.docutils.citation th {
  border: none;
  /* Below needed in some edge cases; if not applied, bottom shadows appear */
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}


/* relbar */

.related {
    line-height: 30px;
    width: 100%;
    font-size: 0.9rem;
}

.related.top {
    border-bottom: 1px solid {{ theme_relbar_border }};
    margin-bottom: 20px;
}

.related.bottom {
    border-top: 1px solid {{ theme_relbar_border }};
}

.related ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.related li {
    display: inline;
}

nav#rellinks {
    float: right;
}

nav#rellinks li+li:before {
    content: "|";
}

nav#breadcrumbs li+li:before {
    content: "\00BB";
}

/* Hide certain items when printing */
@media print {
    div.related {
        display: none;
    }
}

img.github  {
    position: absolute;
    top: 0;
    border: 0;
    right: 0;
}
