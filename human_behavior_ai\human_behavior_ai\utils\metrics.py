"""
Metrics computation and logging utilities for HumanBehaviorAI.
"""

import torch
import numpy as np
from typing import Dict, Any, List, Optional, Union
import logging
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import wandb

logger = logging.getLogger(__name__)


def compute_metrics(
    predictions: Union[torch.Tensor, np.ndarray],
    targets: Union[torch.Tensor, np.ndarray],
    metric_types: Optional[List[str]] = None
) -> Dict[str, float]:
    """
    Compute various metrics for model evaluation.
    
    Args:
        predictions: Model predictions
        targets: Ground truth targets
        metric_types: List of metrics to compute. If None, computes all available metrics.
        
    Returns:
        Dictionary of computed metrics
    """
    # Convert to numpy if needed
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.detach().cpu().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.detach().cpu().numpy()
    
    # Flatten arrays for metric computation
    predictions_flat = predictions.flatten()
    targets_flat = targets.flatten()
    
    # Default metrics
    if metric_types is None:
        metric_types = ['mse', 'mae', 'rmse', 'r2', 'mape']
    
    metrics = {}
    
    for metric_type in metric_types:
        try:
            if metric_type == 'mse':
                metrics['mse'] = float(mean_squared_error(targets_flat, predictions_flat))
            elif metric_type == 'mae':
                metrics['mae'] = float(mean_absolute_error(targets_flat, predictions_flat))
            elif metric_type == 'rmse':
                metrics['rmse'] = float(np.sqrt(mean_squared_error(targets_flat, predictions_flat)))
            elif metric_type == 'r2':
                metrics['r2'] = float(r2_score(targets_flat, predictions_flat))
            elif metric_type == 'mape':
                # Mean Absolute Percentage Error
                mask = targets_flat != 0
                if mask.sum() > 0:
                    mape = np.mean(np.abs((targets_flat[mask] - predictions_flat[mask]) / targets_flat[mask])) * 100
                    metrics['mape'] = float(mape)
                else:
                    metrics['mape'] = float('inf')
            elif metric_type == 'correlation':
                correlation = np.corrcoef(targets_flat, predictions_flat)[0, 1]
                metrics['correlation'] = float(correlation) if not np.isnan(correlation) else 0.0
        except Exception as e:
            logger.warning(f"Failed to compute metric {metric_type}: {e}")
            metrics[metric_type] = float('nan')
    
    return metrics


def compute_behavioral_metrics(
    predicted_trajectories: np.ndarray,
    target_trajectories: np.ndarray
) -> Dict[str, float]:
    """
    Compute behavior-specific metrics for trajectory evaluation.
    
    Args:
        predicted_trajectories: Predicted behavior trajectories [batch, seq_len, features]
        target_trajectories: Target behavior trajectories [batch, seq_len, features]
        
    Returns:
        Dictionary of behavioral metrics
    """
    metrics = {}
    
    # Trajectory similarity metrics
    try:
        # Dynamic Time Warping distance (simplified)
        dtw_distances = []
        for pred, target in zip(predicted_trajectories, target_trajectories):
            dtw_dist = np.linalg.norm(pred - target, axis=1).mean()
            dtw_distances.append(dtw_dist)
        metrics['dtw_distance'] = float(np.mean(dtw_distances))
        
        # Velocity correlation
        pred_velocities = np.diff(predicted_trajectories, axis=1)
        target_velocities = np.diff(target_trajectories, axis=1)
        
        velocity_correlations = []
        for pred_vel, target_vel in zip(pred_velocities, target_velocities):
            corr = np.corrcoef(pred_vel.flatten(), target_vel.flatten())[0, 1]
            if not np.isnan(corr):
                velocity_correlations.append(corr)
        
        if velocity_correlations:
            metrics['velocity_correlation'] = float(np.mean(velocity_correlations))
        else:
            metrics['velocity_correlation'] = 0.0
        
        # Acceleration correlation
        pred_accelerations = np.diff(pred_velocities, axis=1)
        target_accelerations = np.diff(target_velocities, axis=1)
        
        accel_correlations = []
        for pred_acc, target_acc in zip(pred_accelerations, target_accelerations):
            corr = np.corrcoef(pred_acc.flatten(), target_acc.flatten())[0, 1]
            if not np.isnan(corr):
                accel_correlations.append(corr)
        
        if accel_correlations:
            metrics['acceleration_correlation'] = float(np.mean(accel_correlations))
        else:
            metrics['acceleration_correlation'] = 0.0
            
    except Exception as e:
        logger.warning(f"Failed to compute behavioral metrics: {e}")
        metrics.update({
            'dtw_distance': float('nan'),
            'velocity_correlation': float('nan'),
            'acceleration_correlation': float('nan')
        })
    
    return metrics


def log_metrics(
    metrics: Dict[str, float],
    step: Optional[int] = None,
    prefix: str = "",
    use_wandb: bool = True,
    use_tensorboard: bool = False,
    tb_writer: Optional[Any] = None
) -> None:
    """
    Log metrics to various logging backends.
    
    Args:
        metrics: Dictionary of metrics to log
        step: Current step/epoch number
        prefix: Prefix to add to metric names
        use_wandb: Whether to log to Weights & Biases
        use_tensorboard: Whether to log to TensorBoard
        tb_writer: TensorBoard writer instance
    """
    # Add prefix to metric names
    if prefix:
        prefixed_metrics = {f"{prefix}/{k}": v for k, v in metrics.items()}
    else:
        prefixed_metrics = metrics
    
    # Log to console
    metric_str = ", ".join([f"{k}: {v:.4f}" for k, v in prefixed_metrics.items() if not np.isnan(v)])
    if step is not None:
        logger.info(f"Step {step} - {metric_str}")
    else:
        logger.info(f"Metrics - {metric_str}")
    
    # Log to Weights & Biases
    if use_wandb:
        try:
            if step is not None:
                wandb.log(prefixed_metrics, step=step)
            else:
                wandb.log(prefixed_metrics)
        except Exception as e:
            logger.warning(f"Failed to log to wandb: {e}")
    
    # Log to TensorBoard
    if use_tensorboard and tb_writer is not None:
        try:
            for name, value in prefixed_metrics.items():
                if not np.isnan(value):
                    tb_writer.add_scalar(name, value, step or 0)
        except Exception as e:
            logger.warning(f"Failed to log to tensorboard: {e}")


def aggregate_metrics(metric_list: List[Dict[str, float]]) -> Dict[str, float]:
    """
    Aggregate metrics across multiple batches or epochs.
    
    Args:
        metric_list: List of metric dictionaries
        
    Returns:
        Aggregated metrics (mean values)
    """
    if not metric_list:
        return {}
    
    # Get all metric names
    all_keys = set()
    for metrics in metric_list:
        all_keys.update(metrics.keys())
    
    # Aggregate each metric
    aggregated = {}
    for key in all_keys:
        values = [metrics.get(key, float('nan')) for metrics in metric_list]
        valid_values = [v for v in values if not np.isnan(v)]
        
        if valid_values:
            aggregated[key] = float(np.mean(valid_values))
            aggregated[f"{key}_std"] = float(np.std(valid_values))
        else:
            aggregated[key] = float('nan')
            aggregated[f"{key}_std"] = float('nan')
    
    return aggregated
