2025-07-06 22:42:20,506 INFO    MainThread:19876 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-06 22:42:20,507 INFO    MainThread:19876 [wandb_setup.py:_flush():80] Configure stats pid to 19876
2025-07-06 22:42:20,507 INFO    MainThread:19876 [wandb_setup.py:_flush():80] Loading settings from C:\Users\<USER>\.config\wandb\settings
2025-07-06 22:42:20,507 INFO    MainThread:19876 [wandb_setup.py:_flush():80] Loading settings from C:\My_Applications\UIs\human_behavior_ai\wandb\settings
2025-07-06 22:42:20,507 INFO    MainThread:19876 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-06 22:42:20,508 INFO    MainThread:19876 [wandb_init.py:setup_run_log_directory():703] Logging user logs to outputs\logs\wandb\offline-run-20250706_224220-6ngwfs12\logs\debug.log
2025-07-06 22:42:20,508 INFO    MainThread:19876 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to outputs\logs\wandb\offline-run-20250706_224220-6ngwfs12\logs\debug-internal.log
2025-07-06 22:42:20,509 INFO    MainThread:19876 [wandb_init.py:init():830] calling init triggers
2025-07-06 22:42:20,509 INFO    MainThread:19876 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'model_type': 'hybrid', 'config': None, 'data_path': 'data/sessions', 'val_data_path': None, 'sequence_length': 1024, 'batch_size': 8, 'val_batch_size': 64, 'epochs': 1, 'learning_rate': 0.0001, 'weight_decay': 1e-05, 'warmup_steps': 1000, 'gradient_clip': 1.0, 'training_strategy': 'multi_stage', 'mixed_precision': False, 'gradient_accumulation': 1, 'distributed': False, 'local_rank': 0, 'output_dir': './outputs', 'save_every': 1000, 'eval_every': 500, 'log_every': 100, 'wandb_project': 'human-behavior-ai', 'wandb_run_name': None, 'no_wandb': False, 'num_workers': 4, 'pin_memory': False, 'compile': False, 'debug': True, 'profile': False, 'seed': 42, '_wandb': {}}
2025-07-06 22:42:20,509 INFO    MainThread:19876 [wandb_init.py:init():871] starting backend
2025-07-06 22:42:21,129 INFO    MainThread:19876 [wandb_init.py:init():874] sending inform_init request
2025-07-06 22:42:21,198 INFO    MainThread:19876 [wandb_init.py:init():882] backend started and connected
2025-07-06 22:42:21,200 INFO    MainThread:19876 [wandb_init.py:init():953] updated telemetry
2025-07-06 22:42:21,286 INFO    MainThread:19876 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-07-06 22:42:23,953 INFO    MainThread:19876 [wandb_init.py:init():1029] starting run threads in backend
2025-07-06 22:42:24,674 INFO    MainThread:19876 [wandb_run.py:_console_start():2458] atexit reg
2025-07-06 22:42:24,674 INFO    MainThread:19876 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-07-06 22:42:24,675 INFO    MainThread:19876 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-07-06 22:42:24,675 INFO    MainThread:19876 [wandb_run.py:_redirect():2398] Redirects installed.
2025-07-06 22:42:24,683 INFO    MainThread:19876 [wandb_init.py:init():1075] run started, returning control to user process
2025-07-06 22:42:29,009 INFO    MsgRouterThr:19876 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 0 handles.
