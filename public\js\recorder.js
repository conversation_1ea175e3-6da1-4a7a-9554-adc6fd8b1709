// High-Frequency Interaction Recorder
class InteractionRecorder {
    constructor() {
        this.isRecording = false;
        this.isPaused = false;
        this.sessionId = null;
        this.startTime = null;
        this.dataBuffer = [];
        this.currentTask = 0;
        this.totalTasks = 8;
        this.tasks = this.initializeTasks();
        this.lastMousePosition = { x: 0, y: 0 };
        this.lastTimestamp = 0;
        this.captureInterval = null;
        this.saveInterval = null;
        this.sessionTimer = null;
        
        // High-frequency capture settings (144Hz ≈ 6.94ms)
        this.captureFrequency = 144;
        this.captureInterval_ms = 1000 / this.captureFrequency;
        this.batchSize = 100; // Save data in batches
        
        this.setupEventListeners();
    }

    initializeTasks() {
        return [
            {
                title: "Mouse Movement Calibration",
                description: "Move your mouse to each point in sequence to calibrate tracking.",
                type: "mouse_movement",
                duration: 120000, // 2 minutes
                instructions: "Move your mouse to each numbered point in order. Complete all 100 points to finish the task.",
                countdown: 5
            },
            {
                title: "Text Input and Editing",
                description: "Type the provided text and make corrections to capture typing patterns.",
                type: "typing",
                duration: 180000, // 3 minutes
                instructions: "Type the sample text shown in the scrolling box. The text will move to keep your current position visible. Focus on your natural typing rhythm.",
                countdown: 5
            },
            {
                title: "Drag and Drop Operations",
                description: "Drag items between containers to capture drag behavior.",
                type: "drag_drop",
                duration: 90000, // 1.5 minutes
                instructions: "Drag each item to its corresponding container.",
                countdown: 5
            },
            {
                title: "Scrolling Patterns",
                description: "Scroll through content using different techniques.",
                type: "scrolling",
                duration: 75000, // 1.25 minutes
                instructions: "Scroll through the content using mouse wheel, trackpad, and scrollbar.",
                countdown: 5
            }
        ];
    }

    setupEventListeners() {
        // Mouse events
        document.addEventListener('mousemove', this.handleMouseMove.bind(this), { passive: true });
        document.addEventListener('mousedown', this.handleMouseDown.bind(this), { passive: true });
        document.addEventListener('mouseup', this.handleMouseUp.bind(this), { passive: true });
        document.addEventListener('click', this.handleClick.bind(this), { passive: true });
        document.addEventListener('wheel', this.handleWheel.bind(this), { passive: true });

        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown.bind(this), { passive: true });
        document.addEventListener('keyup', this.handleKeyUp.bind(this), { passive: true });

        // Window events
        window.addEventListener('resize', this.handleResize.bind(this), { passive: true });
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    }

    async start() {
        try {
            // Start new session
            const response = await fetch('/api/sessions/start', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    metadata: {
                        userAgent: navigator.userAgent,
                        screenResolution: `${screen.width}x${screen.height}`,
                        viewportSize: `${window.innerWidth}x${window.innerHeight}`,
                        captureFrequency: this.captureFrequency
                    }
                })
            });

            const data = await response.json();
            this.sessionId = data.sessionId;
            this.startTime = performance.now();
            this.isRecording = true;
            this.currentTask = 0;

            // Update UI
            this.showRecordingInterface();
            this.startTask(0);
            this.startCapture();
            this.startTimer();

            console.log(`Recording started - Session ID: ${this.sessionId}`);
        } catch (error) {
            console.error('Failed to start recording:', error);
            alert('Failed to start recording session');
        }
    }

    startCapture() {
        // High-frequency mouse position capture
        this.captureInterval = setInterval(() => {
            if (this.isRecording && !this.isPaused) {
                this.captureMousePosition();
            }
        }, this.captureInterval_ms);

        // Periodic data saving
        this.saveInterval = setInterval(() => {
            if (this.dataBuffer.length > 0) {
                this.saveDataBatch();
            }
        }, 5000); // Save every 5 seconds
    }

    captureMousePosition() {
        const now = performance.now();
        const currentPos = { x: this.mouseX || 0, y: this.mouseY || 0 };
        
        // Calculate velocity and acceleration
        const deltaTime = now - this.lastTimestamp;
        const deltaX = currentPos.x - this.lastMousePosition.x;
        const deltaY = currentPos.y - this.lastMousePosition.y;
        
        const velocity = deltaTime > 0 ? {
            x: deltaX / deltaTime,
            y: deltaY / deltaTime,
            magnitude: Math.sqrt(deltaX * deltaX + deltaY * deltaY) / deltaTime
        } : { x: 0, y: 0, magnitude: 0 };

        this.addDataPoint({
            type: 'mouse_position',
            timestamp: now,
            data: {
                position: currentPos,
                velocity: velocity,
                deltaTime: deltaTime,
                taskId: this.currentTask
            }
        });

        this.lastMousePosition = currentPos;
        this.lastTimestamp = now;
    }

    handleMouseMove(event) {
        this.mouseX = event.clientX;
        this.mouseY = event.clientY;
        
        if (this.isRecording && !this.isPaused) {
            this.addDataPoint({
                type: 'mouse_move',
                timestamp: performance.now(),
                data: {
                    x: event.clientX,
                    y: event.clientY,
                    screenX: event.screenX,
                    screenY: event.screenY,
                    movementX: event.movementX,
                    movementY: event.movementY,
                    taskId: this.currentTask
                }
            });
        }
    }

    handleMouseDown(event) {
        if (this.isRecording && !this.isPaused) {
            this.addDataPoint({
                type: 'mouse_down',
                timestamp: performance.now(),
                data: {
                    button: event.button,
                    buttons: event.buttons,
                    x: event.clientX,
                    y: event.clientY,
                    taskId: this.currentTask
                }
            });
        }
    }

    handleMouseUp(event) {
        if (this.isRecording && !this.isPaused) {
            this.addDataPoint({
                type: 'mouse_up',
                timestamp: performance.now(),
                data: {
                    button: event.button,
                    buttons: event.buttons,
                    x: event.clientX,
                    y: event.clientY,
                    taskId: this.currentTask
                }
            });
        }
    }

    handleClick(event) {
        if (this.isRecording && !this.isPaused) {
            this.addDataPoint({
                type: 'click',
                timestamp: performance.now(),
                data: {
                    button: event.button,
                    x: event.clientX,
                    y: event.clientY,
                    detail: event.detail, // click count
                    taskId: this.currentTask
                }
            });
        }
    }

    handleWheel(event) {
        if (this.isRecording && !this.isPaused) {
            this.addDataPoint({
                type: 'wheel',
                timestamp: performance.now(),
                data: {
                    deltaX: event.deltaX,
                    deltaY: event.deltaY,
                    deltaZ: event.deltaZ,
                    deltaMode: event.deltaMode,
                    x: event.clientX,
                    y: event.clientY,
                    taskId: this.currentTask
                }
            });
        }
    }

    handleKeyDown(event) {
        if (this.isRecording && !this.isPaused) {
            this.addDataPoint({
                type: 'key_down',
                timestamp: performance.now(),
                data: {
                    key: event.key,
                    code: event.code,
                    keyCode: event.keyCode,
                    altKey: event.altKey,
                    ctrlKey: event.ctrlKey,
                    shiftKey: event.shiftKey,
                    metaKey: event.metaKey,
                    repeat: event.repeat,
                    taskId: this.currentTask
                }
            });
        }
    }

    handleKeyUp(event) {
        if (this.isRecording && !this.isPaused) {
            this.addDataPoint({
                type: 'key_up',
                timestamp: performance.now(),
                data: {
                    key: event.key,
                    code: event.code,
                    keyCode: event.keyCode,
                    altKey: event.altKey,
                    ctrlKey: event.ctrlKey,
                    shiftKey: event.shiftKey,
                    metaKey: event.metaKey,
                    taskId: this.currentTask
                }
            });
        }
    }

    handleResize(event) {
        if (this.isRecording && !this.isPaused) {
            this.addDataPoint({
                type: 'resize',
                timestamp: performance.now(),
                data: {
                    innerWidth: window.innerWidth,
                    innerHeight: window.innerHeight,
                    outerWidth: window.outerWidth,
                    outerHeight: window.outerHeight,
                    taskId: this.currentTask
                }
            });
        }
    }

    addDataPoint(dataPoint) {
        this.dataBuffer.push(dataPoint);
        
        // Update UI counter
        const counter = document.getElementById('data-points');
        if (counter) {
            counter.textContent = this.dataBuffer.length.toLocaleString();
        }

        // Auto-save if buffer is full
        if (this.dataBuffer.length >= this.batchSize) {
            this.saveDataBatch();
        }
    }

    async saveDataBatch() {
        if (this.dataBuffer.length === 0 || !this.sessionId) return;

        try {
            const dataToSave = [...this.dataBuffer];
            this.dataBuffer = [];

            await fetch(`/api/sessions/${this.sessionId}/data`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ data: dataToSave })
            });

            console.log(`Saved ${dataToSave.length} data points`);
        } catch (error) {
            console.error('Failed to save data batch:', error);
            // Re-add data to buffer on failure
            this.dataBuffer.unshift(...dataToSave);
        }
    }

    showRecordingInterface() {
        document.getElementById('recording-setup').classList.add('hidden');
        document.getElementById('recording-active').classList.remove('hidden');
        document.getElementById('recording-complete').classList.add('hidden');
    }

    startTimer() {
        const timerElement = document.getElementById('session-timer');
        this.sessionTimer = setInterval(() => {
            if (this.isRecording && !this.isPaused) {
                const elapsed = performance.now() - this.startTime;
                const minutes = Math.floor(elapsed / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }, 1000);
    }

    startTask(taskIndex) {
        if (taskIndex >= this.tasks.length) {
            this.completeSession();
            return;
        }

        const task = this.tasks[taskIndex];
        this.currentTask = taskIndex;

        // Update UI
        document.getElementById('task-title').textContent = task.title;
        document.getElementById('task-description').textContent = task.description;
        document.getElementById('task-counter').textContent = `Task ${taskIndex + 1} of 4`;

        // Update progress bar
        const progress = ((taskIndex) / this.totalTasks) * 100;
        document.getElementById('progress-fill').style.width = `${progress}%`;

        // Show countdown before starting task
        this.showCountdown(task, () => {
            // Generate task content after countdown
            this.generateTaskContent(task);

            // Only auto-advance for tasks that don't have specific completion criteria
            if (task.type === 'scrolling') {
                setTimeout(() => {
                    if (this.currentTask === taskIndex && this.isRecording) {
                        this.completeTask();
                    }
                }, task.duration);
            }
        });

        console.log(`Started task ${taskIndex + 1}: ${task.title}`);
    }

    showCountdown(task, callback) {
        const taskArea = document.getElementById('task-content');
        let countdown = task.countdown || 5;

        const countdownHtml = `
            <div class="countdown-container">
                <div class="task-instructions">
                    <p>${task.instructions}</p>
                </div>
                <div class="countdown-display">
                    <div class="countdown-number">${countdown}</div>
                    <div class="countdown-text">Task starting in...</div>
                </div>
            </div>
        `;

        taskArea.innerHTML = countdownHtml;

        const countdownInterval = setInterval(() => {
            countdown--;
            const countdownNumber = document.querySelector('.countdown-number');
            if (countdownNumber) {
                countdownNumber.textContent = countdown;
            }

            if (countdown <= 0) {
                clearInterval(countdownInterval);
                callback();
            }
        }, 1000);
    }

    generateTaskContent(task) {
        const container = document.getElementById('task-content');
        
        switch (task.type) {
            case 'mouse_movement':
                this.generateMouseMovementTask(container);
                break;
                

                
            case 'typing':
                this.generateTypingTask(container);
                break;

            case 'drag_drop':
                this.generateDragDropTask(container);
                break;

            case 'scrolling':
                this.generateScrollingTask(container);
                break;

            case 'form_filling':
                this.generateFormTask(container);
                break;

            case 'navigation':
                this.generateNavigationTask(container);
                break;

            case 'free_form':
                this.generateFreeFormTask(container);
                break;

            default:
                container.innerHTML = `
                    <div style="text-align: center; padding: 2rem; color: var(--text-secondary);">
                        <i class="fas fa-tasks" style="font-size: 2rem; margin-bottom: 1rem; display: block;"></i>
                        ${task.instructions}
                    </div>
                `;
        }
    }

    generateMouseMovementTask(container) {
        container.innerHTML = `
            <div class="movement-area" style="width: 600px; height: 400px; border: 2px solid var(--accent-primary); border-radius: 8px; position: relative; background: var(--bg-primary); margin: 0 auto;">
                <div id="movement-points"></div>
                <div class="movement-stats" style="position: absolute; top: 10px; left: 10px; background: rgba(0,0,0,0.7); color: white; padding: 0.5rem; border-radius: 4px; font-size: 0.9rem;">
                    Point: <span id="current-point">1</span> / 100
                </div>
            </div>
        `;

        this.setupMouseMovementTask();
    }



    setupMouseMovementTask() {
        const container = document.getElementById('movement-points');
        if (!container) return;

        let currentPointIndex = 0;
        const totalPoints = 100;
        const points = [];
        const self = this;

        // Generate 100 random points within the container
        const containerWidth = 600 - 60; // Account for point size and padding
        const containerHeight = 400 - 60;

        for (let i = 0; i < totalPoints; i++) {
            points.push({
                x: Math.random() * containerWidth + 30,
                y: Math.random() * containerHeight + 30,
                number: i + 1
            });
        }

        // Create point elements
        const createPoint = (point, isNext = false) => {
            const pointElement = document.createElement('div');
            pointElement.className = isNext ? 'movement-point next-point' : 'movement-point current-point';
            pointElement.style.cssText = `
                position: absolute;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                background: ${isNext ? 'rgba(255, 165, 0, 0.6)' : 'var(--accent-primary)'};
                border: 2px solid ${isNext ? '#FFA500' : 'white'};
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                font-size: 0.8rem;
                cursor: pointer;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                left: ${point.x}px;
                top: ${point.y}px;
                z-index: ${isNext ? 1 : 2};
                transform: scale(0);
                opacity: 0;
                box-shadow: ${isNext ? '0 4px 15px rgba(255, 165, 0, 0.3)' : '0 4px 15px rgba(59, 130, 246, 0.4)'};
            `;
            pointElement.textContent = point.number;

            // Animate in
            setTimeout(() => {
                pointElement.style.transform = 'scale(1)';
                pointElement.style.opacity = '1';
            }, 50);



            return pointElement;
        };

        // Show current and next point
        const updatePoints = () => {
            // Check if next point already exists (to avoid re-animation)
            const existingNextPoint = container.querySelector('.next-point');
            let shouldAnimateNext = true;

            if (existingNextPoint && currentPointIndex + 1 < totalPoints) {
                // Check if the existing next point is the one we want to show as current
                const nextPointNumber = parseInt(existingNextPoint.textContent);
                if (nextPointNumber === points[currentPointIndex].number) {
                    // Convert existing next point to current point
                    existingNextPoint.className = 'movement-point current-point';
                    existingNextPoint.style.background = 'var(--accent-primary)';
                    existingNextPoint.style.border = '2px solid white';
                    existingNextPoint.style.zIndex = '2';
                    existingNextPoint.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.4)';
                    existingNextPoint.style.opacity = '1';

                    // Remove other points except the converted one
                    const allPoints = container.querySelectorAll('.movement-point');
                    allPoints.forEach(point => {
                        if (point !== existingNextPoint) {
                            point.remove();
                        }
                    });

                    // Add new next point if exists
                    if (currentPointIndex + 1 < totalPoints) {
                        const nextPoint = createPoint(points[currentPointIndex + 1], true);
                        container.appendChild(nextPoint);
                    }

                    // Add click handler to the converted current point
                    const currentPoint = existingNextPoint;
                    addClickHandler(currentPoint);
                    return;
                }
            }

            // Default behavior - clear and recreate
            container.innerHTML = '';

            if (currentPointIndex < totalPoints) {
                // Show current point
                const currentPoint = createPoint(points[currentPointIndex]);
                container.appendChild(currentPoint);

                // Show next point if exists
                if (currentPointIndex + 1 < totalPoints) {
                    const nextPoint = createPoint(points[currentPointIndex + 1], true);
                    container.appendChild(nextPoint);
                }

                // Add click handler to current point
                addClickHandler(currentPoint);
            }
        };

        // Extract click handler function
        const addClickHandler = (currentPoint) => {
            currentPoint.addEventListener('click', () => {
                    // Success animation
                    currentPoint.style.background = 'linear-gradient(45deg, #10b981, #059669)';
                    currentPoint.style.transform = 'scale(1.3)';
                    currentPoint.style.boxShadow = '0 8px 25px rgba(16, 185, 129, 0.6)';
                    currentPoint.style.animation = 'none';

                    // Ripple effect
                    const ripple = document.createElement('div');
                    ripple.style.cssText = `
                        position: absolute;
                        width: 60px;
                        height: 60px;
                        border-radius: 50%;
                        background: rgba(16, 185, 129, 0.3);
                        left: ${points[currentPointIndex].x - 15}px;
                        top: ${points[currentPointIndex].y - 15}px;
                        transform: scale(0);
                        animation: ripple 0.6s ease-out;
                        pointer-events: none;
                    `;
                    container.appendChild(ripple);

                    setTimeout(() => ripple.remove(), 600);

                    setTimeout(() => {
                        // Fade out current point
                        currentPoint.style.transform = 'scale(0)';
                        currentPoint.style.opacity = '0';

                        setTimeout(() => {
                            currentPointIndex++;
                            document.getElementById('current-point').textContent = currentPointIndex + 1;

                            if (currentPointIndex >= totalPoints) {
                                // Task complete with celebration animation
                                container.innerHTML = `
                                    <div style="text-align: center; padding: 2rem; color: var(--success-color); font-size: 1.2rem; animation: fadeInUp 0.6s ease-out;">
                                        <i class="fas fa-check-circle" style="font-size: 3rem; margin-bottom: 1rem; display: block; animation: bounceIn 0.8s ease-out;"></i>
                                        <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">Congratulations!</div>
                                        <div>All 100 points completed!</div>
                                    </div>
                                `;
                                setTimeout(() => {
                                    self.completeTask();
                                }, 2000);
                            } else {
                                updatePoints();
                            }
                        }, 300);
                    }, 400);
            });
        };

        // Start the task
        updatePoints();
    }

    generateTypingTask(container) {
        const sampleText = "In the realm of artificial intelligence and machine learning, the development of sophisticated algorithms has revolutionized how we approach complex problem-solving tasks. Natural language processing, computer vision, and predictive analytics have become integral components of modern technology systems. The ability to process vast amounts of data and extract meaningful insights has transformed industries ranging from healthcare and finance to transportation and entertainment. Machine learning models, particularly deep neural networks, have demonstrated remarkable capabilities in pattern recognition, decision-making, and autonomous operation. As we continue to advance these technologies, we must also consider the ethical implications and ensure that artificial intelligence systems are developed responsibly, with proper safeguards and transparency measures in place. The future of AI holds immense promise for solving some of humanity's greatest challenges, from climate change and disease prevention to space exploration and sustainable development.";

        container.innerHTML = `
            <div class="typing-area" style="width: 100%; max-width: 800px;">
                <div class="typing-container">
                    <div class="text-display-container" style="background: var(--bg-primary); border: 2px solid var(--border-color); border-radius: 8px; margin-bottom: 1rem; height: 80px; overflow: hidden; position: relative;">
                        <div id="text-display" style="font-family: monospace; font-size: 1.2rem; line-height: 1.6; padding: 1rem; white-space: nowrap; transition: transform 0.2s ease; position: absolute; top: 50%; transform: translateY(-50%);"></div>
                    </div>
                    <textarea id="typing-input" placeholder="Start typing the text shown above..." style="width: 100%; height: 100px; padding: 1rem; border: 2px solid var(--border-color); border-radius: 8px; background: var(--bg-secondary); color: var(--text-primary); font-family: monospace; font-size: 1.2rem; resize: none; line-height: 1.6;"></textarea>
                    <div class="typing-stats" style="margin-top: 1rem; display: flex; gap: 2rem; font-size: 0.9rem; color: var(--text-secondary);">
                        <span>Progress: <span id="typing-progress">0</span>/<span id="typing-total">${sampleText.length}</span></span>
                        <span>Accuracy: <span id="typing-accuracy">100%</span></span>
                        <span>WPM: <span id="typing-wpm">0</span></span>
                    </div>
                </div>
            </div>
        `;

        this.setupTypingTask(sampleText);
    }

    setupTypingTask(sampleText) {
        const textDisplay = document.getElementById('text-display');
        const typingInput = document.getElementById('typing-input');
        const progressSpan = document.getElementById('typing-progress');
        const accuracySpan = document.getElementById('typing-accuracy');
        const wpmSpan = document.getElementById('typing-wpm');

        let startTime = null;
        let correctChars = 0;
        let totalChars = 0;

        // Initialize text display
        this.updateTextDisplay(sampleText, '', 0);

        typingInput.addEventListener('input', (e) => {
            if (!startTime) startTime = Date.now();

            const typed = e.target.value;
            const position = typed.length;

            // Update text display with current position
            this.updateTextDisplay(sampleText, typed, position);

            // Calculate stats
            totalChars = typed.length;
            correctChars = 0;

            for (let i = 0; i < Math.min(typed.length, sampleText.length); i++) {
                if (typed[i] === sampleText[i]) {
                    correctChars++;
                }
            }

            const accuracy = totalChars > 0 ? Math.round((correctChars / totalChars) * 100) : 100;
            const timeElapsed = (Date.now() - startTime) / 1000 / 60; // minutes
            const wordsTyped = correctChars / 5; // standard: 5 chars = 1 word
            const wpm = timeElapsed > 0 ? Math.round(wordsTyped / timeElapsed) : 0;

            // Update UI
            progressSpan.textContent = position;
            accuracySpan.textContent = `${accuracy}%`;
            wpmSpan.textContent = wpm;

            // Check if task is complete
            if (typed === sampleText) {
                typingInput.disabled = true;
                typingInput.style.background = '#10b981';
                typingInput.style.color = 'white';
                // Complete the task automatically
                setTimeout(() => {
                    this.completeTask();
                }, 1000);
            }
        });
    }

    updateTextDisplay(fullText, typedText, currentPosition) {
        const textDisplay = document.getElementById('text-display');
        if (!textDisplay) return;

        let html = '';

        for (let i = 0; i < fullText.length; i++) {
            let char = fullText[i];
            let className = '';

            if (i < typedText.length) {
                // Already typed
                if (typedText[i] === fullText[i]) {
                    className = 'correct';
                } else {
                    className = 'incorrect';
                }
            } else if (i === currentPosition) {
                // Current character
                className = 'current';
            }

            if (char === ' ') char = '&nbsp;';

            html += `<span class="${className}">${char}</span>`;
        }

        textDisplay.innerHTML = html;

        // Scroll horizontally to keep current position centered
        const currentChar = textDisplay.querySelector('.current');
        if (currentChar) {
            const container = textDisplay.parentElement;
            const containerWidth = container.offsetWidth;
            const charOffsetLeft = currentChar.offsetLeft;

            // Calculate the scroll position to center the current character
            const scrollLeft = charOffsetLeft - (containerWidth / 2);

            // Apply the scroll with smooth transition
            textDisplay.style.transform = `translateY(-50%) translateX(-${Math.max(0, scrollLeft)}px)`;
        }
    }

    generateDragDropTask(container) {
        container.innerHTML = `
            <div class="drag-drop-area" style="width: 100%; max-width: 800px;">
                <div class="drag-drop-instructions" style="margin-bottom: 2rem; text-align: center; color: var(--text-secondary);">
                    <p>Drag each item to its corresponding container</p>
                </div>
                <div class="drag-drop-container" style="display: flex; gap: 2rem; justify-content: space-between;">
                    <div class="items-container" style="flex: 1;">
                        <h4 style="text-align: center; margin-bottom: 1rem; color: var(--text-primary);">Items to Drag</h4>
                        <div id="draggable-items" style="display: flex; flex-direction: column; gap: 1rem; min-height: 300px; padding: 1rem; background: var(--bg-primary); border: 2px dashed var(--border-color); border-radius: 8px;">
                            <div class="drag-item" draggable="true" data-category="fruits" style="padding: 1rem; background: var(--accent-primary); color: white; border-radius: 8px; cursor: grab; text-align: center; user-select: none;">
                                🍎 Apple
                            </div>
                            <div class="drag-item" draggable="true" data-category="animals" style="padding: 1rem; background: var(--accent-secondary); color: white; border-radius: 8px; cursor: grab; text-align: center; user-select: none;">
                                🐱 Cat
                            </div>
                            <div class="drag-item" draggable="true" data-category="fruits" style="padding: 1rem; background: var(--accent-primary); color: white; border-radius: 8px; cursor: grab; text-align: center; user-select: none;">
                                🍌 Banana
                            </div>
                            <div class="drag-item" draggable="true" data-category="vehicles" style="padding: 1rem; background: #e74c3c; color: white; border-radius: 8px; cursor: grab; text-align: center; user-select: none;">
                                🚗 Car
                            </div>
                            <div class="drag-item" draggable="true" data-category="animals" style="padding: 1rem; background: var(--accent-secondary); color: white; border-radius: 8px; cursor: grab; text-align: center; user-select: none;">
                                🐕 Dog
                            </div>
                            <div class="drag-item" draggable="true" data-category="vehicles" style="padding: 1rem; background: #e74c3c; color: white; border-radius: 8px; cursor: grab; text-align: center; user-select: none;">
                                ✈️ Airplane
                            </div>
                        </div>
                    </div>
                    <div class="drop-containers" style="flex: 1;">
                        <h4 style="text-align: center; margin-bottom: 1rem; color: var(--text-primary);">Drop Zones</h4>
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <div class="drop-zone" data-category="fruits" style="min-height: 80px; padding: 1rem; background: var(--bg-secondary); border: 2px dashed var(--accent-primary); border-radius: 8px; text-align: center; display: flex; align-items: center; justify-content: center; color: var(--text-secondary);">
                                🍎 Fruits
                            </div>
                            <div class="drop-zone" data-category="animals" style="min-height: 80px; padding: 1rem; background: var(--bg-secondary); border: 2px dashed var(--accent-secondary); border-radius: 8px; text-align: center; display: flex; align-items: center; justify-content: center; color: var(--text-secondary);">
                                🐱 Animals
                            </div>
                            <div class="drop-zone" data-category="vehicles" style="min-height: 80px; padding: 1rem; background: var(--bg-secondary); border: 2px dashed #e74c3c; border-radius: 8px; text-align: center; display: flex; align-items: center; justify-content: center; color: var(--text-secondary);">
                                🚗 Vehicles
                            </div>
                        </div>
                    </div>
                </div>
                <div class="drag-drop-progress" style="margin-top: 2rem; text-align: center;">
                    <span id="drag-progress">0/6 items placed correctly</span>
                </div>
            </div>
        `;

        this.setupDragDropTask();
    }

    setupDragDropTask() {
        const dragItems = document.querySelectorAll('.drag-item');
        const dropZones = document.querySelectorAll('.drop-zone');
        let correctPlacements = 0;
        const self = this; // Store reference to this for use in event handlers

        dragItems.forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', e.target.dataset.category);
                e.dataTransfer.setData('text/html', e.target.outerHTML);
                e.target.style.opacity = '0.5';
            });

            item.addEventListener('dragend', (e) => {
                e.target.style.opacity = '1';
            });
        });

        dropZones.forEach(zone => {
            zone.addEventListener('dragover', (e) => {
                e.preventDefault();
                zone.style.background = 'var(--accent-primary-light)';
                zone.style.transform = 'scale(1.02)';
            });

            zone.addEventListener('dragleave', (e) => {
                zone.style.background = 'var(--bg-secondary)';
                zone.style.transform = 'scale(1)';
            });

            zone.addEventListener('drop', (e) => {
                e.preventDefault();
                const draggedCategory = e.dataTransfer.getData('text/plain');
                const draggedHTML = e.dataTransfer.getData('text/html');

                zone.style.background = 'var(--bg-secondary)';
                zone.style.transform = 'scale(1)';

                if (draggedCategory === zone.dataset.category) {
                    // Correct placement
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = draggedHTML;
                    const draggedElement = tempDiv.firstChild;

                    // Remove from original location
                    const originalElement = document.querySelector(`[data-category="${draggedCategory}"][draggable="true"]`);
                    if (originalElement) {
                        originalElement.remove();
                        correctPlacements++;
                    }

                    // Add to drop zone
                    draggedElement.draggable = false;
                    draggedElement.style.cursor = 'default';
                    draggedElement.style.margin = '0.5rem 0';
                    zone.appendChild(draggedElement);

                    // Update progress
                    document.getElementById('drag-progress').textContent = `${correctPlacements}/6 items placed correctly`;

                    // Check if task is complete
                    if (correctPlacements === 6) {
                        setTimeout(() => {
                            alert('Great job! All items placed correctly!');
                            // Complete the task automatically
                            setTimeout(() => {
                                self.completeTask();
                            }, 1500);
                        }, 500);
                    }
                } else {
                    // Incorrect placement - visual feedback
                    zone.style.background = 'var(--error-color)';
                    setTimeout(() => {
                        zone.style.background = 'var(--bg-secondary)';
                    }, 1000);
                }
            });
        });
    }

    generateScrollingTask(container) {
        container.innerHTML = `
            <div class="scrolling-task-container" style="width: 100%; max-width: 800px; position: relative;">
                <div class="scrolling-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; padding: 1rem; background: var(--bg-primary); border-radius: 8px; border: 1px solid var(--border-color);">
                    <div style="color: var(--text-primary);">
                        <h3 style="margin: 0; font-size: 1.2rem;">Enhanced Scrolling Task</h3>
                        <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; color: var(--text-secondary);">Navigate through 50 scroll points - current point in blue, next in yellow</p>
                    </div>
                    <div class="scrolling-progress" style="text-align: right; color: var(--text-primary);">
                        Point: <span id="current-scroll-point">1</span> / 50
                    </div>
                </div>

                <div class="scrolling-main" style="display: flex; gap: 1rem;">
                    <div class="scrolling-area-wrapper" style="flex: 1; position: relative;">
                        <div id="scrolling-area" class="scrolling-area" style="width: 100%; height: 400px; overflow-y: auto; border: 2px solid var(--border-color); border-radius: 8px; background: var(--bg-secondary); position: relative;">
                            <div id="scrolling-content" class="scrolling-content" style="padding: 1rem; position: relative;">
                                <!-- Content will be generated dynamically -->
                            </div>
                            <div id="scroll-points-overlay" class="scroll-points-overlay" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 10;">
                                <!-- Scroll points will be positioned here -->
                            </div>
                        </div>
                    </div>

                    <div class="custom-scrollbar-container" style="width: 60px; height: 400px; position: relative; background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: 8px;">
                        <div id="custom-scrollbar" class="custom-scrollbar" style="width: 100%; height: 100%; position: relative; padding: 5px;">
                            <div id="scrollbar-track" class="scrollbar-track" style="width: 100%; height: 100%; background: var(--bg-secondary); border-radius: 4px; position: relative;">
                                <div id="scrollbar-thumb" class="scrollbar-thumb" style="width: 100%; background: var(--accent-primary); border-radius: 4px; position: absolute; top: 0; cursor: pointer; min-height: 20px;"></div>
                                <div id="scrollbar-points" class="scrollbar-points" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                                    <!-- Point indicators will be added here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupScrollingTask();
    }

    setupScrollingTask() {
        const scrollArea = document.getElementById('scrolling-area');
        const scrollContent = document.getElementById('scrolling-content');
        const scrollPointsOverlay = document.getElementById('scroll-points-overlay');
        const customScrollbar = document.getElementById('custom-scrollbar');
        const scrollbarThumb = document.getElementById('scrollbar-thumb');
        const scrollbarPoints = document.getElementById('scrollbar-points');

        if (!scrollArea || !scrollContent) return;

        let currentPointIndex = 0;
        const totalPoints = 50;
        const scrollPoints = [];
        const self = this;

        // Enhanced data capture
        let scrollData = {
            points: [],
            scrollEvents: [],
            viewportData: [],
            behaviorPatterns: [],
            distances: [],
            speeds: [],
            startTime: Date.now()
        };

        // Generate extensive content for scrolling
        const contentHeight = 3000; // Total content height
        const content = this.generateScrollContent(contentHeight);
        scrollContent.innerHTML = content;

        // Generate 50 scroll points at different vertical positions
        for (let i = 0; i < totalPoints; i++) {
            const scrollPosition = (i / (totalPoints - 1)) * (contentHeight - 400); // Distribute across scrollable area
            scrollPoints.push({
                id: i + 1,
                scrollPosition: scrollPosition,
                yPosition: scrollPosition + 200, // Position within content
                completed: false,
                viewportPercentage: 0,
                timeReached: null,
                scrollSpeed: 0,
                distanceFromPrevious: i > 0 ? scrollPosition - scrollPoints[i-1].scrollPosition : 0
            });
        }

        // Store distances between points
        scrollData.distances = scrollPoints.map((point, index) => ({
            pointIndex: index,
            distanceFromPrevious: point.distanceFromPrevious,
            distanceToNext: index < scrollPoints.length - 1 ? scrollPoints[index + 1].scrollPosition - point.scrollPosition : 0
        }));

        this.updateScrollPoints();
        this.setupCustomScrollbar();
        this.setupScrollEventListeners();
    }

    generateScrollContent(totalHeight) {
        const sections = [
            "Technology and Innovation",
            "Environmental Sustainability",
            "Health and Wellness",
            "Education and Learning",
            "Arts and Culture",
            "Science and Research",
            "Business and Economics",
            "Travel and Adventure",
            "Food and Nutrition",
            "Sports and Fitness"
        ];

        let content = '';
        const sectionHeight = totalHeight / sections.length;

        sections.forEach((section, index) => {
            content += `
                <div class="scroll-section" style="min-height: ${sectionHeight}px; margin-bottom: 2rem; padding: 2rem; background: ${index % 2 === 0 ? 'var(--bg-primary)' : 'rgba(var(--accent-primary-rgb), 0.05)'}; border-radius: 8px; border-left: 4px solid var(--accent-primary);">
                    <h2 style="color: var(--accent-primary); margin-bottom: 1rem; font-size: 1.5rem;">${section}</h2>
                    <p style="color: var(--text-primary); line-height: 1.6; margin-bottom: 1rem;">
                        This section explores various aspects of ${section.toLowerCase()}. The content is designed to provide comprehensive coverage of the topic while creating natural scrolling patterns for interaction analysis.
                    </p>
                    <p style="color: var(--text-secondary); line-height: 1.6; margin-bottom: 1rem;">
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                    </p>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 1rem 0;">
                        <div style="padding: 1rem; background: var(--bg-secondary); border-radius: 4px; border: 1px solid var(--border-color);">
                            <h4 style="color: var(--text-primary); margin-bottom: 0.5rem;">Key Point 1</h4>
                            <p style="color: var(--text-secondary); font-size: 0.9rem;">Detailed explanation of the first key concept in this section.</p>
                        </div>
                        <div style="padding: 1rem; background: var(--bg-secondary); border-radius: 4px; border: 1px solid var(--border-color);">
                            <h4 style="color: var(--text-primary); margin-bottom: 0.5rem;">Key Point 2</h4>
                            <p style="color: var(--text-secondary); font-size: 0.9rem;">Detailed explanation of the second key concept in this section.</p>
                        </div>
                    </div>
                    <p style="color: var(--text-primary); line-height: 1.6;">
                        Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                    </p>
                </div>
            `;
        });

        return content;
    }

    updateScrollPoints() {
        const scrollPointsOverlay = document.getElementById('scroll-points-overlay');
        const scrollArea = document.getElementById('scrolling-area');
        if (!scrollPointsOverlay || !scrollArea) return;

        // Clear existing points
        scrollPointsOverlay.innerHTML = '';

        const currentPoint = this.scrollPoints[this.currentPointIndex];
        const nextPoint = this.currentPointIndex < this.scrollPoints.length - 1 ? this.scrollPoints[this.currentPointIndex + 1] : null;

        // Create current point indicator
        if (currentPoint && !currentPoint.completed) {
            const currentPointElement = this.createScrollPoint(currentPoint, false);
            scrollPointsOverlay.appendChild(currentPointElement);
        }

        // Create next point indicator
        if (nextPoint && !nextPoint.completed) {
            const nextPointElement = this.createScrollPoint(nextPoint, true);
            scrollPointsOverlay.appendChild(nextPointElement);
        }

        // Update progress counter
        const progressElement = document.getElementById('current-scroll-point');
        if (progressElement) {
            progressElement.textContent = this.currentPointIndex + 1;
        }
    }

    createScrollPoint(point, isNext = false) {
        const pointElement = document.createElement('div');
        pointElement.className = isNext ? 'scroll-point next-point' : 'scroll-point current-point';
        pointElement.style.cssText = `
            position: absolute;
            left: 20px;
            top: ${point.yPosition}px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: ${isNext ? 'rgba(255, 165, 0, 0.8)' : 'var(--accent-primary)'};
            border: 3px solid ${isNext ? '#FFA500' : 'white'};
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 20;
            animation: ${isNext ? 'none' : 'scrollPointAppear 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)'};
            transition: all 0.3s ease;
        `;
        pointElement.textContent = point.id;

        return pointElement;
    }

    generateFormTask(container) {
        container.innerHTML = `
            <div class="form-area" style="width: 100%; max-width: 600px;">
                <form id="interaction-form" style="background: var(--bg-primary); padding: 2rem; border-radius: 8px; border: 1px solid var(--border-color);">
                    <h3 style="margin-bottom: 1.5rem; color: var(--text-primary);">Sample Form</h3>

                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-primary);">Full Name:</label>
                        <input type="text" placeholder="Enter your full name" style="width: 100%; padding: 0.75rem; border: 2px solid var(--border-color); border-radius: 4px; background: var(--bg-secondary); color: var(--text-primary);">
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-primary);">Email:</label>
                        <input type="email" placeholder="<EMAIL>" style="width: 100%; padding: 0.75rem; border: 2px solid var(--border-color); border-radius: 4px; background: var(--bg-secondary); color: var(--text-primary);">
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-primary);">Age:</label>
                        <select style="width: 100%; padding: 0.75rem; border: 2px solid var(--border-color); border-radius: 4px; background: var(--bg-secondary); color: var(--text-primary);">
                            <option value="">Select your age range</option>
                            <option value="18-25">18-25</option>
                            <option value="26-35">26-35</option>
                            <option value="36-45">36-45</option>
                            <option value="46-55">46-55</option>
                            <option value="55+">55+</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 1rem;">
                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-primary);">Interests:</label>
                        <div style="display: flex; flex-wrap: wrap; gap: 1rem;">
                            <label style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-primary);">
                                <input type="checkbox" value="technology"> Technology
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-primary);">
                                <input type="checkbox" value="sports"> Sports
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-primary);">
                                <input type="checkbox" value="music"> Music
                            </label>
                            <label style="display: flex; align-items: center; gap: 0.5rem; color: var(--text-primary);">
                                <input type="checkbox" value="travel"> Travel
                            </label>
                        </div>
                    </div>

                    <div style="margin-bottom: 1.5rem;">
                        <label style="display: block; margin-bottom: 0.5rem; color: var(--text-primary);">Comments:</label>
                        <textarea placeholder="Any additional comments..." style="width: 100%; height: 100px; padding: 0.75rem; border: 2px solid var(--border-color); border-radius: 4px; background: var(--bg-secondary); color: var(--text-primary); resize: vertical;"></textarea>
                    </div>

                    <button type="submit" style="background: var(--accent-primary); color: white; padding: 0.75rem 2rem; border: none; border-radius: 4px; cursor: pointer; font-size: 1rem;">Submit Form</button>
                </form>
            </div>
        `;

        // Add form submission handler
        document.getElementById('interaction-form').addEventListener('submit', (e) => {
            e.preventDefault();
            alert('Form submitted successfully!');
        });
    }

    generateNavigationTask(container) {
        container.innerHTML = `
            <div class="navigation-area" style="width: 100%; max-width: 700px;">
                <nav style="background: var(--bg-primary); border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden;">
                    <div class="nav-header" style="background: var(--accent-primary); color: white; padding: 1rem; font-weight: bold;">
                        Navigation Menu
                    </div>
                    <ul style="list-style: none; margin: 0; padding: 0;">
                        <li class="nav-item" style="border-bottom: 1px solid var(--border-color);">
                            <a href="#" style="display: block; padding: 1rem; color: var(--text-primary); text-decoration: none; transition: background 0.2s;">Home</a>
                        </li>
                        <li class="nav-item dropdown" style="border-bottom: 1px solid var(--border-color); position: relative;">
                            <a href="#" class="dropdown-toggle" style="display: block; padding: 1rem; color: var(--text-primary); text-decoration: none; transition: background 0.2s;">
                                Products <span style="float: right;">▼</span>
                            </a>
                            <ul class="dropdown-menu" style="display: none; position: absolute; top: 100%; left: 0; right: 0; background: var(--bg-secondary); border: 1px solid var(--border-color); z-index: 10;">
                                <li><a href="#" style="display: block; padding: 0.75rem 1rem; color: var(--text-primary); text-decoration: none;">Software</a></li>
                                <li><a href="#" style="display: block; padding: 0.75rem 1rem; color: var(--text-primary); text-decoration: none;">Hardware</a></li>
                                <li><a href="#" style="display: block; padding: 0.75rem 1rem; color: var(--text-primary); text-decoration: none;">Services</a></li>
                            </ul>
                        </li>
                        <li class="nav-item" style="border-bottom: 1px solid var(--border-color);">
                            <a href="#" style="display: block; padding: 1rem; color: var(--text-primary); text-decoration: none; transition: background 0.2s;">About</a>
                        </li>
                        <li class="nav-item dropdown" style="border-bottom: 1px solid var(--border-color); position: relative;">
                            <a href="#" class="dropdown-toggle" style="display: block; padding: 1rem; color: var(--text-primary); text-decoration: none; transition: background 0.2s;">
                                Support <span style="float: right;">▼</span>
                            </a>
                            <ul class="dropdown-menu" style="display: none; position: absolute; top: 100%; left: 0; right: 0; background: var(--bg-secondary); border: 1px solid var(--border-color); z-index: 10;">
                                <li><a href="#" style="display: block; padding: 0.75rem 1rem; color: var(--text-primary); text-decoration: none;">Documentation</a></li>
                                <li><a href="#" style="display: block; padding: 0.75rem 1rem; color: var(--text-primary); text-decoration: none;">Contact Us</a></li>
                                <li><a href="#" style="display: block; padding: 0.75rem 1rem; color: var(--text-primary); text-decoration: none;">FAQ</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a href="#" style="display: block; padding: 1rem; color: var(--text-primary); text-decoration: none; transition: background 0.2s;">Contact</a>
                        </li>
                    </ul>
                </nav>
                <div style="margin-top: 2rem; padding: 1rem; background: var(--bg-primary); border-radius: 8px; border: 1px solid var(--border-color);">
                    <p style="color: var(--text-secondary); margin: 0;">Click on menu items and explore the dropdown menus. Try hovering and clicking different options.</p>
                </div>
            </div>
        `;

        this.setupNavigationTask();
    }

    setupNavigationTask() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        const navItems = document.querySelectorAll('.nav-item a');

        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.preventDefault();
                const dropdown = toggle.nextElementSibling;
                const isVisible = dropdown.style.display === 'block';

                // Close all dropdowns
                document.querySelectorAll('.dropdown-menu').forEach(menu => {
                    menu.style.display = 'none';
                });

                // Toggle current dropdown
                dropdown.style.display = isVisible ? 'none' : 'block';
            });
        });

        navItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                item.style.background = 'var(--accent-primary-light)';
            });

            item.addEventListener('mouseleave', () => {
                item.style.background = 'transparent';
            });

            item.addEventListener('click', (e) => {
                e.preventDefault();
                // Visual feedback for clicks
                item.style.background = 'var(--accent-primary)';
                setTimeout(() => {
                    item.style.background = 'transparent';
                }, 200);
            });
        });
    }

    generateFreeFormTask(container) {
        container.innerHTML = `
            <div class="free-form-area" style="width: 100%; max-width: 800px;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
                    <div class="interactive-panel" style="background: var(--bg-primary); padding: 1.5rem; border-radius: 8px; border: 1px solid var(--border-color);">
                        <h4 style="margin-bottom: 1rem; color: var(--text-primary);">Interactive Elements</h4>
                        <button style="background: var(--accent-primary); color: white; padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: pointer; margin: 0.25rem;">Button 1</button>
                        <button style="background: var(--accent-secondary); color: white; padding: 0.5rem 1rem; border: none; border-radius: 4px; cursor: pointer; margin: 0.25rem;">Button 2</button>
                        <div style="margin: 1rem 0;">
                            <input type="range" min="0" max="100" value="50" style="width: 100%;">
                        </div>
                        <div style="margin: 1rem 0;">
                            <input type="text" placeholder="Type something..." style="width: 100%; padding: 0.5rem; border: 1px solid var(--border-color); border-radius: 4px; background: var(--bg-secondary); color: var(--text-primary);">
                        </div>
                    </div>

                    <div class="content-panel" style="background: var(--bg-primary); padding: 1.5rem; border-radius: 8px; border: 1px solid var(--border-color); max-height: 200px; overflow-y: auto;">
                        <h4 style="margin-bottom: 1rem; color: var(--text-primary);">Scrollable Content</h4>
                        <p style="margin-bottom: 1rem; color: var(--text-secondary);">This is a scrollable content area. You can scroll through this content and interact with various elements.</p>
                        <p style="margin-bottom: 1rem; color: var(--text-secondary);">Try clicking, scrolling, and interacting naturally with all the elements on this page.</p>
                        <p style="margin-bottom: 1rem; color: var(--text-secondary);">This free-form interaction captures your natural browsing patterns and behaviors.</p>
                        <p style="margin-bottom: 1rem; color: var(--text-secondary);">Feel free to explore and interact with anything you see.</p>
                    </div>
                </div>

                <div class="action-area" style="background: var(--bg-primary); padding: 1.5rem; border-radius: 8px; border: 1px solid var(--border-color);">
                    <h4 style="margin-bottom: 1rem; color: var(--text-primary);">Free Interaction Zone</h4>
                    <p style="color: var(--text-secondary); margin-bottom: 1rem;">Interact with this area however you like - click, drag, type, scroll, or just move your mouse around.</p>
                    <div id="interaction-canvas" style="width: 100%; height: 150px; background: var(--bg-secondary); border: 2px dashed var(--border-color); border-radius: 8px; position: relative; cursor: crosshair;">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: var(--text-secondary); text-align: center;">
                            <i class="fas fa-hand-pointer" style="font-size: 2rem; margin-bottom: 0.5rem; display: block;"></i>
                            Click and interact freely
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.setupFreeFormTask();
    }

    setupFreeFormTask() {
        const canvas = document.getElementById('interaction-canvas');
        const buttons = document.querySelectorAll('.interactive-panel button');

        // Add click effects to canvas
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                left: ${x}px;
                top: ${y}px;
                width: 20px;
                height: 20px;
                background: var(--accent-primary);
                border-radius: 50%;
                transform: translate(-50%, -50%);
                animation: ripple 1s ease-out forwards;
                pointer-events: none;
            `;

            canvas.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 1000);
        });

        // Add button interactions
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // Add CSS animation for ripple effect
        if (!document.getElementById('ripple-style')) {
            const style = document.createElement('style');
            style.id = 'ripple-style';
            style.textContent = `
                @keyframes ripple {
                    0% {
                        transform: translate(-50%, -50%) scale(0);
                        opacity: 1;
                    }
                    100% {
                        transform: translate(-50%, -50%) scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    completeTask() {
        if (this.currentTask < this.totalTasks - 1) {
            this.startTask(this.currentTask + 1);
        } else {
            this.completeSession();
        }
    }

    skipTask() {
        this.completeTask();
    }

    async completeSession() {
        try {
            // Save any remaining data
            if (this.dataBuffer.length > 0) {
                await this.saveDataBatch();
            }

            // Mark session as complete
            await fetch(`/api/sessions/${this.sessionId}/complete`, {
                method: 'POST'
            });

            this.stop();
            this.showCompletionInterface();
            
            console.log('Recording session completed');
        } catch (error) {
            console.error('Failed to complete session:', error);
        }
    }

    stop() {
        this.isRecording = false;
        
        // Clear intervals
        if (this.captureInterval) {
            clearInterval(this.captureInterval);
            this.captureInterval = null;
        }
        
        if (this.saveInterval) {
            clearInterval(this.saveInterval);
            this.saveInterval = null;
        }
        
        if (this.sessionTimer) {
            clearInterval(this.sessionTimer);
            this.sessionTimer = null;
        }
    }

    pause() {
        this.isPaused = !this.isPaused;
        const button = document.getElementById('pause-recording');
        const icon = button.querySelector('i');
        
        if (this.isPaused) {
            icon.className = 'fas fa-play';
            button.innerHTML = '<i class="fas fa-play"></i> Resume';
        } else {
            icon.className = 'fas fa-pause';
            button.innerHTML = '<i class="fas fa-pause"></i> Pause';
        }
    }

    showCompletionInterface() {
        document.getElementById('recording-active').classList.add('hidden');
        document.getElementById('recording-complete').classList.remove('hidden');
        
        // Update completion stats
        const duration = performance.now() - this.startTime;
        document.getElementById('final-duration').textContent = this.formatDuration(duration);
        document.getElementById('final-data-points').textContent = this.dataBuffer.length.toLocaleString();
        document.getElementById('final-tasks').textContent = `${this.currentTask + 1}/${this.totalTasks}`;
    }

    formatDuration(ms) {
        const minutes = Math.floor(ms / 60000);
        const seconds = Math.floor((ms % 60000) / 1000);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    handleBeforeUnload(event) {
        if (this.isRecording) {
            event.preventDefault();
            event.returnValue = 'Recording in progress. Are you sure you want to leave?';
            return event.returnValue;
        }
    }
}

// Initialize recorder
window.recorder = new InteractionRecorder();
console.log('Interaction Recorder initialized');
