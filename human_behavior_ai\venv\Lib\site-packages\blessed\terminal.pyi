"""Type hints for :class:`Terminal`, the primary API entry point."""

# std imports
from typing import IO, Any, List, Tuple, Union, Optional, OrderedDict, SupportsIndex, ContextManager

# local
from .keyboard import Keystroke
from .sequences import Termcap
from .formatters import (FormattingString,
                         NullCallableString,
                         ParameterizingString,
                         FormattingOtherString)

HAS_TTY: bool

# pylint: disable=unused-argument,missing-function-docstring,missing-class-docstring
# pylint: disable=too-many-public-methods,too-few-public-methods

class Terminal:
    caps: OrderedDict[str, Termcap]
    errors: List[str] = ...
    def __init__(
        self,
        kind: Optional[str] = ...,
        stream: Optional[IO[str]] = ...,
        force_styling: bool = ...,
    ) -> None: ...
    def __getattr__(
        self, attr: str
    ) -> Union[NullCallableString, ParameterizingString, FormattingString]: ...
    @property
    def kind(self) -> str: ...
    @property
    def does_styling(self) -> bool: ...
    @property
    def is_a_tty(self) -> bool: ...
    @property
    def height(self) -> int: ...
    @property
    def width(self) -> int: ...
    @property
    def pixel_height(self) -> int: ...
    @property
    def pixel_width(self) -> int: ...
    def location(
        self, x: Optional[int] = ..., y: Optional[int] = ...
    ) -> ContextManager[None]: ...
    def get_location(self, timeout: Optional[float] = ...) -> Tuple[int, int]: ...
    def get_fgcolor(self, timeout: Optional[float] = ...) -> Tuple[int, int, int]: ...
    def get_bgcolor(self, timeout: Optional[float] = ...) -> Tuple[int, int, int]: ...
    def fullscreen(self) -> ContextManager[None]: ...
    def hidden_cursor(self) -> ContextManager[None]: ...
    def move_xy(self, x: int, y: int) -> ParameterizingString: ...
    def move_yx(self, y: int, x: int) -> ParameterizingString: ...
    @property
    def move_left(self) -> FormattingOtherString: ...
    @property
    def move_right(self) -> FormattingOtherString: ...
    @property
    def move_up(self) -> FormattingOtherString: ...
    @property
    def move_down(self) -> FormattingOtherString: ...
    @property
    def color(self) -> Union[NullCallableString, ParameterizingString]: ...
    def color_rgb(self, red: int, green: int, blue: int) -> FormattingString: ...
    @property
    def on_color(self) -> Union[NullCallableString, ParameterizingString]: ...
    def on_color_rgb(self, red: int, green: int, blue: int) -> FormattingString: ...
    def formatter(self, value: str) -> Union[NullCallableString, FormattingString]: ...
    def rgb_downconvert(self, red: int, green: int, blue: int) -> int: ...
    @property
    def normal(self) -> str: ...
    def link(self, url: str, text: str, url_id: str = ...) -> str: ...
    @property
    def stream(self) -> IO[str]: ...
    @property
    def number_of_colors(self) -> int: ...
    @number_of_colors.setter
    def number_of_colors(self, value: int) -> None: ...
    @property
    def color_distance_algorithm(self) -> str: ...
    @color_distance_algorithm.setter
    def color_distance_algorithm(self, value: str) -> None: ...
    def ljust(
        self, text: str, width: Optional[SupportsIndex] = ..., fillchar: str = ...
    ) -> str: ...
    def rjust(
        self, text: str, width: Optional[SupportsIndex] = ..., fillchar: str = ...
    ) -> str: ...
    def center(
        self, text: str, width: Optional[SupportsIndex] = ..., fillchar: str = ...
    ) -> str: ...
    def truncate(self, text: str, width: Optional[SupportsIndex] = ...) -> str: ...
    def length(self, text: str) -> int: ...
    def strip(self, text: str, chars: Optional[str] = ...) -> str: ...
    def rstrip(self, text: str, chars: Optional[str] = ...) -> str: ...
    def lstrip(self, text: str, chars: Optional[str] = ...) -> str: ...
    def strip_seqs(self, text: str) -> str: ...
    def split_seqs(self, text: str, maxsplit: int) -> List[str]: ...
    def wrap(
        self, text: str, width: Optional[int] = ..., **kwargs: Any
    ) -> List[str]: ...
    def getch(self) -> str: ...
    def ungetch(self, text: str) -> None: ...
    def kbhit(self, timeout: Optional[float] = ...) -> bool: ...
    def cbreak(self) -> ContextManager[None]: ...
    def raw(self) -> ContextManager[None]: ...
    def keypad(self) -> ContextManager[None]: ...
    def inkey(
        self, timeout: Optional[float] = ..., esc_delay: float = ...
    ) -> Keystroke: ...

class WINSZ:
    ...
