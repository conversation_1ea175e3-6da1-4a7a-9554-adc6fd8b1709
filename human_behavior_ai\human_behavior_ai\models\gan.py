"""
Generative Adversarial Network models for realistic human behavior synthesis.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tu<PERSON>, Optional, List
import math

from .base import BaseModel, ModelConfig, get_activation_fn


class GenerativeAdversarialNetwork(nn.Module):
    """GAN for generating realistic human behavior patterns."""
    
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config
        
        # GAN-specific configuration
        self.noise_dim = config.model_specific.get('noise_dim', 128)
        self.generator_lr = config.model_specific.get('generator_lr', 2e-4)
        self.discriminator_lr = config.model_specific.get('discriminator_lr', 2e-4)
        self.gan_loss_type = config.model_specific.get('gan_loss_type', 'wgan-gp')
        self.gradient_penalty_weight = config.model_specific.get('gradient_penalty_weight', 10.0)
        
        # Generator and Discriminator
        self.generator = BehaviorGenerator(
            noise_dim=self.noise_dim,
            hidden_dim=config.hidden_dim,
            output_dim=config.output_dim,
            num_layers=config.num_layers,
            dropout=config.dropout,
            activation=config.activation,
            sequence_length=config.sequence_length
        )
        
        self.discriminator = BehaviorDiscriminator(
            input_dim=config.input_dim,
            hidden_dim=config.hidden_dim,
            num_layers=config.num_layers // 2,
            dropout=config.dropout,
            activation=config.activation,
            sequence_length=config.sequence_length
        )
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, 0.0, 0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Conv1d):
            torch.nn.init.normal_(module.weight, 0.0, 0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def generate_noise(self, batch_size: int, device: torch.device) -> torch.Tensor:
        """Generate random noise for generator input."""
        return torch.randn(batch_size, self.noise_dim, device=device)
    
    def forward_generator(self, noise: torch.Tensor, 
                         context: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass through generator."""
        return self.generator(noise, context)
    
    def forward_discriminator(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through discriminator."""
        return self.discriminator(x)
    
    def compute_generator_loss(self, fake_data: torch.Tensor) -> torch.Tensor:
        """Compute generator loss."""
        fake_scores = self.discriminator(fake_data)
        
        if self.gan_loss_type == 'wgan-gp':
            # WGAN-GP generator loss
            return -torch.mean(fake_scores)
        elif self.gan_loss_type == 'lsgan':
            # LSGAN generator loss
            return 0.5 * torch.mean((fake_scores - 1) ** 2)
        else:  # Standard GAN
            return F.binary_cross_entropy_with_logits(
                fake_scores, torch.ones_like(fake_scores)
            )
    
    def compute_discriminator_loss(self, real_data: torch.Tensor, 
                                 fake_data: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Compute discriminator loss."""
        real_scores = self.discriminator(real_data)
        fake_scores = self.discriminator(fake_data.detach())
        
        if self.gan_loss_type == 'wgan-gp':
            # WGAN-GP discriminator loss
            d_loss = torch.mean(fake_scores) - torch.mean(real_scores)
            
            # Gradient penalty
            gp = self.gradient_penalty(real_data, fake_data)
            d_loss += self.gradient_penalty_weight * gp
            
            return {
                'discriminator_loss': d_loss,
                'gradient_penalty': gp,
                'real_score': torch.mean(real_scores),
                'fake_score': torch.mean(fake_scores),
            }
        elif self.gan_loss_type == 'lsgan':
            # LSGAN discriminator loss
            real_loss = 0.5 * torch.mean((real_scores - 1) ** 2)
            fake_loss = 0.5 * torch.mean(fake_scores ** 2)
            d_loss = real_loss + fake_loss
            
            return {
                'discriminator_loss': d_loss,
                'real_loss': real_loss,
                'fake_loss': fake_loss,
            }
        else:  # Standard GAN
            real_loss = F.binary_cross_entropy_with_logits(
                real_scores, torch.ones_like(real_scores)
            )
            fake_loss = F.binary_cross_entropy_with_logits(
                fake_scores, torch.zeros_like(fake_scores)
            )
            d_loss = real_loss + fake_loss
            
            return {
                'discriminator_loss': d_loss,
                'real_loss': real_loss,
                'fake_loss': fake_loss,
            }
    
    def gradient_penalty(self, real_data: torch.Tensor, fake_data: torch.Tensor) -> torch.Tensor:
        """Compute gradient penalty for WGAN-GP."""
        batch_size = real_data.shape[0]
        device = real_data.device
        
        # Random interpolation factor
        alpha = torch.rand(batch_size, 1, 1, device=device)
        alpha = alpha.expand_as(real_data)
        
        # Interpolated samples
        interpolated = alpha * real_data + (1 - alpha) * fake_data
        interpolated.requires_grad_(True)
        
        # Discriminator output for interpolated samples
        d_interpolated = self.discriminator(interpolated)
        
        # Compute gradients
        gradients = torch.autograd.grad(
            outputs=d_interpolated,
            inputs=interpolated,
            grad_outputs=torch.ones_like(d_interpolated),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]
        
        # Gradient penalty
        gradients = gradients.view(batch_size, -1)
        gradient_norm = gradients.norm(2, dim=1)
        penalty = torch.mean((gradient_norm - 1) ** 2)
        
        return penalty


class BehaviorGenerator(nn.Module):
    """Generator network for creating realistic behavior sequences."""
    
    def __init__(self, noise_dim: int, hidden_dim: int, output_dim: int,
                 num_layers: int, dropout: float, activation: str, sequence_length: int):
        super().__init__()
        
        self.noise_dim = noise_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.sequence_length = sequence_length
        
        # Initial projection from noise
        self.noise_projection = nn.Linear(noise_dim, hidden_dim)
        
        # Context conditioning (optional)
        self.context_projection = nn.Linear(24, hidden_dim)  # Assuming context_dim = 24
        
        # Transformer-based generator
        self.transformer_layers = nn.ModuleList([
            GeneratorTransformerBlock(
                d_model=hidden_dim,
                num_heads=8,
                d_ff=hidden_dim * 4,
                dropout=dropout,
                activation=activation
            )
            for _ in range(num_layers)
        ])
        
        # Output projection layers for each modality
        self.mouse_projection = nn.Linear(hidden_dim, 16)  # mouse_dim
        self.keyboard_projection = nn.Linear(hidden_dim, 16)  # keyboard_dim
        self.scroll_projection = nn.Linear(hidden_dim, 8)   # scroll_dim
        self.context_projection_out = nn.Linear(hidden_dim, 24)  # context_dim
        
        # Positional encoding
        self.positional_encoding = PositionalEncoding(hidden_dim, sequence_length * 4)
        
        # Layer normalization
        self.layer_norm = nn.LayerNorm(hidden_dim)
    
    def forward(self, noise: torch.Tensor, context: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Generate behavior sequence from noise."""
        batch_size = noise.shape[0]
        device = noise.device
        
        # Project noise to hidden dimension
        x = self.noise_projection(noise)  # [B, H]
        
        # Add context if provided
        if context is not None:
            context_emb = self.context_projection(context.mean(dim=1))  # [B, H]
            x = x + context_emb
        
        # Expand to sequence length
        x = x.unsqueeze(1).expand(-1, self.sequence_length * 4, -1)  # [B, T*4, H]
        
        # Add positional encoding
        x = self.positional_encoding(x)
        
        # Pass through transformer layers
        for layer in self.transformer_layers:
            x = layer(x)
        
        # Layer normalization
        x = self.layer_norm(x)
        
        # Reshape to separate modalities
        x = x.view(batch_size, self.sequence_length, 4, self.hidden_dim)
        
        # Generate outputs for each modality
        mouse_output = torch.tanh(self.mouse_projection(x[:, :, 0, :]))
        keyboard_output = torch.sigmoid(self.keyboard_projection(x[:, :, 1, :]))
        scroll_output = torch.tanh(self.scroll_projection(x[:, :, 2, :]))
        context_output = torch.sigmoid(self.context_projection_out(x[:, :, 3, :]))
        
        # Combine outputs
        output = torch.cat([
            mouse_output,
            keyboard_output, 
            scroll_output,
            context_output
        ], dim=-1)  # [B, T, total_dim]
        
        return output


class BehaviorDiscriminator(nn.Module):
    """Discriminator network for distinguishing real vs fake behavior."""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int,
                 dropout: float, activation: str, sequence_length: int):
        super().__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.sequence_length = sequence_length
        
        # Input projection
        self.input_projection = nn.Linear(input_dim, hidden_dim)
        
        # Convolutional layers for temporal pattern recognition
        self.conv_layers = nn.ModuleList([
            nn.Conv1d(hidden_dim, hidden_dim, kernel_size=3, padding=1)
            for _ in range(num_layers)
        ])
        
        # Self-attention for global dependencies
        self.self_attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # Classification layers
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            get_activation_fn(activation),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            get_activation_fn(activation),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, 1)
        )
        
        # Spectral normalization for training stability
        self.apply_spectral_norm()
    
    def apply_spectral_norm(self):
        """Apply spectral normalization to linear layers."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.utils.spectral_norm(module)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Discriminate between real and fake behavior sequences."""
        batch_size, seq_len, input_dim = x.shape
        
        # Input projection
        x = self.input_projection(x)  # [B, T, H]
        
        # Convolutional processing
        x_conv = x.transpose(1, 2)  # [B, H, T]
        for conv_layer in self.conv_layers:
            residual = x_conv
            x_conv = F.leaky_relu(conv_layer(x_conv), 0.2)
            x_conv = x_conv + residual  # Residual connection
        
        x = x_conv.transpose(1, 2)  # [B, T, H]
        
        # Self-attention
        attn_output, _ = self.self_attention(x, x, x)
        x = x + attn_output  # Residual connection
        
        # Global average pooling
        x = x.mean(dim=1)  # [B, H]
        
        # Classification
        output = self.classifier(x)  # [B, 1]
        
        return output


class GeneratorTransformerBlock(nn.Module):
    """Transformer block for generator."""
    
    def __init__(self, d_model: int, num_heads: int, d_ff: int, 
                 dropout: float, activation: str):
        super().__init__()
        
        self.self_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=num_heads,
            dropout=dropout,
            batch_first=True
        )
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            get_activation_fn(activation),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model),
            nn.Dropout(dropout)
        )
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through generator transformer block."""
        # Self-attention with residual connection
        attn_output, _ = self.self_attention(x, x, x)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Feed-forward with residual connection
        ff_output = self.feed_forward(x)
        x = self.norm2(x + ff_output)
        
        return x


class PositionalEncoding(nn.Module):
    """Positional encoding for transformer models."""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Add positional encoding to input."""
        return x + self.pe[:x.size(1), :].transpose(0, 1)


class BehaviorGAN(GenerativeAdversarialNetwork):
    """Specialized GAN for human behavior modeling with additional features."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        
        # Additional behavior-specific components
        self.feature_matching_weight = config.model_specific.get('feature_matching_weight', 1.0)
        self.diversity_weight = config.model_specific.get('diversity_weight', 0.1)
        
        # Feature extractor for feature matching loss
        self.feature_extractor = FeatureExtractor(
            input_dim=config.input_dim,
            hidden_dim=config.hidden_dim,
            num_layers=3
        )
    
    def compute_feature_matching_loss(self, real_data: torch.Tensor, 
                                    fake_data: torch.Tensor) -> torch.Tensor:
        """Compute feature matching loss for improved training stability."""
        real_features = self.feature_extractor(real_data)
        fake_features = self.feature_extractor(fake_data)
        
        # L2 loss between feature statistics
        loss = 0
        for real_feat, fake_feat in zip(real_features, fake_features):
            loss += F.mse_loss(fake_feat.mean(0), real_feat.mean(0))
            loss += F.mse_loss(fake_feat.var(0), real_feat.var(0))
        
        return loss
    
    def compute_diversity_loss(self, fake_data: torch.Tensor) -> torch.Tensor:
        """Compute diversity loss to encourage varied outputs."""
        batch_size = fake_data.shape[0]
        
        # Compute pairwise distances
        fake_flat = fake_data.view(batch_size, -1)
        distances = torch.cdist(fake_flat, fake_flat, p=2)
        
        # Encourage larger distances (diversity)
        diversity_loss = -torch.mean(distances)
        
        return diversity_loss


class FeatureExtractor(nn.Module):
    """Feature extractor for feature matching loss."""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int):
        super().__init__()
        
        self.layers = nn.ModuleList()
        current_dim = input_dim
        
        for i in range(num_layers):
            self.layers.append(nn.Sequential(
                nn.Linear(current_dim, hidden_dim),
                nn.LeakyReLU(0.2),
                nn.Dropout(0.1)
            ))
            current_dim = hidden_dim
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """Extract features at multiple layers."""
        features = []
        
        # Flatten sequence dimension
        batch_size, seq_len, input_dim = x.shape
        x = x.view(batch_size * seq_len, input_dim)
        
        for layer in self.layers:
            x = layer(x)
            features.append(x)
        
        return features
